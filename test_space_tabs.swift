#!/usr/bin/env swift

import Foundation

// Simple test to verify Space tab components compile and work
print("🧪 Testing Space Tab Components...")

// Test 1: Verify SpaceInfo creation
let testSpace = SpaceInfo(id: 1, name: "Test Space", isActive: true)
print("✅ SpaceInfo creation: \(testSpace.displayName)")

// Test 2: Verify SpaceDetector singleton
let detector = SpaceDetector.shared
print("✅ SpaceDetector singleton created")

// Test 3: Run integration tests
let tester = SpaceTabViewIntegrationTests()
let result = tester.runAllTests()

print("\n🎯 Final Result: \(result ? "All tests passed!" : "Some tests failed")")