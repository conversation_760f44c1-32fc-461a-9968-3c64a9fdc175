#!/bin/bash

echo "測試 Hammerspoon 通信..."

# 檢查 Hammerspoon 是否運行
if pgrep -x "Hammerspoon" > /dev/null; then
    echo "✅ Hammerspoon 正在運行"
else
    echo "❌ Hammerspoon 未運行"
    exit 1
fi

# 測試基本通信
echo "🔄 測試基本 URL scheme 通信..."
open "hammerspoon://runLua?code=hs.alert.show('Hello from test script')"

sleep 2

# 測試儲存功能
echo "🔄 測試儲存功能..."
open "hammerspoon://runLua?code=saveWindowLayout('TestProfile')"

sleep 3

# 檢查檔案是否被創建
if [ -f "$HOME/.hammerspoon/layouts/TestProfile.json" ]; then
    echo "✅ 測試檔案已創建"
    echo "檔案內容:"
    head -3 "$HOME/.hammerspoon/layouts/TestProfile.json"
    
    # 清理測試檔案
    rm "$HOME/.hammerspoon/layouts/TestProfile.json"
    echo "🧹 測試檔案已清理"
else
    echo "❌ 測試檔案未創建，可能存在通信問題"
fi

echo "測試完成！"