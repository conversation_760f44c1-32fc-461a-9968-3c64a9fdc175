-- Space-Specific 功能測試腳本
-- 用於驗證新的 space-specific 視窗管理功能

print("=== Space-Specific 功能測試開始 ===")

-- 測試 1: 獲取當前 space 信息
print("\n--- 測試 1: 獲取當前 space 信息 ---")
local currentSpaceID = hs.spaces.focusedSpace()
local currentScreen = hs.screen.mainScreen()
local screenUUID = currentScreen:getUUID()

print("當前 Space ID: " .. tostring(currentSpaceID))
print("當前螢幕 UUID: " .. tostring(screenUUID))

-- 測試 1.5: 比較所有視窗 vs 當前 space 視窗
print("\n--- 測試 1.5: 比較所有視窗 vs 當前 space 視窗 ---")
local allWindows = hs.window.allWindows()
local currentSpaceWindows = {}

print("所有視窗總數: " .. #allWindows)

for _, window in ipairs(allWindows) do
    if window and window:application() then
        local app = window:application()
        local appName = app:name()

        if appName ~= "Hammerspoon" then
            local windowSpaces = hs.spaces.windowSpaces(window)
            local isInCurrentSpace = false

            for _, spaceID in ipairs(windowSpaces) do
                if spaceID == currentSpaceID then
                    isInCurrentSpace = true
                    break
                end
            end

            if isInCurrentSpace then
                table.insert(currentSpaceWindows, {
                    app = appName,
                    title = window:title() or "無標題",
                    spaces = windowSpaces
                })
            end
        end
    end
end

print("當前 Space 的視窗數量: " .. #currentSpaceWindows)
for i, windowInfo in ipairs(currentSpaceWindows) do
    print("  " .. i .. ". " .. windowInfo.app .. " - " .. windowInfo.title .. " (spaces: " .. table.concat(windowInfo.spaces, ", ") .. ")")
end

-- 測試 2: 獲取當前 space 的視窗
print("\n--- 測試 2: 獲取當前 space 的視窗 ---")
local spaceWindowIDs = hs.spaces.windowsForSpace(currentSpaceID)
print("Space 中的視窗數量: " .. #spaceWindowIDs)

local visibleWindows = 0
local minimizedWindows = 0

for _, windowID in ipairs(spaceWindowIDs) do
    local window = hs.window.get(windowID)
    if window and window:application() then
        local app = window:application()
        local appName = app:name()
        
        if appName ~= "Hammerspoon" then
            local isMinimized = window:isMinimized()
            local title = window:title() or "無標題"
            
            print("  視窗: " .. appName .. " - " .. title .. " (最小化: " .. tostring(isMinimized) .. ")")
            
            if isMinimized then
                minimizedWindows = minimizedWindows + 1
            else
                visibleWindows = visibleWindows + 1
            end
        end
    end
end

print("可見視窗: " .. visibleWindows .. ", 最小化視窗: " .. minimizedWindows)

-- 測試 3: 測試保存功能
print("\n--- 測試 3: 測試保存功能 ---")
local testProfileName = "TestSpaceFeatures"
local saveResult = saveCurrentSpaceLayout(testProfileName)
print("保存結果: " .. tostring(saveResult))

-- 等待一下讓保存完成
hs.timer.doAfter(2, function()
    -- 測試 4: 檢查保存的檔案
    print("\n--- 測試 4: 檢查保存的檔案 ---")
    local layoutsPath = os.getenv("HOME") .. "/.hammerspoon/layouts/"
    local filePath = layoutsPath .. testProfileName .. "_space.json"
    
    local file = io.open(filePath, "r")
    if file then
        local content = file:read("*all")
        file:close()
        
        local success, layoutData = pcall(hs.json.decode, content)
        if success and layoutData then
            print("檔案讀取成功:")
            print("  版本: " .. tostring(layoutData.version))
            print("  類型: " .. tostring(layoutData.type))
            print("  Space ID: " .. tostring(layoutData.spaceID))
            print("  視窗數量: " .. #layoutData.windows)
            print("  應用程式數量: " .. #layoutData.runningApps)
            
            -- 檢查是否有最小化視窗記錄
            local minimizedCount = 0
            for _, windowData in ipairs(layoutData.windows) do
                if windowData.isMinimized then
                    minimizedCount = minimizedCount + 1
                end
            end
            print("  最小化視窗記錄: " .. minimizedCount)
            
        else
            print("❌ JSON 解析失敗")
        end
    else
        print("❌ 無法讀取檔案: " .. filePath)
    end
    
    -- 測試 5: 測試還原功能
    print("\n--- 測試 5: 測試還原功能 ---")
    hs.timer.doAfter(1, function()
        local restoreResult = restoreCurrentSpaceLayout(testProfileName)
        print("還原結果: " .. tostring(restoreResult))
        
        print("\n=== Space-Specific 功能測試完成 ===")
    end)
end)

-- 測試 6: 測試特殊應用數據獲取
print("\n--- 測試 6: 測試特殊應用數據獲取 ---")
local allWindows = hs.window.allWindows()
for _, window in ipairs(allWindows) do
    if window:application() then
        local app = window:application()
        local appName = app:name()
        
        if appName == "Notion" or appName == "Google Chrome" or appName == "Safari" then
            print("發現特殊應用: " .. appName)
            local specialData = getSpecialAppData(app, window)
            if specialData then
                print("  特殊數據: " .. hs.inspect(specialData))
            else
                print("  無特殊數據")
            end
        end
    end
end
