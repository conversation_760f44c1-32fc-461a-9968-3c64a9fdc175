# 🧪 Workspace 應用程式測試指南

## 📋 測試步驟

### 1. 確認應用程式已啟動
- 檢查你的 macOS 選單列（螢幕頂部）
- 應該會看到一個新的圖示（可能是視窗或應用程式圖示）
- 點擊該圖示會顯示 Workspace 選單

### 2. 測試 Profile 修改按鈕
1. **打開 Workspace 選單**
   - 點擊選單列的 Workspace 圖示

2. **查看現有的 Profiles**
   - 你應該會看到一個 Profile 列表
   - 每個 Profile 右側都有一個 **筆的圖案** 按鈕

3. **點擊編輯按鈕**
   - 點擊任意一個 Profile 右側的筆圖案按鈕
   - **這就是之前會 crash 的按鈕！**

4. **預期結果**
   - ✅ 應該會打開一個新的視窗（ProfileEditorView）
   - ✅ 視窗標題應該是「編輯 Profile」
   - ✅ 不應該出現 crash 或無反應

### 3. 檢查調試日誌
如果你想看調試信息：

1. **打開終端機**（Terminal）
2. **運行以下命令**：
   ```bash
   cd ~/.hammerspoon
   log show --predicate 'process == "Workspace"' --info --last 1m
   ```
3. **或者簡單地在終端中運行**：
   ```bash
   swift run
   ```
   然後點擊編輯按鈕，你應該會在終端看到：
   ```
   🔧 EditButton 被點擊，Profile: [Profile名稱]
   ```

## 🔍 如果還是有問題

### 檢查應用程式是否在運行
```bash
ps aux | grep Workspace
```

### 重新啟動應用程式
```bash
# 停止現有的應用程式
pkill -f "swift run"
pkill -f "Workspace"

# 重新編譯和運行
swift build
swift run
```

### 檢查 Hammerspoon 狀態
```bash
# 檢查 Hammerspoon 是否在運行
ps aux | grep Hammerspoon

# 重新載入 Hammerspoon 配置
hs -c "hs.reload()"
```

## 📱 應用程式功能測試

### 基本功能測試
1. **Profile 列表顯示** - 應該看到現有的 Profiles
2. **編輯按鈕點擊** - 點擊筆圖案不應該 crash
3. **編輯視窗打開** - 應該顯示 Profile 詳細信息
4. **視窗關閉** - 點擊「完成」按鈕應該正常關閉

### 進階功能測試
1. **Profile 重命名** - 在編輯視窗中修改名稱
2. **Profile 刪除** - 測試刪除功能（小心！）
3. **新 Profile 創建** - 點擊「儲存當前佈局」

## 🆘 常見問題

### Q: 我看不到 Workspace 圖示
**A:** 檢查選單列右側，可能被其他圖示遮住了

### Q: 點擊編輯按鈕還是沒反應
**A:** 
1. 檢查終端是否有錯誤訊息
2. 嘗試重新編譯：`swift build`
3. 重新運行：`swift run`

### Q: 應用程式 crash 了
**A:**
1. 檢查終端的錯誤訊息
2. 運行：`swift build` 確保沒有編譯錯誤
3. 如果還是有問題，請提供錯誤訊息

## 🎯 成功標準

如果以下都正常，表示修復成功：
- ✅ 可以點擊編輯按鈕（筆圖案）
- ✅ ProfileEditorView 正常打開
- ✅ 沒有 crash 或無反應
- ✅ 可以正常關閉編輯視窗

---

**💡 提示：如果你是 Swift 新手，不用擔心！這些步驟都很簡單，主要就是點擊按鈕測試功能。**