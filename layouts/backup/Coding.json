{"name": "Coding", "windows": [{"app": "Sourcetree", "bundleID": "com.torusknot.SourceTreeNotMAS", "title": "/Users/<USER>/Desktop/github/bobo52310/memecreater (Git)", "frame": {"y": -1415, "x": 640, "h": 1415, "w": 1720}}, {"app": "Code", "bundleID": "com.microsoft.VSCode", "title": "Web Anywhere Door", "frame": {"w": 1720, "y": -1414, "h": 1415, "x": -1080}}, {"app": "Code", "bundleID": "com.microsoft.VSCode", "title": "Summary • Untitled-1 — memecreater", "frame": {"x": -1080, "y": -1415, "h": 1415, "w": 3440}}, {"app": "LINE", "bundleID": "jp.naver.line.mac", "title": "LINE", "frame": {"y": 34, "h": 860, "x": 470, "w": 969}}, {"app": "Google Chrome", "bundleID": "com.google.Chrome", "title": "macOS Space 自動化工具 | Google AI Studio - Google Chrome - 柏宏 (<PERSON><PERSON>)", "frame": {"x": 640, "h": 1415, "y": -1415, "w": 1720}}, {"app": "Google Chrome", "bundleID": "com.google.Chrome", "title": "收件匣 - <EMAIL> - QT Medical, Inc. 郵件 - High memory usage - 1.8 GB - Google Chrome - 柏宏 (<PERSON><PERSON>)", "frame": {"h": 1415, "x": -1080, "y": -1415, "w": 1720}}, {"app": "Google Chrome", "bundleID": "com.google.Chrome", "title": "歐美長輩圖：文化現象分析 | Google AI Studio - Google Chrome - 柏宏 (<PERSON><PERSON>)", "frame": {"h": 1415, "x": -1080, "y": -1415, "w": 1720}}, {"app": "Notion", "bundleID": "notion.id", "title": "Space Lighthouse", "frame": {"y": 25, "x": 0, "h": 875, "w": 1440}}, {"app": "Warp", "bundleID": "dev.warp.Warp-Stable", "title": "..n@Mac:~/Desktop/github", "frame": {"w": 1440, "h": 875, "x": -392, "y": -1012}}, {"app": "<PERSON><PERSON><PERSON>", "bundleID": "com.todesktop.230313mzl4w4u92", "title": "init.lua — .hammerspoon", "frame": {"y": -1415, "x": -1080, "h": 1415, "w": 1720}}, {"app": "<PERSON><PERSON><PERSON>", "bundleID": "com.todesktop.230313mzl4w4u92", "title": "restore_layout.sh — Space-Lighthouse", "frame": {"w": 1720, "y": -1415, "h": 1415, "x": -1080}}, {"app": "Hammerspoon", "bundleID": "org.hammerspoon.Hammerspoon", "title": "Hammerspoon Console", "frame": {"y": -1415, "x": 1157, "h": 521, "w": 510}}], "createdAt": "2025-08-01T12:29:34Z", "modifiedAt": "2025-08-01T12:34:52Z", "isSpaceSpecific": false}