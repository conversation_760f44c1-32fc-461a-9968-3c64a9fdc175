#!/usr/bin/env swift

import Foundation
import AppKit

// 模擬 WindowLayout 結構
struct WindowLayout {
    let app: String
    let bundleID: String
    let title: String
}

// 模擬 Profile 結構
struct Profile {
    let name: String
    let windows: [WindowLayout]
}

// 測試用的 AppIconProvider
class AppIconProvider {
    static func getIcon(for bundleID: String) -> NSImage? {
        guard !bundleID.isEmpty else { return nil }
        
        guard let appURL = NSWorkspace.shared.urlForApplication(withBundleIdentifier: bundleID) else {
            return nil
        }
        
        return NSWorkspace.shared.icon(forFile: appURL.path)
    }
    
    static func getIconByName(for appName: String) -> NSImage? {
        guard !appName.isEmpty else { return nil }
        
        let runningApps = NSWorkspace.shared.runningApplications
        
        if let app = runningApps.first(where: { $0.localizedName == appName }) {
            return app.icon
        }
        
        return NSImage(systemSymbolName: "app", accessibilityDescription: appName)
    }
    
    static func getIconWithFallback(bundleID: String?, appName: String?) -> NSImage? {
        if let bundleID = bundleID, let icon = getIcon(for: bundleID) {
            return icon
        }
        
        if let appName = appName, let icon = getIconByName(for: appName) {
            return icon
        }
        
        return NSImage(systemSymbolName: "app", accessibilityDescription: "Unknown App")
    }
}

// 創建測試用的 Profile 資料
let testProfile = Profile(
    name: "測試 Profile",
    windows: [
        WindowLayout(app: "Finder", bundleID: "com.apple.finder", title: "桌面"),
        WindowLayout(app: "Safari", bundleID: "com.apple.Safari", title: "Google"),
        WindowLayout(app: "Unknown App", bundleID: "com.unknown.app", title: "Unknown Window"),
        WindowLayout(app: "Empty Bundle", bundleID: "", title: "Empty Bundle Window"),
        WindowLayout(app: "Visual Studio Code", bundleID: "com.microsoft.VSCode", title: "main.swift")
    ]
)

print("🧪 開始整合測試...")
print("測試 Profile: \(testProfile.name)")
print("視窗數量: \(testProfile.windows.count)")

var successCount = 0
var totalCount = 0

// 測試每個視窗的圖示獲取
for (index, window) in testProfile.windows.enumerated() {
    print("\n--- 測試視窗 \(index + 1) ---")
    print("App: \(window.app)")
    print("Bundle ID: \(window.bundleID.isEmpty ? "空字串" : window.bundleID)")
    print("Title: \(window.title)")
    
    totalCount += 1
    
    // 使用 getIconWithFallback 方法（這是修正後的方法）
    let icon = AppIconProvider.getIconWithFallback(
        bundleID: window.bundleID.isEmpty ? nil : window.bundleID,
        appName: window.app
    )
    
    if icon != nil {
        print("✅ 圖示獲取成功")
        successCount += 1
    } else {
        print("❌ 圖示獲取失敗")
    }
}

print("\n📊 測試結果:")
print("成功: \(successCount)/\(totalCount)")
print("成功率: \(Double(successCount)/Double(totalCount) * 100)%")

if successCount == totalCount {
    print("🎉 所有測試通過！修改按鈕應該不會再 crash 了。")
} else {
    print("⚠️  部分測試失敗，但應用程式不會 crash（有 fallback 機制）")
}

print("\n✅ 整合測試完成，沒有發生 crash！")