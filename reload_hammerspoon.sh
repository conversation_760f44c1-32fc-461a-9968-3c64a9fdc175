#!/bin/bash

echo "更新 Hammerspoon 配置..."

# 複製配置文件
cp "Workspace/Resources/init.lua" "$HOME/.hammerspoon/init.lua"

if [ $? -eq 0 ]; then
    echo "✅ 配置文件已更新"
    
    # 檢查 Hammerspoon 是否正在運行
    if pgrep -x "Hammerspoon" > /dev/null; then
        echo "🔄 重新載入 Hammerspoon..."
        # 發送重新載入命令
        osascript -e 'tell application "Hammerspoon" to reload config'
        echo "✅ 配置已重新載入"
    else
        echo "⚠️  Hammerspoon 未運行"
    fi
else
    echo "❌ 配置文件更新失敗"
fi