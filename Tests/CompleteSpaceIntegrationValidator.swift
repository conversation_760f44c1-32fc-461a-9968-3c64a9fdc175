import Foundation
import SwiftUI
@testable import Workspace

/**
 * 完整的 Space 整合驗證器
 *
 * 提供全面的 Space 功能整合驗證，確保所有組件正確協作
 *
 * ## 驗證範圍
 * - 所有核心組件的功能性
 * - 組件間的整合和通信
 * - 完整的使用者工作流程
 * - 錯誤處理和邊界情況
 * - 效能和穩定性
 *
 * @since 1.0.0
 * <AUTHOR> Integration Team
 */
class CompleteSpaceIntegrationValidator {
    
    // MARK: - Properties
    
    private let spaceProfileManager = SpaceProfileManager.shared
    private let spaceDetector = SpaceDetector.shared
    private let profileManager = ProfileManager.shared
    private let hammerspoonManager = HammerspoonManager.shared
    
    private var validationResults: [ValidationResult] = []
    
    // MARK: - Public Methods
    
    /**
     * 執行完整的整合驗證
     *
     * @return 驗證是否全部通過
     */
    func runCompleteValidation() -> Bool {
        print("🔍 開始執行完整的 Space 整合驗證...")
        print("=" * 70)
        
        validationResults.removeAll()
        
        // 執行所有驗證步驟
        let validationSteps: [(String, () -> ValidationResult)] = [
            ("核心組件初始化", validateCoreComponentInitialization),
            ("Space 偵測功能", validateSpaceDetection),
            ("設定檔管理功能", validateProfileManagement),
            ("Space-Profile 映射", validateSpaceProfileMapping),
            ("UI 組件創建", validateUIComponentCreation),
            ("狀態同步機制", validateStateSynchronization),
            ("完整工作流程", validateCompleteWorkflow),
            ("錯誤處理機制", validateErrorHandling),
            ("效能和穩定性", validatePerformanceAndStability),
            ("資料持久化", validateDataPersistence)
        ]
        
        for (stepName, validationFunction) in validationSteps {
            print("\n🔎 驗證步驟: \(stepName)")
            let result = validationFunction()
            validationResults.append(result)
            
            if result.passed {
                print("✅ \(stepName) 驗證通過")
                if !result.details.isEmpty {
                    for detail in result.details {
                        print("   ℹ️  \(detail)")
                    }
                }
            } else {
                print("❌ \(stepName) 驗證失敗")
                for error in result.errors {
                    print("   ⚠️  \(error)")
                }
            }
        }
        
        // 生成驗證報告
        generateValidationReport()
        
        let allPassed = validationResults.allSatisfy { $0.passed }
        return allPassed
    }
    
    // MARK: - Validation Steps
    
    /**
     * 驗證核心組件初始化
     */
    private func validateCoreComponentInitialization() -> ValidationResult {
        var errors: [String] = []
        var details: [String] = []
        
        // 驗證 SpaceDetector 初始化
        if spaceDetector == nil {
            errors.append("SpaceDetector 未正確初始化")
        } else {
            details.append("SpaceDetector 初始化成功")
        }
        
        // 驗證 SpaceProfileManager 初始化
        if spaceProfileManager == nil {
            errors.append("SpaceProfileManager 未正確初始化")
        } else {
            details.append("SpaceProfileManager 初始化成功")
        }
        
        // 驗證 ProfileManager 初始化
        if profileManager == nil {
            errors.append("ProfileManager 未正確初始化")
        } else {
            details.append("ProfileManager 初始化成功")
        }
        
        // 驗證 HammerspoonManager 初始化
        if hammerspoonManager == nil {
            errors.append("HammerspoonManager 未正確初始化")
        } else {
            details.append("HammerspoonManager 初始化成功")
        }
        
        return ValidationResult(
            stepName: "核心組件初始化",
            passed: errors.isEmpty,
            errors: errors,
            details: details
        )
    }
    
    /**
     * 驗證 Space 偵測功能
     */
    private func validateSpaceDetection() -> ValidationResult {
        var errors: [String] = []
        var details: [String] = []
        
        // 設定測試 Spaces
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        
        // 驗證 Spaces 設定
        if spaceDetector.availableSpaces.count != 3 {
            errors.append("可用 Spaces 數量不正確，期望 3 個，實際 \(spaceDetector.availableSpaces.count) 個")
        } else {
            details.append("成功偵測到 3 個 Spaces")
        }
        
        // 驗證當前 Space 設定
        spaceDetector.setCurrentSpace(1)
        if spaceDetector.currentSpaceID != 1 {
            errors.append("當前 Space 設定失敗")
        } else {
            details.append("當前 Space 設定成功")
        }
        
        // 驗證 Space 可存取性檢查
        let accessibleSpaces = testSpaces.filter { spaceDetector.isSpaceAccessible($0.id) }
        if accessibleSpaces.count != testSpaces.count {
            errors.append("Space 可存取性檢查失敗")
        } else {
            details.append("所有 Spaces 都可存取")
        }
        
        return ValidationResult(
            stepName: "Space 偵測功能",
            passed: errors.isEmpty,
            errors: errors,
            details: details
        )
    }
    
    /**
     * 驗證設定檔管理功能
     */
    private func validateProfileManagement() -> ValidationResult {
        var errors: [String] = []
        var details: [String] = []
        
        // 建立測試設定檔
        let testProfile = createTestProfile(name: "驗證測試設定檔", spaceID: 1)
        
        // 驗證設定檔建立
        if testProfile.name.isEmpty {
            errors.append("設定檔建立失敗")
        } else {
            details.append("設定檔建立成功")
        }
        
        // 驗證設定檔屬性
        if testProfile.spaceID != 1 {
            errors.append("設定檔 Space ID 不正確")
        } else {
            details.append("設定檔 Space ID 正確")
        }
        
        if !testProfile.isSpaceSpecific {
            errors.append("設定檔 Space 特定標記不正確")
        } else {
            details.append("設定檔 Space 特定標記正確")
        }
        
        // 驗證設定檔視窗資料
        if testProfile.windows.isEmpty {
            errors.append("設定檔視窗資料為空")
        } else {
            details.append("設定檔包含 \(testProfile.windows.count) 個視窗")
        }
        
        return ValidationResult(
            stepName: "設定檔管理功能",
            passed: errors.isEmpty,
            errors: errors,
            details: details
        )
    }
    
    /**
     * 驗證 Space-Profile 映射
     */
    private func validateSpaceProfileMapping() -> ValidationResult {
        var errors: [String] = []
        var details: [String] = []
        
        // 清理測試環境
        spaceProfileManager.clearAllMappings()
        
        // 建立測試設定檔並儲存到不同 Spaces
        let profiles = [
            createTestProfile(name: "Space1設定檔1", spaceID: 1),
            createTestProfile(name: "Space1設定檔2", spaceID: 1),
            createTestProfile(name: "Space2設定檔1", spaceID: 2),
            createTestProfile(name: "Space3設定檔1", spaceID: 3)
        ]
        
        for profile in profiles {
            if let spaceID = profile.spaceID {
                spaceProfileManager.saveProfileToSpace(profile, spaceID: spaceID)
            }
        }
        
        // 驗證映射結果
        let space1Count = spaceProfileManager.getProfileCount(for: 1)
        let space2Count = spaceProfileManager.getProfileCount(for: 2)
        let space3Count = spaceProfileManager.getProfileCount(for: 3)
        
        if space1Count != 2 {
            errors.append("Space 1 設定檔數量不正確，期望 2 個，實際 \(space1Count) 個")
        } else {
            details.append("Space 1 包含 2 個設定檔")
        }
        
        if space2Count != 1 {
            errors.append("Space 2 設定檔數量不正確，期望 1 個，實際 \(space2Count) 個")
        } else {
            details.append("Space 2 包含 1 個設定檔")
        }
        
        if space3Count != 1 {
            errors.append("Space 3 設定檔數量不正確，期望 1 個，實際 \(space3Count) 個")
        } else {
            details.append("Space 3 包含 1 個設定檔")
        }
        
        // 驗證設定檔移動功能
        let profileToMove = profiles[0]
        spaceProfileManager.moveProfileBetweenSpaces(profileToMove, from: 1, to: 2)
        
        let newSpace1Count = spaceProfileManager.getProfileCount(for: 1)
        let newSpace2Count = spaceProfileManager.getProfileCount(for: 2)
        
        if newSpace1Count != 1 || newSpace2Count != 2 {
            errors.append("設定檔移動功能失敗")
        } else {
            details.append("設定檔移動功能正常")
        }
        
        return ValidationResult(
            stepName: "Space-Profile 映射",
            passed: errors.isEmpty,
            errors: errors,
            details: details
        )
    }
    
    /**
     * 驗證 UI 組件創建
     */
    private func validateUIComponentCreation() -> ValidationResult {
        var errors: [String] = []
        var details: [String] = []
        
        // 驗證 SpaceAwareProfileView 創建
        do {
            let spaceAwareView = SpaceAwareProfileView()
            if spaceAwareView != nil {
                details.append("SpaceAwareProfileView 創建成功")
            }
        } catch {
            errors.append("SpaceAwareProfileView 創建失敗: \(error)")
        }
        
        // 驗證 SpaceTabView 創建
        do {
            var selectedSpaceID: Int? = 1
            let binding = Binding<Int?>(
                get: { selectedSpaceID },
                set: { selectedSpaceID = $0 }
            )
            let spaceTabView = SpaceTabView(selectedSpaceID: binding)
            if spaceTabView != nil {
                details.append("SpaceTabView 創建成功")
            }
        } catch {
            errors.append("SpaceTabView 創建失敗: \(error)")
        }
        
        // 驗證 SpaceTabButton 創建
        do {
            let testSpace = SpaceInfo(id: 1, name: "Test Space", isActive: true, displayName: "測試 Space")
            let spaceTabButton = SpaceTabButton(
                space: testSpace,
                isSelected: true,
                isCurrentSpace: true,
                onTap: {}
            )
            if spaceTabButton != nil {
                details.append("SpaceTabButton 創建成功")
            }
        } catch {
            errors.append("SpaceTabButton 創建失敗: \(error)")
        }
        
        return ValidationResult(
            stepName: "UI 組件創建",
            passed: errors.isEmpty,
            errors: errors,
            details: details
        )
    }
    
    /**
     * 驗證狀態同步機制
     */
    private func validateStateSynchronization() -> ValidationResult {
        var errors: [String] = []
        var details: [String] = []
        
        // 設定測試環境
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        
        // 建立測試設定檔
        let profile = createTestProfile(name: "同步測試設定檔", spaceID: 1)
        spaceProfileManager.saveProfileToSpace(profile, spaceID: 1)
        
        // 測試 Space 切換同步
        spaceDetector.setCurrentSpace(1)
        let currentProfiles1 = spaceProfileManager.getProfilesForCurrentSpace()
        
        spaceDetector.setCurrentSpace(2)
        let currentProfiles2 = spaceProfileManager.getProfilesForCurrentSpace()
        
        if currentProfiles1.count != 1 {
            errors.append("Space 1 同步失敗，期望 1 個設定檔，實際 \(currentProfiles1.count) 個")
        } else {
            details.append("Space 1 狀態同步正常")
        }
        
        if currentProfiles2.count != 0 {
            errors.append("Space 2 同步失敗，期望 0 個設定檔，實際 \(currentProfiles2.count) 個")
        } else {
            details.append("Space 2 狀態同步正常")
        }
        
        // 測試映射更新同步
        let newProfile = createTestProfile(name: "新同步測試設定檔", spaceID: 2)
        spaceProfileManager.saveProfileToSpace(newProfile, spaceID: 2)
        
        let updatedProfiles2 = spaceProfileManager.getProfilesForCurrentSpace()
        if updatedProfiles2.count != 1 {
            errors.append("映射更新同步失敗")
        } else {
            details.append("映射更新同步正常")
        }
        
        return ValidationResult(
            stepName: "狀態同步機制",
            passed: errors.isEmpty,
            errors: errors,
            details: details
        )
    }
    
    /**
     * 驗證完整工作流程
     */
    private func validateCompleteWorkflow() -> ValidationResult {
        var errors: [String] = []
        var details: [String] = []
        
        // 清理環境
        spaceProfileManager.clearAllMappings()
        
        // 步驟 1: 初始化 Spaces
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        spaceDetector.setCurrentSpace(1)
        details.append("步驟 1: Spaces 初始化完成")
        
        // 步驟 2: 建立和儲存設定檔
        let workProfile = createTestProfile(name: "工作設定檔", spaceID: 1)
        let entertainmentProfile = createTestProfile(name: "娛樂設定檔", spaceID: 2)
        
        spaceProfileManager.saveProfileToSpace(workProfile, spaceID: 1)
        spaceProfileManager.saveProfileToSpace(entertainmentProfile, spaceID: 2)
        details.append("步驟 2: 設定檔建立和儲存完成")
        
        // 步驟 3: 驗證 Space 分組
        let space1Profiles = spaceProfileManager.getProfilesForSpace(1)
        let space2Profiles = spaceProfileManager.getProfilesForSpace(2)
        
        if space1Profiles.count != 1 || space2Profiles.count != 1 {
            errors.append("步驟 3: Space 分組驗證失敗")
        } else {
            details.append("步驟 3: Space 分組驗證成功")
        }
        
        // 步驟 4: 測試 Space 切換
        spaceDetector.setCurrentSpace(2)
        let currentProfiles = spaceProfileManager.getProfilesForCurrentSpace()
        
        if currentProfiles.count != 1 || currentProfiles[0].name != "娛樂設定檔" {
            errors.append("步驟 4: Space 切換測試失敗")
        } else {
            details.append("步驟 4: Space 切換測試成功")
        }
        
        // 步驟 5: 測試設定檔移動
        spaceProfileManager.moveProfileBetweenSpaces(workProfile, from: 1, to: 3)
        let space3Profiles = spaceProfileManager.getProfilesForSpace(3)
        
        if space3Profiles.count != 1 || space3Profiles[0].name != "工作設定檔" {
            errors.append("步驟 5: 設定檔移動測試失敗")
        } else {
            details.append("步驟 5: 設定檔移動測試成功")
        }
        
        // 步驟 6: 測試資料持久化
        spaceProfileManager.reloadMappings()
        let reloadedSpace3Profiles = spaceProfileManager.getProfilesForSpace(3)
        
        if reloadedSpace3Profiles.count != 1 {
            errors.append("步驟 6: 資料持久化測試失敗")
        } else {
            details.append("步驟 6: 資料持久化測試成功")
        }
        
        return ValidationResult(
            stepName: "完整工作流程",
            passed: errors.isEmpty,
            errors: errors,
            details: details
        )
    }
    
    /**
     * 驗證錯誤處理機制
     */
    private func validateErrorHandling() -> ValidationResult {
        var errors: [String] = []
        var details: [String] = []
        
        // 測試無效 Space ID 處理
        let invalidProfile = createTestProfile(name: "無效測試", spaceID: 999)
        spaceProfileManager.saveProfileToSpace(invalidProfile, spaceID: 999)
        
        let invalidSpaceProfiles = spaceProfileManager.getProfilesForSpace(999)
        if !invalidSpaceProfiles.isEmpty {
            errors.append("無效 Space ID 處理失敗")
        } else {
            details.append("無效 Space ID 處理正常")
        }
        
        // 測試空 Spaces 處理
        spaceDetector.setTestSpaces([])
        let emptySpaceProfiles = spaceProfileManager.getProfilesForCurrentSpace()
        
        if !emptySpaceProfiles.isEmpty {
            errors.append("空 Spaces 處理失敗")
        } else {
            details.append("空 Spaces 處理正常")
        }
        
        // 測試 nil Space ID 處理
        let nilSpaceProfile = createTestProfile(name: "Nil Space 測試", spaceID: nil)
        if nilSpaceProfile.spaceID != nil {
            errors.append("Nil Space ID 處理失敗")
        } else {
            details.append("Nil Space ID 處理正常")
        }
        
        return ValidationResult(
            stepName: "錯誤處理機制",
            passed: errors.isEmpty,
            errors: errors,
            details: details
        )
    }
    
    /**
     * 驗證效能和穩定性
     */
    private func validatePerformanceAndStability() -> ValidationResult {
        var errors: [String] = []
        var details: [String] = []
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // 設定測試環境
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        
        // 建立大量設定檔測試效能
        let profileCount = 50
        for i in 1...profileCount {
            let spaceID = (i % 3) + 1
            let profile = createTestProfile(name: "效能測試\(i)", spaceID: spaceID)
            spaceProfileManager.saveProfileToSpace(profile, spaceID: spaceID)
        }
        
        // 驗證設定檔數量
        let totalProfiles = spaceProfileManager.getProfileCount(for: 1) +
                           spaceProfileManager.getProfileCount(for: 2) +
                           spaceProfileManager.getProfileCount(for: 3)
        
        if totalProfiles != profileCount {
            errors.append("大量設定檔建立失敗，期望 \(profileCount) 個，實際 \(totalProfiles) 個")
        } else {
            details.append("成功建立 \(profileCount) 個設定檔")
        }
        
        // 測試大量 Space 切換
        for _ in 1...20 {
            let randomSpace = Int.random(in: 1...3)
            spaceDetector.setCurrentSpace(randomSpace)
        }
        
        let endTime = CFAbsoluteTimeGetCurrent()
        let duration = endTime - startTime
        
        if duration > 3.0 {
            errors.append("效能測試超時，耗時 \(String(format: "%.3f", duration)) 秒")
        } else {
            details.append("效能測試通過，耗時 \(String(format: "%.3f", duration)) 秒")
        }
        
        return ValidationResult(
            stepName: "效能和穩定性",
            passed: errors.isEmpty,
            errors: errors,
            details: details
        )
    }
    
    /**
     * 驗證資料持久化
     */
    private func validateDataPersistence() -> ValidationResult {
        var errors: [String] = []
        var details: [String] = []
        
        // 建立測試資料
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        
        let profile1 = createTestProfile(name: "持久化測試1", spaceID: 1)
        let profile2 = createTestProfile(name: "持久化測試2", spaceID: 2)
        
        spaceProfileManager.saveProfileToSpace(profile1, spaceID: 1)
        spaceProfileManager.saveProfileToSpace(profile2, spaceID: 2)
        
        // 記錄初始狀態
        let initialSpace1Count = spaceProfileManager.getProfileCount(for: 1)
        let initialSpace2Count = spaceProfileManager.getProfileCount(for: 2)
        
        // 重新載入資料
        spaceProfileManager.reloadMappings()
        
        // 驗證資料持久化
        let reloadedSpace1Count = spaceProfileManager.getProfileCount(for: 1)
        let reloadedSpace2Count = spaceProfileManager.getProfileCount(for: 2)
        
        if reloadedSpace1Count != initialSpace1Count {
            errors.append("Space 1 資料持久化失敗")
        } else {
            details.append("Space 1 資料持久化成功")
        }
        
        if reloadedSpace2Count != initialSpace2Count {
            errors.append("Space 2 資料持久化失敗")
        } else {
            details.append("Space 2 資料持久化成功")
        }
        
        return ValidationResult(
            stepName: "資料持久化",
            passed: errors.isEmpty,
            errors: errors,
            details: details
        )
    }
    
    // MARK: - Helper Methods
    
    /**
     * 生成驗證報告
     */
    private func generateValidationReport() {
        print("\n" + "=" * 70)
        print("📋 Space 整合驗證報告")
        print("=" * 70)
        
        let passedCount = validationResults.filter { $0.passed }.count
        let totalCount = validationResults.count
        
        print("📊 總體結果: \(passedCount)/\(totalCount) 驗證步驟通過")
        print("📈 通過率: \(String(format: "%.1f", Double(passedCount) / Double(totalCount) * 100))%")
        
        print("\n📝 詳細結果:")
        for result in validationResults {
            let status = result.passed ? "✅" : "❌"
            print("\(status) \(result.stepName)")
            
            if !result.passed {
                for error in result.errors {
                    print("   ⚠️  \(error)")
                }
            }
        }
        
        if passedCount == totalCount {
            print("\n🎉 所有驗證步驟都通過了！Space 整合功能完全正常。")
        } else {
            print("\n⚠️  有 \(totalCount - passedCount) 個驗證步驟失敗，需要檢查和修復。")
        }
        
        print("=" * 70)
    }
    
    /**
     * 建立測試 Spaces
     */
    private func createTestSpaces() -> [SpaceInfo] {
        return [
            SpaceInfo(id: 1, name: "Space 1", isActive: true, displayName: "工作區 1"),
            SpaceInfo(id: 2, name: "Space 2", isActive: false, displayName: "工作區 2"),
            SpaceInfo(id: 3, name: "Space 3", isActive: false, displayName: "工作區 3")
        ]
    }
    
    /**
     * 建立測試設定檔
     */
    private func createTestProfile(name: String, spaceID: Int?) -> Profile {
        let testWindows = [
            WindowLayout(
                id: UUID().uuidString,
                app: "測試應用程式",
                title: "測試視窗",
                bundleID: "com.test.app",
                frame: WindowFrame(x: 100, y: 100, w: 800, h: 600)
            )
        ]
        
        return Profile(
            name: name,
            windows: testWindows,
            spaceID: spaceID,
            isSpaceSpecific: spaceID != nil,
            createdAt: Date(),
            modifiedAt: Date()
        )
    }
}

// MARK: - Validation Result

/**
 * 驗證結果結構
 */
struct ValidationResult {
    let stepName: String
    let passed: Bool
    let errors: [String]
    let details: [String]
}

// MARK: - Public Interface

/**
 * 執行完整的 Space 整合驗證
 *
 * @return 驗證是否全部通過
 */
func validateCompleteSpaceIntegration() -> Bool {
    let validator = CompleteSpaceIntegrationValidator()
    return validator.runCompleteValidation()
}