import Testing
import Combine
@testable import Workspace

@Suite("SpaceDetector Tests")
struct SpaceDetectorTests {
    
    // MARK: - SpaceInfo Tests
    
    @Test("SpaceInfo Initialization")
    func testSpaceInfoInitialization() {
        // Test with default values
        let space1 = SpaceInfo(id: 1)
        #expect(space1.id == 1)
        #expect(space1.name == "Space 1")
        #expect(space1.displayName == "工作區 1")
        #expect(space1.isActive == false)
        
        // Test with custom values
        let space2 = SpaceInfo(id: 2, name: "Custom Space", isActive: true)
        #expect(space2.id == 2)
        #expect(space2.name == "Custom Space")
        #expect(space2.displayName == "Custom Space")
        #expect(space2.isActive == true)
    }
    
    @Test("SpaceInfo Codable")
    func testSpaceInfoCodable() throws {
        let originalSpace = SpaceInfo(id: 1, name: "Test Space", isActive: true)
        
        // Test encoding
        let encoder = JSONEncoder()
        let data = try encoder.encode(originalSpace)
        
        // Test decoding
        let decoder = JSONDecoder()
        let decodedSpace = try decoder.decode(SpaceInfo.self, from: data)
        
        #expect(originalSpace == decodedSpace)
    }
    
    @Test("SpaceInfo Equality")
    func testSpaceInfoEquality() {
        let space1 = SpaceInfo(id: 1, name: "Space 1", isActive: true)
        let space2 = SpaceInfo(id: 1, name: "Space 1", isActive: true)
        let space3 = SpaceInfo(id: 2, name: "Space 2", isActive: false)
        
        #expect(space1 == space2)
        #expect(space1 != space3)
    }
    
    // MARK: - SpaceDetector Initialization Tests
    
    @Test("SpaceDetector Singleton")
    func testSpaceDetectorSingleton() {
        let detector1 = SpaceDetector.shared
        let detector2 = SpaceDetector.shared
        
        #expect(detector1 === detector2, "SpaceDetector 應該是單例")
    }
    
    @Test("SpaceDetector Initial Values")
    func testSpaceDetectorInitialValues() {
        let spaceDetector = SpaceDetector.shared
        #expect(spaceDetector.maxSupportedSpaces == 3)
        // availableSpaces 和 currentSpaceID 可能為空，這是正常的
    }
    
    // MARK: - Space Detection Tests
    
    @Test("Get Current Space")
    func testGetCurrentSpace() {
        let spaceDetector = SpaceDetector.shared
        let currentSpace = spaceDetector.getCurrentSpace()
        
        // 當前 Space 應該是 nil 或在有效範圍內 (1-3)
        if let spaceID = currentSpace {
            #expect(spaceID >= 1 && spaceID <= 3, "當前 Space ID 應該在 1-3 範圍內")
        }
    }
    
    @Test("Get Available Spaces")
    func testGetAvailableSpaces() {
        let spaceDetector = SpaceDetector.shared
        let availableSpaces = spaceDetector.getAvailableSpaces()
        
        // 應該至少有一個可用的 Space
        #expect(!availableSpaces.isEmpty, "應該至少有一個可用的 Space")
        
        // 所有 Space ID 應該在有效範圍內
        for space in availableSpaces {
            #expect(space.id >= 1 && space.id <= 3, "Space ID 應該在 1-3 範圍內")
        }
        
        // Space ID 應該是唯一的
        let spaceIDs = availableSpaces.map { $0.id }
        let uniqueSpaceIDs = Set(spaceIDs)
        #expect(spaceIDs.count == uniqueSpaceIDs.count, "Space ID 應該是唯一的")
    }
    
    @Test("Is Space Accessible")
    func testIsSpaceAccessible() {
        let spaceDetector = SpaceDetector.shared
        
        // 測試有效的 Space ID
        for spaceID in 1...3 {
            let isAccessible = spaceDetector.isSpaceAccessible(spaceID)
            // 至少 Space 1 應該是可存取的
            if spaceID == 1 {
                #expect(isAccessible, "Space 1 應該總是可存取的")
            }
        }
        
        // 測試無效的 Space ID
        #expect(!spaceDetector.isSpaceAccessible(0), "Space 0 應該不可存取")
        #expect(!spaceDetector.isSpaceAccessible(4), "Space 4 應該不可存取")
        #expect(!spaceDetector.isSpaceAccessible(-1), "負數 Space ID 應該不可存取")
    }
    
    // MARK: - Extension Methods Tests
    
    @Test("Get Display Name")
    func testGetDisplayName() {
        let spaceDetector = SpaceDetector.shared
        let displayName = spaceDetector.getDisplayName(for: 1)
        #expect(displayName.contains("工作區") || displayName.contains("Space"), "顯示名稱應該包含適當的前綴")
    }
    
    @Test("Is Current Space")
    func testIsCurrentSpace() {
        let spaceDetector = SpaceDetector.shared
        let currentSpace = spaceDetector.getCurrentSpace()
        
        if let spaceID = currentSpace {
            #expect(spaceDetector.isCurrentSpace(spaceID), "應該正確識別當前 Space")
            
            // 測試其他 Space ID
            for testID in 1...3 {
                if testID != spaceID {
                    #expect(!spaceDetector.isCurrentSpace(testID), "非當前 Space 應該回傳 false")
                }
            }
        }
    }
    
    @Test("Get Default Space ID")
    func testGetDefaultSpaceID() {
        let spaceDetector = SpaceDetector.shared
        let defaultSpaceID = spaceDetector.getDefaultSpaceID()
        #expect(defaultSpaceID == 1, "預設 Space ID 應該是 1")
    }
    
    // MARK: - Error Handling Tests
    
    @Test("Invalid Space ID Handling")
    func testInvalidSpaceIDHandling() {
        let spaceDetector = SpaceDetector.shared
        // 測試無效的 Space ID
        let invalidIDs = [0, -1, 4, 10, 100]
        
        for invalidID in invalidIDs {
            #expect(!spaceDetector.isSpaceAccessible(invalidID), "無效的 Space ID \(invalidID) 應該不可存取")
        }
    }
    
    @Test("Max Supported Spaces Limit")
    func testMaxSupportedSpacesLimit() {
        let spaceDetector = SpaceDetector.shared
        let availableSpaces = spaceDetector.getAvailableSpaces()
        
        // 可用 Spaces 數量不應該超過最大支援數量
        #expect(availableSpaces.count <= spaceDetector.maxSupportedSpaces, "可用 Spaces 數量不應該超過最大支援數量")
        
        // 所有可用 Spaces 的 ID 都應該在支援範圍內
        for space in availableSpaces {
            #expect(space.id <= spaceDetector.maxSupportedSpaces, "Space ID 不應該超過最大支援數量")
        }
    }
}

@Suite("SpaceInfo JSON Tests")
struct SpaceInfoJSONTests {
    
    @Test("SpaceInfo JSON Serialization")
    func testSpaceInfoJSONSerialization() throws {
        let spaces = [
            SpaceInfo(id: 1, name: "Space 1", isActive: true),
            SpaceInfo(id: 2, name: "Space 2", isActive: false),
            SpaceInfo(id: 3, name: "Space 3", isActive: false)
        ]
        
        let encoder = JSONEncoder()
        encoder.outputFormatting = .prettyPrinted
        let data = try encoder.encode(spaces)
        
        let decoder = JSONDecoder()
        let decodedSpaces = try decoder.decode([SpaceInfo].self, from: data)
        
        #expect(spaces == decodedSpaces, "序列化和反序列化應該保持資料完整性")
    }
    
    @Test("SpaceInfo Description")
    func testSpaceInfoDescription() {
        let space = SpaceInfo(id: 1, name: "Test Space", isActive: true)
        
        // 驗證基本屬性
        #expect(space.id == 1)
        #expect(space.name == "Test Space")
        #expect(space.displayName == "Test Space")
        #expect(space.isActive == true)
    }
}