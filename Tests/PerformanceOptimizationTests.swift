import XCTest
import SwiftUI
import Combine
@testable import Workspace

/// 效能最佳化測試套件
/// 驗證懶載入、快取和 UI 最佳化的效果
class PerformanceOptimizationTests: XCTestCase {
    
    var lazyLoader: LazyProfileLoader!
    var spaceCache: SpaceDetectionCache!
    var uiOptimizer: UIPerformanceOptimizer!
    var cancellables: Set<AnyCancellable>!
    
    override func setUp() {
        super.setUp()
        lazyLoader = LazyProfileLoader.shared
        spaceCache = SpaceDetectionCache.shared
        uiOptimizer = UIPerformanceOptimizer.shared
        cancellables = Set<AnyCancellable>()
        
        // 清除快取以確保測試環境乾淨
        lazyLoader.clearCache()
        spaceCache.clearCache()
        uiOptimizer.clearRenderCache()
    }
    
    override func tearDown() {
        cancellables.removeAll()
        super.tearDown()
    }
    
    // MARK: - Lazy Loading Tests
    
    /// 測試設定檔懶載入功能
    func testProfileLazyLoading() {
        let expectation = XCTestExpectation(description: "Profile lazy loading")
        
        // 測試載入不存在的設定檔
        lazyLoader.loadProfile("nonexistent_profile")
            .sink { profile in
                XCTAssertNil(profile, "不存在的設定檔應該返回 nil")
                expectation.fulfill()
            }
            .store(in: &cancellables)
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    /// 測試設定檔元資料載入效能
    func testProfileMetadataLoadingPerformance() {
        measure {
            let metadata = lazyLoader.getAllProfileMetadata()
            XCTAssertTrue(metadata.count >= 0, "應該能夠載入設定檔元資料")
        }
    }
    
    /// 測試批次載入效能
    func testBatchProfileLoadingPerformance() {
        let profileNames = ["test1", "test2", "test3", "test4", "test5"]
        
        measure {
            let expectation = XCTestExpectation(description: "Batch loading")
            
            lazyLoader.loadProfiles(profileNames)
                .sink { profiles in
                    // 驗證批次載入結果
                    XCTAssertTrue(profiles.count >= 0, "批次載入應該返回結果")
                    expectation.fulfill()
                }
                .store(in: &cancellables)
            
            wait(for: [expectation], timeout: 2.0)
        }
    }
    
    /// 測試快取命中率
    func testLazyLoadingCacheHitRate() {
        let testProfileName = "test_profile"
        let expectation = XCTestExpectation(description: "Cache hit rate test")
        expectation.expectedFulfillmentCount = 2
        
        // 第一次載入（應該是 cache miss）
        lazyLoader.loadProfile(testProfileName)
            .sink { _ in
                expectation.fulfill()
                
                // 第二次載入（應該是 cache hit）
                self.lazyLoader.loadProfile(testProfileName)
                    .sink { _ in
                        expectation.fulfill()
                    }
                    .store(in: &self.cancellables)
            }
            .store(in: &cancellables)
        
        wait(for: [expectation], timeout: 5.0)
        
        let stats = lazyLoader.getCacheStats()
        XCTAssertGreaterThanOrEqual(stats.loadedCount, 0, "載入的設定檔數量應該大於等於 0")
    }
    
    // MARK: - Space Detection Cache Tests
    
    /// 測試 Space 偵測快取功能
    func testSpaceDetectionCache() {
        // 預熱快取
        spaceCache.warmUpCache()
        
        measure {
            // 測試快取的 getCurrentSpaceID 效能
            let spaceID = spaceCache.getCurrentSpaceID()
            XCTAssertTrue(spaceID == nil || spaceID! > 0, "Space ID 應該是正數或 nil")
        }
    }
    
    /// 測試 Space 可存取性快取
    func testSpaceAccessibilityCache() {
        measure {
            for spaceID in 1...3 {
                let isAccessible = spaceCache.isSpaceAccessible(spaceID)
                XCTAssertTrue(isAccessible || !isAccessible, "可存取性檢查應該返回布林值")
            }
        }
    }
    
    /// 測試快取統計資訊
    func testCacheStatistics() {
        // 執行一些快取操作
        _ = spaceCache.getCurrentSpaceID()
        _ = spaceCache.getAvailableSpaces()
        _ = spaceCache.isSpaceAccessible(1)
        
        let stats = spaceCache.getCacheStatistics()
        XCTAssertGreaterThanOrEqual(stats.totalRequests, 3, "應該有至少 3 個請求")
        XCTAssertGreaterThanOrEqual(stats.hitRate, 0, "命中率應該大於等於 0")
        XCTAssertLessThanOrEqual(stats.hitRate, 100, "命中率應該小於等於 100")
    }
    
    /// 測試快取清除功能
    func testCacheClearance() {
        // 執行一些操作以填充快取
        _ = spaceCache.getCurrentSpaceID()
        _ = spaceCache.getAvailableSpaces()
        
        let statsBeforeClear = spaceCache.getCacheStatistics()
        
        // 清除快取
        spaceCache.clearCache()
        
        let statsAfterClear = spaceCache.getCacheStatistics()
        XCTAssertEqual(statsAfterClear.hitCount, 0, "清除後命中次數應該為 0")
        XCTAssertEqual(statsAfterClear.missCount, 0, "清除後未命中次數應該為 0")
    }
    
    // MARK: - UI Performance Tests
    
    /// 測試 UI 動畫最佳化
    func testUIAnimationOptimization() {
        measure {
            for context in [AnimationContext.spaceTransition, .profileListUpdate, .tabSwitch, .contentLoad, .hover] {
                let animation = uiOptimizer.optimizedAnimation(for: context)
                XCTAssertNotNil(animation, "應該返回最佳化的動畫")
            }
        }
    }
    
    /// 測試動畫狀態追蹤
    func testAnimationStateTracking() {
        let animationId = "test_animation"
        
        uiOptimizer.startAnimation(animationId, context: .spaceTransition)
        
        let metrics = uiOptimizer.performanceMetrics
        XCTAssertGreaterThan(metrics.activeAnimations, 0, "應該有活躍的動畫")
        
        uiOptimizer.endAnimation(animationId)
        
        let updatedMetrics = uiOptimizer.performanceMetrics
        XCTAssertEqual(updatedMetrics.activeAnimations, 0, "動畫結束後應該沒有活躍動畫")
    }
    
    /// 測試渲染快取功能
    func testRenderCaching() {
        let testKey = "test_view"
        let testView = Text("Test View")
        
        // 快取視圖
        uiOptimizer.cacheView(testView, for: testKey)
        
        // 檢索快取的視圖
        let cachedView = uiOptimizer.getCachedView(for: testKey)
        XCTAssertNotNil(cachedView, "應該能夠檢索快取的視圖")
        
        let metrics = uiOptimizer.performanceMetrics
        XCTAssertGreaterThan(metrics.cacheHits, 0, "應該有快取命中")
    }
    
    /// 測試 UI 操作時間測量
    func testUIOperationMeasurement() {
        let operationType = "test_operation"
        
        let result = uiOptimizer.measureUIOperation({
            // 模擬一些 UI 操作
            Thread.sleep(forTimeInterval: 0.01)
            return "operation_result"
        }, operationType: operationType)
        
        XCTAssertEqual(result, "operation_result", "應該返回操作結果")
        
        let metrics = uiOptimizer.performanceMetrics
        XCTAssertNotNil(metrics.operationTimes[operationType], "應該記錄操作時間")
        XCTAssertGreaterThan(metrics.operationTimes[operationType]!, 0, "操作時間應該大於 0")
    }
    
    /// 測試批次動畫最佳化
    func testBatchAnimationOptimization() {
        let animations = [
            { return "animation1" },
            { return "animation2" },
            { return "animation3" }
        ]
        
        measure {
            let results = uiOptimizer.batchAnimations(animations)
            XCTAssertEqual(results.count, 3, "應該返回所有動畫結果")
            XCTAssertEqual(results, ["animation1", "animation2", "animation3"], "結果應該正確")
        }
    }
    
    // MARK: - Integration Performance Tests
    
    /// 測試整合效能：Space 切換
    func testSpaceSwitchingPerformance() {
        let spaceProfileManager = SpaceProfileManager.shared
        
        measure {
            // 模擬 Space 切換操作
            for spaceID in 1...3 {
                let profiles = spaceProfileManager.getProfilesForSpace(spaceID)
                XCTAssertTrue(profiles.count >= 0, "應該能夠獲取 Space 設定檔")
                
                let metadata = spaceProfileManager.getProfileMetadataForSpace(spaceID)
                XCTAssertTrue(metadata.count >= 0, "應該能夠獲取 Space 元資料")
            }
        }
    }
    
    /// 測試設定檔載入效能
    func testProfileLoadingPerformance() {
        let spaceProfileManager = SpaceProfileManager.shared
        
        measure {
            let expectation = XCTestExpectation(description: "Profile loading performance")
            
            spaceProfileManager.loadProfile("test_profile")
                .sink { profile in
                    // 驗證載入結果
                    expectation.fulfill()
                }
                .store(in: &cancellables)
            
            wait(for: [expectation], timeout: 1.0)
        }
    }
    
    /// 測試記憶體使用效能
    func testMemoryUsageOptimization() {
        let initialMemory = getCurrentMemoryUsage()
        
        // 執行一系列操作
        for i in 1...100 {
            let profileName = "test_profile_\(i)"
            lazyLoader.loadProfile(profileName)
                .sink { _ in }
                .store(in: &cancellables)
        }
        
        let afterOperationsMemory = getCurrentMemoryUsage()
        
        // 清除快取
        lazyLoader.clearCache()
        uiOptimizer.clearRenderCache()
        
        let afterCleanupMemory = getCurrentMemoryUsage()
        
        // 驗證記憶體使用是否合理
        let memoryIncrease = afterOperationsMemory - initialMemory
        let memoryAfterCleanup = afterCleanupMemory - initialMemory
        
        XCTAssertLessThan(memoryAfterCleanup, memoryIncrease, "清除快取後記憶體使用應該減少")
    }
    
    // MARK: - Helper Methods
    
    /// 獲取當前記憶體使用量（MB）
    private func getCurrentMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / (1024 * 1024) // Convert to MB
        }
        
        return 0
    }
}

// MARK: - Performance Benchmark Tests

/// 效能基準測試
class PerformanceBenchmarkTests: XCTestCase {
    
    /// 基準測試：設定檔載入時間
    func testProfileLoadingBenchmark() {
        let lazyLoader = LazyProfileLoader.shared
        let profileNames = (1...50).map { "benchmark_profile_\($0)" }
        
        measure(metrics: [XCTClockMetric(), XCTMemoryMetric()]) {
            let expectation = XCTestExpectation(description: "Benchmark loading")
            
            lazyLoader.loadProfiles(profileNames)
                .sink { profiles in
                    expectation.fulfill()
                }
                .store(in: &cancellables)
            
            wait(for: [expectation], timeout: 10.0)
        }
    }
    
    /// 基準測試：Space 偵測快取效能
    func testSpaceDetectionBenchmark() {
        let spaceCache = SpaceDetectionCache.shared
        
        measure(metrics: [XCTClockMetric()]) {
            for _ in 1...1000 {
                _ = spaceCache.getCurrentSpaceID()
                _ = spaceCache.getAvailableSpaces()
                _ = spaceCache.isSpaceAccessible(1)
            }
        }
    }
    
    /// 基準測試：UI 動畫最佳化效能
    func testUIAnimationBenchmark() {
        let uiOptimizer = UIPerformanceOptimizer.shared
        
        measure(metrics: [XCTClockMetric()]) {
            for i in 1...1000 {
                let animationId = "benchmark_animation_\(i)"
                uiOptimizer.startAnimation(animationId, context: .spaceTransition)
                uiOptimizer.endAnimation(animationId)
            }
        }
    }
    
    var cancellables = Set<AnyCancellable>()
    
    override func setUp() {
        super.setUp()
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDown() {
        cancellables.removeAll()
        super.tearDown()
    }
}

// MARK: - Performance Regression Tests

/// 效能回歸測試
class PerformanceRegressionTests: XCTestCase {
    
    /// 回歸測試：確保最佳化不會降低基本功能效能
    func testBasicFunctionalityPerformance() {
        let spaceProfileManager = SpaceProfileManager.shared
        
        // 設定效能基準
        let maxAllowedTime: TimeInterval = 0.1
        
        measure {
            let startTime = CFAbsoluteTimeGetCurrent()
            
            // 執行基本操作
            _ = spaceProfileManager.getProfilesForSpace(1)
            _ = spaceProfileManager.getProfilesForCurrentSpace()
            _ = spaceProfileManager.getProfileMetadataForSpace(1)
            
            let endTime = CFAbsoluteTimeGetCurrent()
            let executionTime = endTime - startTime
            
            XCTAssertLessThan(executionTime, maxAllowedTime, 
                             "基本功能執行時間不應超過 \(maxAllowedTime) 秒")
        }
    }
    
    /// 回歸測試：記憶體洩漏檢測
    func testMemoryLeakDetection() {
        let initialMemory = getCurrentMemoryUsage()
        
        // 執行大量操作
        for _ in 1...100 {
            autoreleasepool {
                let lazyLoader = LazyProfileLoader.shared
                let spaceCache = SpaceDetectionCache.shared
                let uiOptimizer = UIPerformanceOptimizer.shared
                
                _ = lazyLoader.getAllProfileMetadata()
                _ = spaceCache.getCurrentSpaceID()
                _ = uiOptimizer.optimizedAnimation(for: .spaceTransition)
            }
        }
        
        // 強制垃圾回收
        for _ in 1...3 {
            autoreleasepool {
                // 空的 autoreleasepool 來觸發清理
            }
        }
        
        let finalMemory = getCurrentMemoryUsage()
        let memoryIncrease = finalMemory - initialMemory
        
        // 允許一定的記憶體增長，但不應該過多
        XCTAssertLessThan(memoryIncrease, 50.0, "記憶體增長不應超過 50MB")
    }
    
    private func getCurrentMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / (1024 * 1024)
        }
        
        return 0
    }
}