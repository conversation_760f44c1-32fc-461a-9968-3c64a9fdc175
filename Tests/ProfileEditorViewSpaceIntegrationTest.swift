import SwiftUI
@testable import Workspace

/// ProfileEditorView Space 功能整合測試
struct ProfileEditorViewSpaceIntegrationTest {
    
    static func runIntegrationTest() {
        print("🔧 開始 ProfileEditorView Space 功能整合測試")
        
        let profileManager = ProfileManager.shared
        let spaceProfileManager = SpaceProfileManager.shared
        let spaceDetector = SpaceDetector.shared
        
        // 清理測試環境
        cleanupTestEnvironment()
        
        // 測試場景 1: 建立帶有 Space 資訊的 Profile
        print("📝 測試場景 1: 建立帶有 Space 資訊的 Profile")
        let testProfile1 = createTestProfile(name: "Integration Test Profile 1", spaceID: 1)
        profileManager.saveProfile(testProfile1)
        spaceProfileManager.saveProfileToSpace(testProfile1, spaceID: 1)
        
        // 驗證 Profile 正確儲存
        assert(profileManager.profiles.contains { $0.id == testProfile1.id }, "Profile 應該被儲存")
        assert(spaceProfileManager.getProfilesForSpace(1).contains { $0.id == testProfile1.id }, "Profile 應該在 Space 1 中")
        
        // 測試場景 2: 在 ProfileEditorView 中顯示 Space 資訊
        print("👁️ 測試場景 2: 在 ProfileEditorView 中顯示 Space 資訊")
        let editorView1 = ProfileEditorView(profile: testProfile1)
        
        // 驗證 Space 資訊顯示邏輯
        let spaceDisplayName = spaceDetector.getDisplayName(for: 1)
        assert(!spaceDisplayName.isEmpty, "Space 顯示名稱不應該為空")
        
        let statusText = getSpaceStatusText(for: testProfile1)
        let statusColor = getSpaceStatusColor(for: testProfile1)
        assert(!statusText.isEmpty, "狀態文字不應該為空")
        
        // 測試場景 3: 在 ProfileEditorView 中移動 Profile 到不同 Space
        print("🔄 測試場景 3: 在 ProfileEditorView 中移動 Profile 到不同 Space")
        
        // 模擬使用者選擇目標 Space
        let targetSpaceID = 2
        assert(spaceDetector.isSpaceAccessible(targetSpaceID), "目標 Space 應該是可存取的")
        
        // 執行移動操作
        spaceProfileManager.moveProfileBetweenSpaces(testProfile1, from: 1, to: targetSpaceID)
        
        // 驗證移動結果
        assert(!spaceProfileManager.getProfilesForSpace(1).contains { $0.id == testProfile1.id }, "Profile 不應該在原 Space 中")
        assert(spaceProfileManager.getProfilesForSpace(targetSpaceID).contains { $0.id == testProfile1.id }, "Profile 應該在目標 Space 中")
        
        // 測試場景 4: 處理沒有 Space 資訊的 Profile
        print("❓ 測試場景 4: 處理沒有 Space 資訊的 Profile")
        let testProfile2 = createTestProfile(name: "Integration Test Profile 2", spaceID: nil)
        profileManager.saveProfile(testProfile2)
        
        let editorView2 = ProfileEditorView(profile: testProfile2)
        
        // 驗證沒有 Space 資訊的處理
        let statusTextForNoSpace = getSpaceStatusText(for: testProfile2)
        assert(statusTextForNoSpace == "未指定", "沒有 Space 的 Profile 狀態應該是 '未指定'")
        
        // 測試場景 5: 驗證 Space 選擇器選項
        print("📋 測試場景 5: 驗證 Space 選擇器選項")
        let availableSpaces = spaceDetector.getAvailableSpaces()
        assert(!availableSpaces.isEmpty, "應該有可用的 Spaces")
        
        for space in availableSpaces {
            assert(spaceDetector.isSpaceAccessible(space.id), "列出的 Space 都應該是可存取的")
            assert(!space.displayName.isEmpty, "Space 顯示名稱不應該為空")
        }
        
        // 測試場景 6: 驗證移動限制
        print("🚫 測試場景 6: 驗證移動限制")
        
        // 嘗試移動到相同 Space (應該被阻止)
        let currentSpaceID = testProfile1.spaceID ?? targetSpaceID
        let canMoveToSameSpace = (currentSpaceID != currentSpaceID) // 應該是 false
        assert(!canMoveToSameSpace, "不應該允許移動到相同的 Space")
        
        // 嘗試移動到無效 Space (應該被阻止)
        let invalidSpaceID = 99
        assert(!spaceDetector.isSpaceAccessible(invalidSpaceID), "無效的 Space 應該不可存取")
        
        // 測試場景 7: 清理和驗證
        print("🧹 測試場景 7: 清理和驗證")
        profileManager.deleteProfile(testProfile1)
        profileManager.deleteProfile(testProfile2)
        
        // 驗證清理結果
        assert(!profileManager.profiles.contains { $0.id == testProfile1.id }, "Profile 1 應該被刪除")
        assert(!profileManager.profiles.contains { $0.id == testProfile2.id }, "Profile 2 應該被刪除")
        assert(!spaceProfileManager.getProfilesForSpace(targetSpaceID).contains { $0.id == testProfile1.id }, "Profile 1 應該從 Space 映射中移除")
        
        print("✅ ProfileEditorView Space 功能整合測試完成")
    }
    
    // MARK: - Helper Methods
    
    static func createTestProfile(name: String, spaceID: Int?) -> Profile {
        let testWindows = [
            WindowLayout(
                id: UUID().uuidString,
                app: "Integration Test App",
                title: "Integration Test Window",
                bundleID: "com.test.integration",
                frame: WindowFrame(x: 150, y: 150, w: 900, h: 650)
            ),
            WindowLayout(
                id: UUID().uuidString,
                app: "Secondary Test App",
                title: "Secondary Test Window",
                bundleID: "com.test.secondary",
                frame: WindowFrame(x: 300, y: 300, w: 600, h: 400)
            )
        ]
        
        return Profile(
            name: name,
            windows: testWindows,
            spaceID: spaceID
        )
    }
    
    static func cleanupTestEnvironment() {
        let profileManager = ProfileManager.shared
        let spaceProfileManager = SpaceProfileManager.shared
        
        // 移除所有測試 Profile
        let testProfiles = profileManager.profiles.filter { $0.name.contains("Integration Test") }
        for profile in testProfiles {
            profileManager.deleteProfile(profile)
        }
        
        // 清理 Space 映射
        spaceProfileManager.reloadMappings()
    }
    
    static func getSpaceStatusColor(for profile: Profile) -> Color {
        let spaceDetector = SpaceDetector.shared
        
        guard let spaceID = profile.spaceID else {
            return .orange
        }
        
        if spaceDetector.isCurrentSpace(spaceID) {
            return .green
        } else if spaceDetector.isSpaceAccessible(spaceID) {
            return .blue
        } else {
            return .red
        }
    }
    
    static func getSpaceStatusText(for profile: Profile) -> String {
        let spaceDetector = SpaceDetector.shared
        
        guard let spaceID = profile.spaceID else {
            return "未指定"
        }
        
        if spaceDetector.isCurrentSpace(spaceID) {
            return "當前 Space"
        } else if spaceDetector.isSpaceAccessible(spaceID) {
            return "可存取"
        } else {
            return "無法存取"
        }
    }
}

// MARK: - Test Execution

extension ProfileEditorViewSpaceIntegrationTest {
    
    /// 執行完整的整合測試套件
    static func executeFullTestSuite() {
        print("🚀 開始執行 ProfileEditorView Space 功能完整測試套件")
        
        do {
            runIntegrationTest()
            print("🎉 所有整合測試都通過了！")
            
            // 執行額外的驗證
            validateSpaceIntegration()
            print("✨ Space 整合驗證完成！")
            
        } catch {
            print("❌ 整合測試失敗: \(error)")
        }
    }
    
    static func validateSpaceIntegration() {
        print("🔍 執行 Space 整合驗證...")
        
        let spaceDetector = SpaceDetector.shared
        let spaceProfileManager = SpaceProfileManager.shared
        
        // 驗證 SpaceDetector 功能
        let currentSpace = spaceDetector.getCurrentSpace()
        let availableSpaces = spaceDetector.getAvailableSpaces()
        
        print("📊 當前 Space: \(currentSpace?.description ?? "未知")")
        print("📊 可用 Spaces: \(availableSpaces.map { $0.displayName }.joined(separator: ", "))")
        
        // 驗證 SpaceProfileManager 功能
        let spacesWithProfiles = spaceProfileManager.spacesWithProfiles
        print("📊 有 Profile 的 Spaces: \(spacesWithProfiles)")
        
        for spaceID in spacesWithProfiles {
            let profileCount = spaceProfileManager.getProfileCount(for: spaceID)
            print("📊 Space \(spaceID) 有 \(profileCount) 個 Profiles")
        }
        
        print("✅ Space 整合驗證完成")
    }
}