import XCTest
import SwiftUI
@testable import Workspace

/**
 * SpaceAwareProfileView 手動測試
 *
 * 提供手動測試 SpaceAwareProfileView 功能的測試案例
 * 這些測試可以用來驗證 UI 組件的基本功能
 *
 * @since 1.0.0
 * <AUTHOR> Integration Team
 */
class SpaceAwareProfileViewManualTest: XCTestCase {
    
    /**
     * 測試 SpaceAwareProfileView 基本初始化
     *
     * 驗證視圖能夠正確初始化而不會崩潰
     */
    func testSpaceAwareProfileViewInitialization() {
        // Given: 建立 SpaceAwareProfileView
        let view = SpaceAwareProfileView()
        
        // When: 嘗試初始化視圖
        let hostingController = NSHostingController(rootView: view)
        
        // Then: 驗證視圖成功初始化
        XCTAssertNotNil(hostingController)
        XCTAssertNotNil(hostingController.view)
        
        print("✅ SpaceAwareProfileView 初始化成功")
    }
    
    /**
     * 測試 SpaceAwareProfileRowView 基本功能
     *
     * 驗證設定檔行視圖能夠正確顯示設定檔資訊
     */
    func testSpaceAwareProfileRowView() {
        // Given: 建立測試設定檔和 Space 資訊
        let testProfile = createTestProfile()
        let testSpaceInfo = SpaceInfo(id: 1, name: "Space 1", isActive: true, displayName: "工作區 1")
        
        var restoreCalled = false
        var editCalled = false
        
        // When: 建立 SpaceAwareProfileRowView
        let rowView = SpaceAwareProfileRowView(
            profile: testProfile,
            spaceInfo: testSpaceInfo,
            onRestore: { restoreCalled = true },
            onEdit: { editCalled = true }
        )
        
        let hostingController = NSHostingController(rootView: rowView)
        
        // Then: 驗證視圖成功建立
        XCTAssertNotNil(hostingController)
        XCTAssertNotNil(hostingController.view)
        
        print("✅ SpaceAwareProfileRowView 建立成功")
        print("   - 設定檔名稱: \(testProfile.displayName)")
        print("   - Space 資訊: \(testSpaceInfo.displayName)")
    }
    
    /**
     * 測試空狀態視圖
     *
     * 驗證空 Space 狀態的正確顯示
     */
    func testEmptyStateView() {
        // Given: 設定空的測試環境
        let spaceDetector = SpaceDetector.shared
        let spaceProfileManager = SpaceProfileManager.shared
        
        // 清理測試資料
        spaceDetector.resetTestState()
        spaceProfileManager.clearAllMappings()
        
        // 設定測試 Spaces
        let testSpaces = [
            SpaceInfo(id: 1, name: "Space 1", isActive: true, displayName: "工作區 1")
        ]
        spaceDetector.setTestSpaces(testSpaces)
        spaceDetector.setCurrentSpace(1)
        
        // When: 建立 SpaceAwareProfileView
        let view = SpaceAwareProfileView()
        let hostingController = NSHostingController(rootView: view)
        
        // Then: 驗證空狀態視圖
        XCTAssertNotNil(hostingController)
        
        let profileCount = spaceProfileManager.getProfileCount(for: 1)
        XCTAssertEqual(profileCount, 0, "Space 1 應該沒有設定檔")
        
        print("✅ 空狀態視圖測試成功")
        print("   - Space 1 設定檔數量: \(profileCount)")
    }
    
    /**
     * 測試 Space 標籤整合
     *
     * 驗證 SpaceTabView 與 SpaceAwareProfileView 的整合
     */
    func testSpaceTabIntegration() {
        // Given: 設定測試 Spaces
        let spaceDetector = SpaceDetector.shared
        let testSpaces = [
            SpaceInfo(id: 1, name: "Space 1", isActive: true, displayName: "工作區 1"),
            SpaceInfo(id: 2, name: "Space 2", isActive: false, displayName: "工作區 2"),
            SpaceInfo(id: 3, name: "Space 3", isActive: false, displayName: "工作區 3")
        ]
        
        spaceDetector.setTestSpaces(testSpaces)
        spaceDetector.setCurrentSpace(1)
        
        // When: 建立包含 Space 標籤的視圖
        @State var selectedSpaceID: Int? = 1
        let spaceTabView = SpaceTabView(selectedSpaceID: .constant(selectedSpaceID))
        let hostingController = NSHostingController(rootView: spaceTabView)
        
        // Then: 驗證 Space 標籤整合
        XCTAssertNotNil(hostingController)
        XCTAssertEqual(spaceDetector.availableSpaces.count, 3)
        XCTAssertEqual(spaceDetector.currentSpaceID, 1)
        
        print("✅ Space 標籤整合測試成功")
        print("   - 可用 Spaces 數量: \(spaceDetector.availableSpaces.count)")
        print("   - 當前 Space ID: \(spaceDetector.currentSpaceID ?? -1)")
    }
    
    /**
     * 測試設定檔與 Space 的關聯
     *
     * 驗證設定檔能夠正確與 Space 關聯
     */
    func testProfileSpaceAssociation() {
        // Given: 設定測試環境
        let spaceDetector = SpaceDetector.shared
        let spaceProfileManager = SpaceProfileManager.shared
        
        let testSpaces = [
            SpaceInfo(id: 1, name: "Space 1", isActive: true, displayName: "工作區 1"),
            SpaceInfo(id: 2, name: "Space 2", isActive: false, displayName: "工作區 2")
        ]
        
        spaceDetector.setTestSpaces(testSpaces)
        spaceDetector.setCurrentSpace(1)
        
        // When: 建立並儲存測試設定檔
        let testProfile1 = createTestProfile(name: "測試設定檔1", spaceID: 1)
        let testProfile2 = createTestProfile(name: "測試設定檔2", spaceID: 2)
        
        spaceProfileManager.saveProfileToSpace(testProfile1, spaceID: 1)
        spaceProfileManager.saveProfileToSpace(testProfile2, spaceID: 2)
        
        // Then: 驗證設定檔與 Space 的關聯
        let space1Profiles = spaceProfileManager.getProfilesForSpace(1)
        let space2Profiles = spaceProfileManager.getProfilesForSpace(2)
        
        XCTAssertEqual(space1Profiles.count, 1)
        XCTAssertEqual(space2Profiles.count, 1)
        XCTAssertEqual(space1Profiles[0].name, "測試設定檔1")
        XCTAssertEqual(space2Profiles[0].name, "測試設定檔2")
        
        print("✅ 設定檔與 Space 關聯測試成功")
        print("   - Space 1 設定檔: \(space1Profiles.map { $0.name })")
        print("   - Space 2 設定檔: \(space2Profiles.map { $0.name })")
    }
    
    /**
     * 測試 MainMenuView 整合
     *
     * 驗證 SpaceAwareProfileView 與 MainMenuView 的整合
     */
    func testMainMenuViewIntegration() {
        // Given: 設定測試環境
        let spaceDetector = SpaceDetector.shared
        let testSpaces = [
            SpaceInfo(id: 1, name: "Space 1", isActive: true, displayName: "工作區 1")
        ]
        
        spaceDetector.setTestSpaces(testSpaces)
        spaceDetector.setCurrentSpace(1)
        
        // When: 建立 MainMenuView
        let mainMenuView = MainMenuView()
        let hostingController = NSHostingController(rootView: mainMenuView)
        
        // Then: 驗證整合成功
        XCTAssertNotNil(hostingController)
        XCTAssertNotNil(hostingController.view)
        
        print("✅ MainMenuView 整合測試成功")
        print("   - 視圖寬度: \(hostingController.view.frame.width)")
    }
    
    // MARK: - 輔助方法
    
    /**
     * 建立測試設定檔
     */
    private func createTestProfile(name: String = "測試設定檔", spaceID: Int? = nil) -> Profile {
        let testWindows = [
            WindowLayout(
                id: UUID().uuidString,
                app: "測試應用程式",
                title: "測試視窗",
                bundleID: "com.test.app",
                frame: WindowFrame(x: 100, y: 100, w: 800, h: 600)
            ),
            WindowLayout(
                id: UUID().uuidString,
                app: "另一個測試應用程式",
                title: "另一個測試視窗",
                bundleID: "com.test.app2",
                frame: WindowFrame(x: 200, y: 200, w: 600, h: 400)
            )
        ]
        
        return Profile(
            name: name,
            windows: testWindows,
            spaceID: spaceID,
            isSpaceSpecific: spaceID != nil,
            createdAt: Date(),
            modifiedAt: Date()
        )
    }
    
    override func setUp() {
        super.setUp()
        print("\n🧪 開始 SpaceAwareProfileView 手動測試")
    }
    
    override func tearDown() {
        // 清理測試資料
        SpaceDetector.shared.resetTestState()
        SpaceProfileManager.shared.clearAllMappings()
        print("🧹 測試清理完成\n")
        super.tearDown()
    }
}

// MARK: - 測試擴展

extension SpaceDetector {
    func setTestSpaces(_ spaces: [SpaceInfo]) {
        DispatchQueue.main.async {
            self.availableSpaces = spaces
        }
    }
    
    func setCurrentSpace(_ spaceID: Int) {
        DispatchQueue.main.async {
            self.currentSpaceID = spaceID
        }
    }
    
    func resetTestState() {
        DispatchQueue.main.async {
            self.availableSpaces = []
            self.currentSpaceID = nil
        }
    }
}

extension SpaceProfileManager {
    func clearAllMappings() {
        DispatchQueue.main.async {
            self.spaceProfileMapping.removeAll()
        }
    }
}