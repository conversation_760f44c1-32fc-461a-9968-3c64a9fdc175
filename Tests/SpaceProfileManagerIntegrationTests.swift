import XCTest
import Foundation
@testable import Workspace

/// 整合測試：驗證 SpaceProfileManager 與現有系統的整合
class SpaceProfileManagerIntegrationTests: XCTestCase {
    var spaceProfileManager: SpaceProfileManager!
    var profileManager: ProfileManager!
    var spaceDetector: SpaceDetector!
    
    override func setUp() {
        super.setUp()
        
        // 使用 shared instances 進行整合測試
        spaceProfileManager = SpaceProfileManager.shared
        profileManager = ProfileManager.shared
        spaceDetector = SpaceDetector.shared
    }
    
    // MARK: - 整合測試
    
    func testIntegrationWithProfileManager() {
        // 建立測試設定檔
        let testProfile = createTestProfile(name: "IntegrationTest", spaceID: 1)
        
        // 透過 SpaceProfileManager 儲存
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: 1)
        
        // 驗證 ProfileManager 中也有該設定檔
        let profileExists = profileManager.profiles.contains { $0.name == "IntegrationTest" }
        XCTAssertTrue(profileExists, "設定檔應該存在於 ProfileManager 中")
        
        // 驗證設定檔的 Space 資訊正確
        let savedProfile = profileManager.profiles.first { $0.name == "IntegrationTest" }
        XCTAssertNotNil(savedProfile)
        XCTAssertEqual(savedProfile?.spaceID, 1)
        XCTAssertTrue(savedProfile?.isSpaceSpecific == true)
    }
    
    func testIntegrationWithSpaceDetector() {
        // 測試與 SpaceDetector 的整合
        let currentSpaceID = spaceDetector.currentSpaceID ?? spaceDetector.getDefaultSpaceID()
        
        // 建立當前 Space 的設定檔
        let testProfile = createTestProfile(name: "CurrentSpaceTest", spaceID: currentSpaceID)
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: currentSpaceID)
        
        // 驗證可以正確獲取當前 Space 的設定檔
        let currentSpaceProfiles = spaceProfileManager.getProfilesForCurrentSpace()
        XCTAssertTrue(currentSpaceProfiles.contains { $0.name == "CurrentSpaceTest" })
    }
    
    func testSpaceAccessibilityIntegration() {
        // 測試 Space 可存取性檢查的整合
        let accessibleSpaces = spaceDetector.availableSpaces
        
        for space in accessibleSpaces {
            // 對每個可存取的 Space 建立設定檔
            let testProfile = createTestProfile(name: "AccessibilityTest\(space.id)", spaceID: space.id)
            spaceProfileManager.saveProfileToSpace(testProfile, spaceID: space.id)
            
            // 驗證設定檔已正確儲存
            let spaceProfiles = spaceProfileManager.getProfilesForSpace(space.id)
            XCTAssertTrue(spaceProfiles.contains { $0.name == "AccessibilityTest\(space.id)" })
        }
    }
    
    func testProfileDeletionIntegration() {
        // 測試刪除設定檔的整合
        let testProfile = createTestProfile(name: "DeletionTest", spaceID: 2)
        
        // 儲存設定檔
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: 2)
        
        // 驗證設定檔存在
        XCTAssertTrue(profileManager.profiles.contains { $0.name == "DeletionTest" })
        XCTAssertTrue(spaceProfileManager.getProfilesForSpace(2).contains { $0.name == "DeletionTest" })
        
        // 透過 SpaceProfileManager 刪除
        spaceProfileManager.deleteProfileFromSpace(testProfile, spaceID: 2)
        
        // 驗證設定檔已從兩個管理器中刪除
        XCTAssertFalse(profileManager.profiles.contains { $0.name == "DeletionTest" })
        XCTAssertFalse(spaceProfileManager.getProfilesForSpace(2).contains { $0.name == "DeletionTest" })
    }
    
    func testMappingPersistenceIntegration() {
        // 測試映射持久化的整合
        let testProfile1 = createTestProfile(name: "PersistenceTest1", spaceID: 1)
        let testProfile2 = createTestProfile(name: "PersistenceTest2", spaceID: 2)
        
        // 儲存設定檔到不同 Spaces
        spaceProfileManager.saveProfileToSpace(testProfile1, spaceID: 1)
        spaceProfileManager.saveProfileToSpace(testProfile2, spaceID: 2)
        
        // 重新載入映射
        spaceProfileManager.reloadMappings()
        
        // 驗證映射仍然正確
        XCTAssertTrue(spaceProfileManager.getProfilesForSpace(1).contains { $0.name == "PersistenceTest1" })
        XCTAssertTrue(spaceProfileManager.getProfilesForSpace(2).contains { $0.name == "PersistenceTest2" })
    }
    
    func testLegacyProfileMigrationIntegration() {
        // 測試舊格式設定檔的整合
        let legacyProfile = Profile(
            name: "LegacyProfile",
            windows: createTestWindows(),
            isSpaceSpecific: true,
            spaceID: nil // 舊格式沒有 spaceID
        )
        
        // 透過 ProfileManager 儲存舊格式設定檔
        profileManager.saveProfile(legacyProfile)
        
        // 重新載入映射，應該將舊格式設定檔分配到預設 Space
        spaceProfileManager.reloadMappings()
        
        // 驗證舊格式設定檔已被正確處理
        let defaultSpaceID = spaceDetector.getDefaultSpaceID()
        let defaultSpaceProfiles = spaceProfileManager.getProfilesForSpace(defaultSpaceID)
        XCTAssertTrue(defaultSpaceProfiles.contains { $0.name == "LegacyProfile" })
    }
    
    // MARK: - 效能整合測試
    
    func testPerformanceIntegrationWithLargeDataset() {
        measure {
            // 建立大量設定檔測試效能
            for i in 1...50 {
                let profile = createTestProfile(name: "PerfIntegrationTest\(i)", spaceID: (i % 3) + 1)
                spaceProfileManager.saveProfileToSpace(profile, spaceID: (i % 3) + 1)
            }
            
            // 測試各種操作的效能
            for spaceID in 1...3 {
                _ = spaceProfileManager.getProfilesForSpace(spaceID)
                _ = spaceProfileManager.getProfileCount(for: spaceID)
                _ = spaceProfileManager.hasProfiles(in: spaceID)
            }
            
            // 測試重新載入效能
            spaceProfileManager.reloadMappings()
        }
    }
    
    // MARK: - 輔助方法
    
    private func createTestProfile(name: String, spaceID: Int?) -> Profile {
        return Profile(
            name: name,
            windows: createTestWindows(),
            isSpaceSpecific: spaceID != nil,
            spaceID: spaceID
        )
    }
    
    private func createTestWindows() -> [WindowLayout] {
        return [
            WindowLayout(
                app: "TestApp",
                bundleID: "com.test.app",
                title: "Test Window",
                frame: WindowLayout.WindowFrame(x: 100, y: 100, w: 800, h: 600)
            ),
            WindowLayout(
                app: "AnotherApp",
                bundleID: "com.test.another",
                title: "Another Window",
                frame: WindowLayout.WindowFrame(x: 200, y: 200, w: 600, h: 400)
            )
        ]
    }
    
    private func waitForAsyncOperation(timeout: TimeInterval = 1.0) {
        let expectation = XCTestExpectation(description: "Async operation")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: timeout)
    }
}