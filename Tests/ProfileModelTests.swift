import Testing
import Foundation
@testable import Workspace

@Suite("Profile Model Tests")
struct ProfileModelTests {
    
    // MARK: - Test Data
    
    private func createTestWindowLayouts() -> [WindowLayout] {
        return [
            WindowLayout(
                app: "Safari",
                bundleID: "com.apple.Safari",
                title: "Test Page 1",
                frame: WindowLayout.WindowFrame(x: 100, y: 100, w: 800, h: 600)
            ),
            WindowLayout(
                app: "Xcode",
                bundleID: "com.apple.dt.Xcode",
                title: "Test Project",
                frame: WindowLayout.WindowFrame(x: 200, y: 200, w: 1200, h: 800)
            )
        ]
    }
    
    // MARK: - Initialization Tests
    
    @Test("Profile 初始化 - 基本初始化")
    func testProfileBasicInitialization() {
        let windows = createTestWindowLayouts()
        let profile = Profile(name: "Test Profile", windows: windows)
        
        #expect(profile.name == "Test Profile")
        #expect(profile.windows.count == 2)
        #expect(profile.isSpaceSpecific == false)
        #expect(profile.spaceID == nil)
        #expect(profile.createdAt <= Date())
        #expect(profile.modifiedAt <= Date())
    }
    
    @Test("Profile 初始化 - 帶 Space ID")
    func testProfileInitializationWithSpaceID() {
        let windows = createTestWindowLayouts()
        let spaceID = 2
        let profile = Profile(name: "Space Profile", windows: windows, isSpaceSpecific: true, spaceID: spaceID)
        
        #expect(profile.name == "Space Profile")
        #expect(profile.windows.count == 2)
        #expect(profile.isSpaceSpecific == true)
        #expect(profile.spaceID == spaceID)
        #expect(profile.createdAt <= Date())
        #expect(profile.modifiedAt <= Date())
    }
    
    @Test("Profile 初始化 - 預設參數")
    func testProfileInitializationWithDefaults() {
        let profile = Profile(name: "Default Profile")
        
        #expect(profile.name == "Default Profile")
        #expect(profile.windows.isEmpty)
        #expect(profile.isSpaceSpecific == false)
        #expect(profile.spaceID == nil)
    }
    
    // MARK: - JSON Encoding/Decoding Tests
    
    @Test("Profile JSON 編碼 - 基本設定檔")
    func testProfileJSONEncodingBasic() throws {
        let windows = createTestWindowLayouts()
        let profile = Profile(name: "Test Profile", windows: windows)
        
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let data = try encoder.encode(profile)
        
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        let decodedProfile = try decoder.decode(Profile.self, from: data)
        
        #expect(decodedProfile.name == profile.name)
        #expect(decodedProfile.windows.count == profile.windows.count)
        #expect(decodedProfile.isSpaceSpecific == profile.isSpaceSpecific)
        #expect(decodedProfile.spaceID == profile.spaceID)
    }
    
    @Test("Profile JSON 編碼 - 帶 Space ID")
    func testProfileJSONEncodingWithSpaceID() throws {
        let windows = createTestWindowLayouts()
        let spaceID = 3
        let profile = Profile(name: "Space Profile", windows: windows, isSpaceSpecific: true, spaceID: spaceID)
        
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let data = try encoder.encode(profile)
        
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        let decodedProfile = try decoder.decode(Profile.self, from: data)
        
        #expect(decodedProfile.name == profile.name)
        #expect(decodedProfile.windows.count == profile.windows.count)
        #expect(decodedProfile.isSpaceSpecific == profile.isSpaceSpecific)
        #expect(decodedProfile.spaceID == spaceID)
    }
    
    @Test("Profile JSON 編碼 - 向後相容性")
    func testProfileJSONBackwardCompatibility() throws {
        // 模擬舊版本的 JSON 資料（沒有 spaceID 欄位）
        let jsonString = """
        {
            "name": "Legacy Profile",
            "windows": [],
            "createdAt": "2025-01-08T10:00:00Z",
            "modifiedAt": "2025-01-08T10:00:00Z",
            "isSpaceSpecific": false
        }
        """
        
        let data = jsonString.data(using: .utf8)!
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        let profile = try decoder.decode(Profile.self, from: data)
        
        #expect(profile.name == "Legacy Profile")
        #expect(profile.spaceID == nil)
        #expect(profile.isSpaceSpecific == false)
    }
    
    // MARK: - Property Tests
    
    @Test("Profile jsonFileName - 基本設定檔")
    func testProfileJSONFileNameBasic() {
        let profile = Profile(name: "Test Profile")
        #expect(profile.jsonFileName == "Test Profile.json")
    }
    
    @Test("Profile jsonFileName - Space 專用設定檔")
    func testProfileJSONFileNameSpaceSpecific() {
        let profile = Profile(name: "Space Profile", isSpaceSpecific: true)
        #expect(profile.jsonFileName == "Space Profile_space.json")
    }
    
    @Test("Profile jsonFileName - 帶 Space ID")
    func testProfileJSONFileNameWithSpaceID() {
        let profile = Profile(name: "Space Profile", spaceID: 2)
        #expect(profile.jsonFileName == "Space Profile_space2.json")
    }
    
    @Test("Profile previewDescription - 基本設定檔")
    func testProfilePreviewDescriptionBasic() {
        let windows = createTestWindowLayouts()
        let profile = Profile(name: "Test Profile", windows: windows)
        #expect(profile.previewDescription == "2 個視窗 • 全域")
    }
    
    @Test("Profile previewDescription - Space 專用設定檔")
    func testProfilePreviewDescriptionSpaceSpecific() {
        let windows = createTestWindowLayouts()
        let profile = Profile(name: "Space Profile", windows: windows, isSpaceSpecific: true)
        #expect(profile.previewDescription == "2 個視窗 • Space 專用")
    }
    
    @Test("Profile previewDescription - 帶 Space ID")
    func testProfilePreviewDescriptionWithSpaceID() {
        let windows = createTestWindowLayouts()
        let profile = Profile(name: "Space Profile", windows: windows, spaceID: 3)
        #expect(profile.previewDescription == "2 個視窗 • Space 3")
    }
    
    // MARK: - Space-related Method Tests
    
    @Test("Profile belongsToSpace - 匹配的 Space")
    func testProfileBelongsToSpaceMatching() {
        let profile = Profile(name: "Space Profile", spaceID: 2)
        #expect(profile.belongsToSpace(2) == true)
    }
    
    @Test("Profile belongsToSpace - 不匹配的 Space")
    func testProfileBelongsToSpaceNotMatching() {
        let profile = Profile(name: "Space Profile", spaceID: 2)
        #expect(profile.belongsToSpace(3) == false)
    }
    
    @Test("Profile belongsToSpace - 沒有 Space ID")
    func testProfileBelongsToSpaceNoSpaceID() {
        let profile = Profile(name: "Regular Profile")
        #expect(profile.belongsToSpace(2) == false)
    }
    
    @Test("Profile isSpaceAware - 有 Space ID")
    func testProfileIsSpaceAwareWithSpaceID() {
        let profile = Profile(name: "Space Profile", spaceID: 2)
        #expect(profile.isSpaceAware == true)
    }
    
    @Test("Profile isSpaceAware - Space 專用但沒有 ID")
    func testProfileIsSpaceAwareSpaceSpecific() {
        let profile = Profile(name: "Space Profile", isSpaceSpecific: true)
        #expect(profile.isSpaceAware == true)
    }
    
    @Test("Profile isSpaceAware - 一般設定檔")
    func testProfileIsSpaceAwareRegular() {
        let profile = Profile(name: "Regular Profile")
        #expect(profile.isSpaceAware == false)
    }
    
    // MARK: - Equatable Tests
    
    @Test("Profile Equatable - 相同設定檔")
    func testProfileEquatableSame() {
        let windows = createTestWindowLayouts()
        let profile1 = Profile(name: "Test Profile", windows: windows, spaceID: 2)
        let profile2 = Profile(name: "Test Profile", windows: windows, spaceID: 2)
        
        // 注意：由於 createdAt 和 modifiedAt 會不同，這個測試可能會失敗
        // 我們需要手動設置相同的時間戳
        var profile2Modified = profile2
        profile2Modified.createdAt = profile1.createdAt
        profile2Modified.modifiedAt = profile1.modifiedAt
        
        #expect(profile1 == profile2Modified)
    }
    
    @Test("Profile Equatable - 不同 Space ID")
    func testProfileEquatableDifferentSpaceID() {
        let windows = createTestWindowLayouts()
        let profile1 = Profile(name: "Test Profile", windows: windows, spaceID: 2)
        let profile2 = Profile(name: "Test Profile", windows: windows, spaceID: 3)
        
        #expect(profile1 != profile2)
    }
    
    // MARK: - Edge Cases
    
    @Test("Profile 邊界情況 - 空名稱")
    func testProfileEdgeCaseEmptyName() {
        let profile = Profile(name: "", spaceID: 1)
        #expect(profile.name == "")
        #expect(profile.id == "") // ID 基於名稱
        #expect(profile.spaceID == 1)
    }
    
    @Test("Profile 邊界情況 - 負數 Space ID")
    func testProfileEdgeCaseNegativeSpaceID() {
        let profile = Profile(name: "Test Profile", spaceID: -1)
        #expect(profile.spaceID == -1)
        #expect(profile.belongsToSpace(-1) == true)
        #expect(profile.belongsToSpace(1) == false)
    }
    
    @Test("Profile 邊界情況 - 零 Space ID")
    func testProfileEdgeCaseZeroSpaceID() {
        let profile = Profile(name: "Test Profile", spaceID: 0)
        #expect(profile.spaceID == 0)
        #expect(profile.belongsToSpace(0) == true)
        #expect(profile.isSpaceAware == true)
    }
}