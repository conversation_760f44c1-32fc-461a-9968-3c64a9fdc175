import SwiftUI
@testable import Workspace

/// ProfileEditorView Space 功能演示
struct ProfileEditorViewSpaceDemo {
    
    static func demonstrateSpaceFeatures() {
        print("🎬 ProfileEditorView Space 功能演示開始")
        print("=" * 50)
        
        demonstrateSpaceInformationDisplay()
        demonstrateSpaceMovementUI()
        demonstrateSpaceStatusIndicators()
        demonstrateSpaceValidation()
        
        print("=" * 50)
        print("🎉 ProfileEditorView Space 功能演示完成")
    }
    
    // MARK: - Demo Scenarios
    
    static func demonstrateSpaceInformationDisplay() {
        print("\n📋 演示 1: Space 資訊顯示")
        print("-" * 30)
        
        // 建立示例 Profile
        let demoProfile = createDemoProfile(name: "工作區佈局", spaceID: 1)
        
        print("Profile 名稱: \(demoProfile.name)")
        print("所屬 Space: \(demoProfile.spaceID?.description ?? "未指定")")
        print("Space 特定: \(demoProfile.isSpaceSpecific ? "是" : "否")")
        print("視窗數量: \(demoProfile.windows.count)")
        
        // 模擬 ProfileEditorView 中的 Space 資訊顯示
        let spaceDetector = SpaceDetector.shared
        if let spaceID = demoProfile.spaceID {
            let displayName = spaceDetector.getDisplayName(for: spaceID)
            let isCurrentSpace = spaceDetector.isCurrentSpace(spaceID)
            let isAccessible = spaceDetector.isSpaceAccessible(spaceID)
            
            print("Space 顯示名稱: \(displayName)")
            print("是否為當前 Space: \(isCurrentSpace ? "是" : "否")")
            print("是否可存取: \(isAccessible ? "是" : "否")")
        }
        
        print("✅ Space 資訊顯示演示完成")
    }
    
    static func demonstrateSpaceMovementUI() {
        print("\n🔄 演示 2: Space 移動 UI")
        print("-" * 30)
        
        let profileManager = ProfileManager.shared
        let spaceProfileManager = SpaceProfileManager.shared
        let spaceDetector = SpaceDetector.shared
        
        // 建立示例 Profile
        let demoProfile = createDemoProfile(name: "開發環境佈局", spaceID: 1)
        
        // 儲存到 Space 1
        profileManager.saveProfile(demoProfile)
        spaceProfileManager.saveProfileToSpace(demoProfile, spaceID: 1)
        
        print("原始 Space: 1")
        print("可用的目標 Spaces:")
        
        let availableSpaces = spaceDetector.getAvailableSpaces()
        for space in availableSpaces {
            if space.id != demoProfile.spaceID {
                print("  - \(space.displayName) (ID: \(space.id))")
            }
        }
        
        // 模擬移動到 Space 2
        let targetSpaceID = 2
        if spaceDetector.isSpaceAccessible(targetSpaceID) {
            print("\n執行移動操作: Space 1 → Space 2")
            spaceProfileManager.moveProfileBetweenSpaces(demoProfile, from: 1, to: targetSpaceID)
            
            // 驗證移動結果
            let profilesInSpace1 = spaceProfileManager.getProfilesForSpace(1)
            let profilesInSpace2 = spaceProfileManager.getProfilesForSpace(2)
            
            print("Space 1 中的 Profiles: \(profilesInSpace1.count)")
            print("Space 2 中的 Profiles: \(profilesInSpace2.count)")
            print("移動成功: \(profilesInSpace2.contains { $0.id == demoProfile.id } ? "是" : "否")")
        }
        
        // 清理
        profileManager.deleteProfile(demoProfile)
        
        print("✅ Space 移動 UI 演示完成")
    }
    
    static func demonstrateSpaceStatusIndicators() {
        print("\n🚦 演示 3: Space 狀態指示器")
        print("-" * 30)
        
        let spaceDetector = SpaceDetector.shared
        
        // 建立不同狀態的示例 Profiles
        let profiles = [
            ("當前 Space Profile", spaceDetector.currentSpaceID),
            ("其他 Space Profile", 2),
            ("未指定 Space Profile", nil)
        ]
        
        for (name, spaceID) in profiles {
            let profile = createDemoProfile(name: name, spaceID: spaceID)
            
            print("\nProfile: \(profile.name)")
            print("Space ID: \(profile.spaceID?.description ?? "未指定")")
            
            let statusText = getSpaceStatusText(for: profile)
            let statusColor = getSpaceStatusColor(for: profile)
            
            print("狀態文字: \(statusText)")
            print("狀態顏色: \(getColorName(statusColor))")
            
            // 顯示狀態圖示
            let statusIcon = getStatusIcon(for: profile)
            print("狀態圖示: \(statusIcon)")
        }
        
        print("✅ Space 狀態指示器演示完成")
    }
    
    static func demonstrateSpaceValidation() {
        print("\n✅ 演示 4: Space 驗證邏輯")
        print("-" * 30)
        
        let spaceDetector = SpaceDetector.shared
        let demoProfile = createDemoProfile(name: "驗證測試 Profile", spaceID: 1)
        
        // 測試各種驗證情況
        let validationTests = [
            ("移動到相同 Space", 1, false),
            ("移動到有效 Space", 2, spaceDetector.isSpaceAccessible(2)),
            ("移動到無效 Space", 99, false)
        ]
        
        for (testName, targetSpaceID, expectedValid) in validationTests {
            let isValid = validateSpaceMovement(from: demoProfile.spaceID ?? 1, to: targetSpaceID)
            let result = isValid == expectedValid ? "✅ 通過" : "❌ 失敗"
            
            print("\(testName): \(result)")
            print("  目標 Space: \(targetSpaceID)")
            print("  預期有效: \(expectedValid)")
            print("  實際有效: \(isValid)")
        }
        
        print("✅ Space 驗證邏輯演示完成")
    }
    
    // MARK: - Helper Methods
    
    static func createDemoProfile(name: String, spaceID: Int?) -> Profile {
        let demoWindows = [
            WindowLayout(
                id: UUID().uuidString,
                app: "Xcode",
                title: "Workspace.xcodeproj",
                bundleID: "com.apple.dt.Xcode",
                frame: WindowFrame(x: 0, y: 0, w: 1200, h: 800)
            ),
            WindowLayout(
                id: UUID().uuidString,
                app: "Terminal",
                title: "zsh",
                bundleID: "com.apple.Terminal",
                frame: WindowFrame(x: 1200, y: 0, w: 600, h: 400)
            ),
            WindowLayout(
                id: UUID().uuidString,
                app: "Safari",
                title: "Documentation",
                bundleID: "com.apple.Safari",
                frame: WindowFrame(x: 1200, y: 400, w: 600, h: 400)
            )
        ]
        
        return Profile(
            name: name,
            windows: demoWindows,
            spaceID: spaceID
        )
    }
    
    static func getSpaceStatusText(for profile: Profile) -> String {
        let spaceDetector = SpaceDetector.shared
        
        guard let spaceID = profile.spaceID else {
            return "未指定"
        }
        
        if spaceDetector.isCurrentSpace(spaceID) {
            return "當前 Space"
        } else if spaceDetector.isSpaceAccessible(spaceID) {
            return "可存取"
        } else {
            return "無法存取"
        }
    }
    
    static func getSpaceStatusColor(for profile: Profile) -> Color {
        let spaceDetector = SpaceDetector.shared
        
        guard let spaceID = profile.spaceID else {
            return .orange
        }
        
        if spaceDetector.isCurrentSpace(spaceID) {
            return .green
        } else if spaceDetector.isSpaceAccessible(spaceID) {
            return .blue
        } else {
            return .red
        }
    }
    
    static func getColorName(_ color: Color) -> String {
        switch color {
        case .green: return "綠色 (當前)"
        case .blue: return "藍色 (可存取)"
        case .red: return "紅色 (無法存取)"
        case .orange: return "橙色 (未指定)"
        default: return "未知"
        }
    }
    
    static func getStatusIcon(for profile: Profile) -> String {
        let spaceDetector = SpaceDetector.shared
        
        guard let spaceID = profile.spaceID else {
            return "❓"
        }
        
        if spaceDetector.isCurrentSpace(spaceID) {
            return "🟢"
        } else if spaceDetector.isSpaceAccessible(spaceID) {
            return "🔵"
        } else {
            return "🔴"
        }
    }
    
    static func validateSpaceMovement(from sourceSpaceID: Int, to targetSpaceID: Int) -> Bool {
        let spaceDetector = SpaceDetector.shared
        
        // 不能移動到相同 Space
        if sourceSpaceID == targetSpaceID {
            return false
        }
        
        // 目標 Space 必須可存取
        return spaceDetector.isSpaceAccessible(targetSpaceID)
    }
}

// MARK: - Demo Runner

extension ProfileEditorViewSpaceDemo {
    
    /// 執行完整的功能演示
    static func runFullDemo() {
        print("🎭 開始 ProfileEditorView Space 功能完整演示")
        
        demonstrateSpaceFeatures()
        
        print("\n📊 系統狀態摘要:")
        printSystemStatus()
        
        print("\n💡 使用提示:")
        printUsageTips()
    }
    
    static func printSystemStatus() {
        let spaceDetector = SpaceDetector.shared
        let spaceProfileManager = SpaceProfileManager.shared
        
        print("當前 Space: \(spaceDetector.currentSpaceID?.description ?? "未知")")
        print("可用 Spaces: \(spaceDetector.availableSpaces.count)")
        print("有 Profile 的 Spaces: \(spaceProfileManager.spacesWithProfiles.count)")
        
        for spaceID in spaceProfileManager.spacesWithProfiles {
            let count = spaceProfileManager.getProfileCount(for: spaceID)
            print("  Space \(spaceID): \(count) 個 Profiles")
        }
    }
    
    static func printUsageTips() {
        print("1. 在 ProfileEditorView 中，Space 資訊會顯示在 Profile 基本資訊下方")
        print("2. 使用下拉選單選擇目標 Space，然後點擊「移動」按鈕")
        print("3. 狀態指示器會顯示 Profile 所屬 Space 的當前狀態")
        print("4. 只能移動到可存取的不同 Space")
        print("5. 移動操作會有確認對話框以防止意外操作")
    }
}