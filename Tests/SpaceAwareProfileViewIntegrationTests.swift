import XCTest
import SwiftUI
@testable import Workspace

/**
 * SpaceAwareProfileView 整合測試
 *
 * 測試 SpaceAwareProfileView 與其他系統組件的整合功能
 *
 * ## 測試範圍
 * - 與 SpaceProfileManager 的整合
 * - 與 SpaceDetector 的整合
 * - 與 HammerspoonManager 的整合
 * - 完整的使用者工作流程測試
 *
 * @since 1.0.0
 * <AUTHOR> Integration Team
 */
class SpaceAwareProfileViewIntegrationTests: XCTestCase {
    
    var spaceProfileManager: SpaceProfileManager!
    var spaceDetector: SpaceDetector!
    var profileManager: ProfileManager!
    var hammerspoonManager: HammerspoonManager!
    
    override func setUp() {
        super.setUp()
        
        // 設定測試環境
        spaceProfileManager = SpaceProfileManager.shared
        spaceDetector = SpaceDetector.shared
        profileManager = ProfileManager.shared
        hammerspoonManager = HammerspoonManager.shared
        
        // 清理測試資料
        clearTestData()
        
        // 設定測試 Spaces
        setupTestSpaces()
    }
    
    override func tearDown() {
        // 清理測試資料
        clearTestData()
        super.tearDown()
    }
    
    // MARK: - 整合測試
    
    /**
     * 測試完整的 Space 分組工作流程
     *
     * 驗證從建立設定檔到 Space 分組顯示的完整流程
     */
    func testCompleteSpaceGroupingWorkflow() {
        // Given: 設定測試環境
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        spaceDetector.setCurrentSpace(1)
        
        // When: 建立並儲存設定檔到不同 Spaces
        let profile1 = createTestProfile(name: "工作設定檔", spaceID: 1)
        let profile2 = createTestProfile(name: "娛樂設定檔", spaceID: 2)
        let profile3 = createTestProfile(name: "開發設定檔", spaceID: 1)
        
        spaceProfileManager.saveProfileToSpace(profile1, spaceID: 1)
        spaceProfileManager.saveProfileToSpace(profile2, spaceID: 2)
        spaceProfileManager.saveProfileToSpace(profile3, spaceID: 1)
        
        // Then: 驗證 Space 分組
        let space1Profiles = spaceProfileManager.getProfilesForSpace(1)
        let space2Profiles = spaceProfileManager.getProfilesForSpace(2)
        let space3Profiles = spaceProfileManager.getProfilesForSpace(3)
        
        XCTAssertEqual(space1Profiles.count, 2, "Space 1 應該有 2 個設定檔")
        XCTAssertEqual(space2Profiles.count, 1, "Space 2 應該有 1 個設定檔")
        XCTAssertEqual(space3Profiles.count, 0, "Space 3 應該沒有設定檔")
        
        // 驗證設定檔內容
        XCTAssertTrue(space1Profiles.contains { $0.name == "工作設定檔" })
        XCTAssertTrue(space1Profiles.contains { $0.name == "開發設定檔" })
        XCTAssertTrue(space2Profiles.contains { $0.name == "娛樂設定檔" })
    }
    
    /**
     * 測試 Space 切換時的 UI 更新
     *
     * 驗證切換 Space 時設定檔列表能夠正確更新
     */
    func testSpaceSwitchingUIUpdate() {
        // Given: 設定不同 Space 的設定檔
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        
        let profile1 = createTestProfile(name: "Space1設定檔", spaceID: 1)
        let profile2 = createTestProfile(name: "Space2設定檔", spaceID: 2)
        
        spaceProfileManager.saveProfileToSpace(profile1, spaceID: 1)
        spaceProfileManager.saveProfileToSpace(profile2, spaceID: 2)
        
        // When & Then: 測試 Space 切換
        spaceDetector.setCurrentSpace(1)
        let space1Profiles = spaceProfileManager.getProfilesForCurrentSpace()
        XCTAssertEqual(space1Profiles.count, 1)
        XCTAssertEqual(space1Profiles[0].name, "Space1設定檔")
        
        spaceDetector.setCurrentSpace(2)
        let space2Profiles = spaceProfileManager.getProfilesForCurrentSpace()
        XCTAssertEqual(space2Profiles.count, 1)
        XCTAssertEqual(space2Profiles[0].name, "Space2設定檔")
    }
    
    /**
     * 測試空 Space 狀態處理
     *
     * 驗證空 Space 的正確處理和顯示
     */
    func testEmptySpaceHandling() {
        // Given: 設定空的 Space
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        spaceDetector.setCurrentSpace(3) // Space 3 沒有設定檔
        
        // When: 檢查空 Space 狀態
        let space3Profiles = spaceProfileManager.getProfilesForCurrentSpace()
        let hasProfiles = spaceProfileManager.hasProfiles(in: 3)
        let profileCount = spaceProfileManager.getProfileCount(for: 3)
        
        // Then: 驗證空狀態
        XCTAssertTrue(space3Profiles.isEmpty, "Space 3 應該沒有設定檔")
        XCTAssertFalse(hasProfiles, "Space 3 不應該有設定檔")
        XCTAssertEqual(profileCount, 0, "Space 3 的設定檔數量應該為 0")
    }
    
    /**
     * 測試設定檔在 Spaces 間移動
     *
     * 驗證設定檔能夠正確在不同 Space 間移動
     */
    func testProfileMovementBetweenSpaces() {
        // Given: 建立測試設定檔
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        
        let profile = createTestProfile(name: "可移動設定檔", spaceID: 1)
        spaceProfileManager.saveProfileToSpace(profile, spaceID: 1)
        
        // 驗證初始狀態
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: 1), 1)
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: 2), 0)
        
        // When: 移動設定檔從 Space 1 到 Space 2
        spaceProfileManager.moveProfileBetweenSpaces(profile, from: 1, to: 2)
        
        // Then: 驗證移動結果
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: 1), 0, "Space 1 應該沒有設定檔")
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: 2), 1, "Space 2 應該有 1 個設定檔")
        
        let space2Profiles = spaceProfileManager.getProfilesForSpace(2)
        XCTAssertEqual(space2Profiles[0].name, "可移動設定檔")
        XCTAssertEqual(space2Profiles[0].spaceID, 2)
    }
    
    /**
     * 測試跨 Space 還原警告機制
     *
     * 驗證跨 Space 還原設定檔時的警告機制
     */
    func testCrossSpaceRestoreWarning() {
        // Given: 建立跨 Space 的情況
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        
        let profile = createTestProfile(name: "跨Space設定檔", spaceID: 1)
        spaceProfileManager.saveProfileToSpace(profile, spaceID: 1)
        
        // 切換到不同的 Space
        spaceDetector.setCurrentSpace(2)
        
        // When: 檢查跨 Space 情況
        let profileSpaceID = spaceProfileManager.getSpaceID(for: profile)
        let currentSpaceID = spaceDetector.currentSpaceID
        let isInCurrentSpace = spaceProfileManager.isProfileInSpace(profile, spaceID: 2)
        
        // Then: 驗證跨 Space 檢測
        XCTAssertEqual(profileSpaceID, 1, "設定檔應該屬於 Space 1")
        XCTAssertEqual(currentSpaceID, 2, "當前應該在 Space 2")
        XCTAssertFalse(isInCurrentSpace, "設定檔不應該在當前 Space 中")
        XCTAssertNotEqual(profileSpaceID, currentSpaceID, "應該偵測到跨 Space 情況")
    }
    
    /**
     * 測試設定檔統計資訊
     *
     * 驗證各種統計資訊的正確性
     */
    func testProfileStatistics() {
        // Given: 建立多個設定檔
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        
        let profiles = [
            createTestProfile(name: "設定檔1", spaceID: 1),
            createTestProfile(name: "設定檔2", spaceID: 1),
            createTestProfile(name: "設定檔3", spaceID: 2),
            createTestProfile(name: "設定檔4", spaceID: 2),
            createTestProfile(name: "設定檔5", spaceID: 2)
        ]
        
        for profile in profiles {
            if let spaceID = profile.spaceID {
                spaceProfileManager.saveProfileToSpace(profile, spaceID: spaceID)
            }
        }
        
        // When & Then: 驗證統計資訊
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: 1), 2)
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: 2), 3)
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: 3), 0)
        
        XCTAssertTrue(spaceProfileManager.hasProfiles(in: 1))
        XCTAssertTrue(spaceProfileManager.hasProfiles(in: 2))
        XCTAssertFalse(spaceProfileManager.hasProfiles(in: 3))
        
        let spacesWithProfiles = spaceProfileManager.spacesWithProfiles
        XCTAssertEqual(spacesWithProfiles.sorted(), [1, 2])
    }
    
    /**
     * 測試資料持久化
     *
     * 驗證 Space-Profile 映射的儲存和載入
     */
    func testDataPersistence() {
        // Given: 建立測試資料
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        
        let profile1 = createTestProfile(name: "持久化測試1", spaceID: 1)
        let profile2 = createTestProfile(name: "持久化測試2", spaceID: 2)
        
        spaceProfileManager.saveProfileToSpace(profile1, spaceID: 1)
        spaceProfileManager.saveProfileToSpace(profile2, spaceID: 2)
        
        // 記錄初始狀態
        let initialSpace1Count = spaceProfileManager.getProfileCount(for: 1)
        let initialSpace2Count = spaceProfileManager.getProfileCount(for: 2)
        
        // When: 重新載入映射
        spaceProfileManager.reloadMappings()
        
        // Then: 驗證資料持久化
        let reloadedSpace1Count = spaceProfileManager.getProfileCount(for: 1)
        let reloadedSpace2Count = spaceProfileManager.getProfileCount(for: 2)
        
        XCTAssertEqual(reloadedSpace1Count, initialSpace1Count, "Space 1 的設定檔數量應該保持一致")
        XCTAssertEqual(reloadedSpace2Count, initialSpace2Count, "Space 2 的設定檔數量應該保持一致")
    }
    
    // MARK: - 輔助方法
    
    /**
     * 清理測試資料
     */
    private func clearTestData() {
        spaceProfileManager.clearAllMappings()
        spaceDetector.resetTestState()
    }
    
    /**
     * 設定測試 Spaces
     */
    private func setupTestSpaces() {
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        spaceDetector.setCurrentSpace(1)
    }
    
    /**
     * 建立測試 Spaces
     */
    private func createTestSpaces() -> [SpaceInfo] {
        return [
            SpaceInfo(id: 1, name: "Space 1", isActive: true, displayName: "工作區 1"),
            SpaceInfo(id: 2, name: "Space 2", isActive: false, displayName: "工作區 2"),
            SpaceInfo(id: 3, name: "Space 3", isActive: false, displayName: "工作區 3")
        ]
    }
    
    /**
     * 建立測試設定檔
     */
    private func createTestProfile(name: String, spaceID: Int?) -> Profile {
        let testWindows = [
            WindowLayout(
                id: UUID().uuidString,
                app: "測試應用程式",
                title: "測試視窗",
                bundleID: "com.test.app",
                frame: WindowFrame(x: 100, y: 100, w: 800, h: 600)
            )
        ]
        
        return Profile(
            name: name,
            windows: testWindows,
            spaceID: spaceID,
            isSpaceSpecific: spaceID != nil,
            createdAt: Date(),
            modifiedAt: Date()
        )
    }
}

// MARK: - 測試擴展

extension SpaceProfileManager {
    /// 清理所有映射資料（測試用）
    func clearAllMappings() {
        DispatchQueue.main.async {
            self.spaceProfileMapping.removeAll()
        }
    }
}

extension SpaceDetector {
    /// 設定測試用的 Spaces
    func setTestSpaces(_ spaces: [SpaceInfo]) {
        DispatchQueue.main.async {
            self.availableSpaces = spaces
        }
    }
    
    /// 設定測試用的當前 Space
    func setCurrentSpace(_ spaceID: Int) {
        DispatchQueue.main.async {
            self.currentSpaceID = spaceID
        }
    }
    
    /// 重設測試狀態
    func resetTestState() {
        DispatchQueue.main.async {
            self.availableSpaces = []
            self.currentSpaceID = nil
        }
    }
}