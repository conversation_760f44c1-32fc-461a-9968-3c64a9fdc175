import Testing
@testable import Workspace

@Suite("HammerspoonManager Space Integration Tests")
struct HammerspoonManagerSpaceIntegrationTests {
    
    // MARK: - Space Context Validation Tests
    
    @Test("Validate Profile Space Context With Matching Space")
    func testValidateProfileSpaceContext_WithMatchingSpace() {
        // Given: 創建一個測試 Profile 檔案，包含 Space 資訊
        let profileName = "TestProfile_SpaceValidation"
        let testSpaceID = 1
        let hammerspoonManager = HammerspoonManager.shared
        let spaceDetector = SpaceDetector.shared
        
        createTestProfileWithSpace(profileName: profileName, spaceID: testSpaceID)
        
        // When: 驗證 Profile Space 上下文
        let isValid = hammerspoonManager.validateProfileSpaceContext(profileName: profileName)
        
        // Then: 如果當前 Space 匹配，應該返回 true
        if spaceDetector.getCurrentSpace() == testSpaceID {
            #expect(isValid, "Profile 應該在正確的 Space 上下文中有效")
        }
        
        // Cleanup
        cleanupTestProfile(profileName: profileName)
    }
    
    @Test("Validate Profile Space Context Without Space Info")
    func testValidateProfileSpaceContext_WithoutSpaceInfo() {
        // Given: 創建一個沒有 Space 資訊的測試 Profile
        let profileName = "TestProfile_NoSpace"
        let hammerspoonManager = HammerspoonManager.shared
        
        createTestProfileWithoutSpace(profileName: profileName)
        
        // When: 驗證 Profile Space 上下文
        let isValid = hammerspoonManager.validateProfileSpaceContext(profileName: profileName)
        
        // Then: 沒有 Space 資訊的 Profile 應該總是有效
        #expect(isValid, "沒有 Space 資訊的 Profile 應該總是有效")
        
        // Cleanup
        cleanupTestProfile(profileName: profileName)
    }
    
    @Test("Get Profile Space ID With Valid Profile")
    func testGetProfileSpaceID_WithValidProfile() {
        // Given: 創建一個包含 Space 資訊的測試 Profile
        let profileName = "TestProfile_WithSpaceID"
        let expectedSpaceID = 2
        createTestProfileWithSpace(profileName: profileName, spaceID: expectedSpaceID)
        
        // When: 獲取 Profile 的 Space ID
        let spaceID = getProfileSpaceIDForTesting(profileName: profileName)
        
        // Then: 應該返回正確的 Space ID
        #expect(spaceID == expectedSpaceID, "應該返回正確的 Space ID")
        
        // Cleanup
        cleanupTestProfile(profileName: profileName)
    }
    
    @Test("Get Profile Space ID With Invalid Profile")
    func testGetProfileSpaceID_WithInvalidProfile() {
        // Given: 一個不存在的 Profile 名稱
        let profileName = "NonExistentProfile"
        
        // When: 嘗試獲取不存在 Profile 的 Space ID
        let spaceID = getProfileSpaceIDForTesting(profileName: profileName)
        
        // Then: 應該返回 nil
        #expect(spaceID == nil, "不存在的 Profile 應該返回 nil")
    }
    
    // MARK: - Cross-Space Restoration Tests
    
    @Test("Should Show Cross Space Confirmation With Different Spaces")
    func testShouldShowCrossSpaceConfirmation_WithDifferentSpaces() {
        // Given: 不同的當前 Space 和 Profile Space
        let currentSpaceID = 1
        let profileSpaceID = 2
        
        // When: 檢查是否需要顯示跨 Space 確認
        let shouldShow = shouldShowCrossSpaceConfirmationForTesting(
            currentSpaceID: currentSpaceID,
            profileSpaceID: profileSpaceID
        )
        
        // Then: 應該顯示確認對話框
        #expect(shouldShow, "不同 Space 之間應該顯示確認對話框")
    }
    
    @Test("Should Show Cross Space Confirmation With Same Space")
    func testShouldShowCrossSpaceConfirmation_WithSameSpace() {
        // Given: 相同的當前 Space 和 Profile Space
        let currentSpaceID = 1
        let profileSpaceID = 1
        
        // When: 檢查是否需要顯示跨 Space 確認
        let shouldShow = shouldShowCrossSpaceConfirmationForTesting(
            currentSpaceID: currentSpaceID,
            profileSpaceID: profileSpaceID
        )
        
        // Then: 不應該顯示確認對話框
        #expect(!shouldShow, "相同 Space 不應該顯示確認對話框")
    }
    
    @Test("Should Show Cross Space Confirmation With Nil Spaces")
    func testShouldShowCrossSpaceConfirmation_WithNilSpaces() {
        // Given: 其中一個或兩個 Space ID 為 nil
        let testCases: [(Int?, Int?, Bool)] = [
            (nil, 1, false),
            (1, nil, false),
            (nil, nil, false)
        ]
        
        for (currentSpaceID, profileSpaceID, expectedResult) in testCases {
            // When: 檢查是否需要顯示跨 Space 確認
            let shouldShow = shouldShowCrossSpaceConfirmationForTesting(
                currentSpaceID: currentSpaceID,
                profileSpaceID: profileSpaceID
            )
            
            // Then: 應該根據預期結果返回
            #expect(shouldShow == expectedResult, 
                   "Space ID 為 nil 的情況應該返回 \(expectedResult)")
        }
    }
    
    // MARK: - Space-Aware Save Tests
    
    @Test("Save Current Layout With Space Detection")
    func testSaveCurrentLayout_WithSpaceDetection() {
        // Given: 一個測試 Profile 名稱
        let profileName = "TestProfile_SpaceAwareSave"
        let spaceDetector = SpaceDetector.shared
        
        // When: 儲存當前佈局（這個測試主要驗證方法不會崩潰）
        // 注意：實際的 Hammerspoon 通信在測試環境中可能不可用
        
        // Then: 方法應該能夠正常執行而不崩潰
        let currentSpace = spaceDetector.getCurrentSpace()
        // 至少應該能夠獲取 Space 資訊（可能為 nil，但不應該崩潰）
        #expect(currentSpace == nil || (currentSpace! >= 1 && currentSpace! <= 3), 
               "Space 感知的儲存操作不應該崩潰，且 Space ID 應該在有效範圍內")
    }
    
    // MARK: - Helper Methods
    
    // MARK: - Helper Methods
    
    private func createTestProfileWithSpace(profileName: String, spaceID: Int) {
        let layoutsPath = FileManager.default.homeDirectoryForCurrentUser
            .appendingPathComponent(".hammerspoon/layouts")
        
        // 確保目錄存在
        try? FileManager.default.createDirectory(at: layoutsPath, withIntermediateDirectories: true)
        
        let profileData: [String: Any] = [
            "windows": [],
            "spaceID": spaceID,
            "timestamp": Date().timeIntervalSince1970,
            "version": "1.0"
        ]
        
        let profilePath = layoutsPath.appendingPathComponent("\(profileName).json")
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: profileData, options: .prettyPrinted)
            try jsonData.write(to: profilePath)
        } catch {
            // In Swift Testing, we can't use XCTFail, so we'll use a different approach
            print("無法創建測試 Profile: \(error)")
        }
    }
    
    private func createTestProfileWithoutSpace(profileName: String) {
        let layoutsPath = FileManager.default.homeDirectoryForCurrentUser
            .appendingPathComponent(".hammerspoon/layouts")
        
        // 確保目錄存在
        try? FileManager.default.createDirectory(at: layoutsPath, withIntermediateDirectories: true)
        
        let profileData: [String: Any] = [
            "windows": [],
            "timestamp": Date().timeIntervalSince1970,
            "version": "1.0"
        ]
        
        let profilePath = layoutsPath.appendingPathComponent("\(profileName).json")
        
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: profileData, options: .prettyPrinted)
            try jsonData.write(to: profilePath)
        } catch {
            print("無法創建測試 Profile: \(error)")
        }
    }
    
    private func cleanupTestProfile(profileName: String) {
        let layoutsPath = FileManager.default.homeDirectoryForCurrentUser
            .appendingPathComponent(".hammerspoon/layouts")
        let profilePath = layoutsPath.appendingPathComponent("\(profileName).json")
        
        try? FileManager.default.removeItem(at: profilePath)
    }
    
    // 測試輔助方法，用於訪問私有方法
    private func getProfileSpaceIDForTesting(profileName: String) -> Int? {
        let layoutsPath = FileManager.default.homeDirectoryForCurrentUser
            .appendingPathComponent(".hammerspoon/layouts")
        let profilePath = layoutsPath.appendingPathComponent("\(profileName).json")
        
        guard let data = try? Data(contentsOf: profilePath),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            return nil
        }
        
        return json["spaceID"] as? Int
    }
    
    private func shouldShowCrossSpaceConfirmationForTesting(currentSpaceID: Int?, profileSpaceID: Int?) -> Bool {
        // 如果任一方沒有 Space 資訊，不需要確認
        guard let currentSpaceID = currentSpaceID,
              let profileSpaceID = profileSpaceID else {
            return false
        }
        
        // 如果 Space 不同，需要確認
        return currentSpaceID != profileSpaceID
    }
}

@Suite("HammerspoonManager Integration Tests")
struct HammerspoonManagerIntegrationTests {
    
    @Test("Hammerspoon Installation Status")
    func testHammerspoonInstallationStatus() {
        // Given: HammerspoonManager 實例
        let hammerspoonManager = HammerspoonManager.shared
        
        // When: 檢查安裝狀態
        hammerspoonManager.checkInstallation()
        
        // Then: 應該能夠檢查 Hammerspoon 是否已安裝
        // 注意：這個測試的結果取決於測試環境是否安裝了 Hammerspoon
        #expect(hammerspoonManager.isInstalled != nil, "應該能夠檢查 Hammerspoon 安裝狀態")
    }
    
    @Test("SpaceDetector Integration")
    func testSpaceDetectorIntegration() {
        // Given: HammerspoonManager 與 SpaceDetector 的整合
        let spaceDetector = SpaceDetector.shared
        
        // When: 獲取當前 Space 資訊
        let currentSpace = spaceDetector.getCurrentSpace()
        
        // Then: 應該能夠獲取 Space 資訊（可能為 nil，但不應該崩潰）
        // 驗證方法調用不會崩潰
        #expect(currentSpace == nil || (currentSpace! >= 1 && currentSpace! <= 3), 
               "獲取當前 Space 不應該崩潰，且 Space ID 應該在有效範圍內")
        
        print("當前 Space ID: \(currentSpace ?? -1)")
    }
}