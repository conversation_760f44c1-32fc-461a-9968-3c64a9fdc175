import Testing
import Foundation
import Swift<PERSON>
@testable @testable import Workspace

// MARK: - 視覺回歸測試套件
@Suite("Visual Regression Tests")
struct VisualRegressionTests {
    
    // MARK: - 測試數據生成
    
    private func createTestProfile(windowCount: Int) -> Profile {
        let windows = (0..<windowCount).map { i in
            WindowLayout(
                app: "TestApp\(i)",
                bundleID: "com.test.app\(i)",
                title: "Test Window \(i)",
                frame: WindowLayout.WindowFrame(
                    x: Double(100 + i * 50),
                    y: Double(100 + i * 30),
                    w: Double(300 + i % 200),
                    h: Double(200 + i % 150)
                )
            )
        }
        
        return Profile(name: "Test Profile", windows: windows)
    }
    
    private func createOverlappingProfile() -> Profile {
        let windows = [
            WindowLayout(
                app: "Safari",
                bundleID: "com.apple.Safari",
                title: "Safari Window",
                frame: WindowLayout.WindowFrame(x: 100, y: 100, w: 800, h: 600)
            ),
            WindowLayout(
                app: "Chrome",
                bundleID: "com.google.Chrome",
                title: "Chrome Window",
                frame: WindowLayout.WindowFrame(x: 150, y: 150, w: 800, h: 600)
            ),
            WindowLayout(
                app: "Firefox",
                bundleID: "org.mozilla.firefox",
                title: "Firefox Window",
                frame: WindowLayout.WindowFrame(x: 200, y: 200, w: 800, h: 600)
            )
        ]
        
        return Profile(name: "Overlapping Profile", windows: windows)
    }
    
    private func createMultiScreenProfile() -> Profile {
        let windows = [
            // 第一個螢幕的視窗
            WindowLayout(
                app: "Screen1App1", bundleID: "com.screen1.app1", title: "Screen 1 Window 1",
                frame: WindowLayout.WindowFrame(x: 100, y: 100, w: 400, h: 300)
            ),
            WindowLayout(
                app: "Screen1App2", bundleID: "com.screen1.app2", title: "Screen 1 Window 2",
                frame: WindowLayout.WindowFrame(x: 600, y: 200, w: 500, h: 400)
            ),
            // 第二個螢幕的視窗
            WindowLayout(
                app: "Screen2App1", bundleID: "com.screen2.app1", title: "Screen 2 Window 1",
                frame: WindowLayout.WindowFrame(x: 2020, y: 100, w: 600, h: 450)
            ),
            WindowLayout(
                app: "Screen2App2", bundleID: "com.screen2.app2", title: "Screen 2 Window 2",
                frame: WindowLayout.WindowFrame(x: 2700, y: 300, w: 400, h: 300)
            )
        ]
        
        return Profile(name: "Multi-Screen Profile", windows: windows)
    }
    
    // MARK: - 縮放預覽模式視覺測試
    
    @Test("視覺回歸 - 縮放模式 - 少量視窗")
    func testScaledModeVisualFewWindows() {
        let profile = createTestProfile(windowCount: 3)
        let bounds = CGRect(x: 0, y: 0, width: 800, height: 600)
        
        let result = OverlapResolver.resolveOverlaps(
            windows: profile.windows,
            in: bounds
        )
        
        // 驗證視覺佈局的關鍵屬性
        #expect(result.windowDisplayInfos.count == 3)
        
        // 檢查視窗是否都在邊界內
        for info in result.windowDisplayInfos {
            #expect(bounds.contains(info.displayFrame.origin))
            #expect(info.displayFrame.maxX <= bounds.maxX)
            #expect(info.displayFrame.maxY <= bounds.maxY)
        }
        
        // 檢查透明度設置是否合理
        for info in result.windowDisplayInfos {
            #expect(info.opacity >= 0.3)
            #expect(info.opacity <= 1.0)
        }
    }
    
    @Test("視覺回歸 - 縮放模式 - 重疊視窗")
    func testScaledModeVisualOverlappingWindows() {
        let profile = createOverlappingProfile()
        let bounds = CGRect(x: 0, y: 0, width: 1000, height: 800)
        
        let result = OverlapResolver.resolveOverlaps(
            windows: profile.windows,
            in: bounds
        )
        
        // 檢查重疊檢測是否正確
        let overlappingWindows = result.windowDisplayInfos.filter { $0.isOverlapping }
        #expect(overlappingWindows.count > 0)
        
        // 檢查重疊層級是否合理分配
        let maxLevel = result.windowDisplayInfos.map { $0.overlapLevel }.max() ?? 0
        #expect(maxLevel > 0)
        
        // 檢查透明度是否根據重疊程度調整
        for info in result.windowDisplayInfos {
            if info.isOverlapping {
                #expect(info.opacity < 1.0)
            }
        }
    }
    
    @Test("視覺回歸 - 縮放模式 - 大量視窗")
    func testScaledModeVisualManyWindows() {
        let profile = createTestProfile(windowCount: 20)
        let bounds = CGRect(x: 0, y: 0, width: 1200, height: 900)
        
        let result = OverlapResolver.resolveOverlaps(
            windows: profile.windows,
            in: bounds
        )
        
        #expect(result.windowDisplayInfos.count == 20)
        
        // 檢查視窗縮放是否合理
        for info in result.windowDisplayInfos {
            #expect(info.displayFrame.width > 0)
            #expect(info.displayFrame.height > 0)
            #expect(info.displayFrame.width <= bounds.width)
            #expect(info.displayFrame.height <= bounds.height)
        }
        
        // 檢查是否有合理的重疊處理
        #expect(result.totalOverlapCount >= 0)
        #expect(result.maxOverlapLevel >= 0)
    }
    
    // MARK: - 網格預覽模式視覺測試
    
    @Test("視覺回歸 - 網格模式 - 佈局計算")
    func testGridModeVisualLayoutCalculation() {
        let profile = createTestProfile(windowCount: 12)
        let containerWidth: CGFloat = 800
        let itemWidth: CGFloat = 160
        let itemHeight: CGFloat = 120
        let spacing: CGFloat = 8
        let padding: CGFloat = 16
        
        // 計算網格配置
        let availableWidth = containerWidth - padding * 2
        let columnsPerRow = max(1, Int(availableWidth / (itemWidth + spacing)))
        let totalRows = (profile.windows.count + columnsPerRow - 1) / columnsPerRow
        
        #expect(columnsPerRow > 0)
        #expect(totalRows > 0)
        #expect(columnsPerRow * totalRows >= profile.windows.count)
        
        // 檢查網格項目位置
        for (index, window) in profile.windows.enumerated() {
            let row = index / columnsPerRow
            let col = index % columnsPerRow
            
            let expectedX = padding + CGFloat(col) * (itemWidth + spacing)
            let expectedY = padding + CGFloat(row) * (itemHeight + spacing)
            
            #expect(expectedX >= padding)
            #expect(expectedY >= padding)
            #expect(expectedX + itemWidth <= containerWidth - padding)
        }
    }
    
    @Test("視覺回歸 - 網格模式 - 響應式佈局")
    func testGridModeVisualResponsiveLayout() {
        let profile = createTestProfile(windowCount: 15)
        let containerSizes: [CGSize] = [
            CGSize(width: 400, height: 600),   // 窄容器
            CGSize(width: 800, height: 600),   // 中等容器
            CGSize(width: 1200, height: 800),  // 寬容器
            CGSize(width: 1600, height: 1000)  // 超寬容器
        ]
        
        for containerSize in containerSizes {
            let itemWidth: CGFloat = 160
            let spacing: CGFloat = 8
            let padding: CGFloat = 16
            
            let availableWidth = containerSize.width - padding * 2
            let columnsPerRow = max(1, Int(availableWidth / (itemWidth + spacing)))
            
            #expect(columnsPerRow > 0)
            
            // 檢查不同容器寬度下的列數是否合理
            if containerSize.width >= 800 {
                #expect(columnsPerRow >= 4)
            } else if containerSize.width >= 400 {
                #expect(columnsPerRow >= 2)
            } else {
                #expect(columnsPerRow >= 1)
            }
        }
    }
    
    // MARK: - 列表預覽模式視覺測試
    
    @Test("視覺回歸 - 列表模式 - 項目佈局")
    func testListModeVisualItemLayout() {
        let profile = createTestProfile(windowCount: 10)
        let containerSize = CGSize(width: 800, height: 600)
        let itemHeight: CGFloat = 80
        let spacing: CGFloat = 4
        
        // 計算列表項目位置
        for (index, window) in profile.windows.enumerated() {
            let expectedY = CGFloat(index) * (itemHeight + spacing)
            
            #expect(expectedY >= 0)
            #expect(expectedY + itemHeight <= CGFloat(profile.windows.count) * (itemHeight + spacing))
        }
        
        // 檢查總高度
        let totalHeight = CGFloat(profile.windows.count) * itemHeight + CGFloat(profile.windows.count - 1) * spacing
        #expect(totalHeight > 0)
    }
    
    @Test("視覺回歸 - 列表模式 - 虛擬化滾動")
    func testListModeVisualVirtualizedScrolling() {
        let profile = createTestProfile(windowCount: 100)
        let manager = VirtualizationManager()
        
        let config = VirtualizationManager.VirtualizationConfig(
            bufferSize: 20,
            itemHeight: 80,
            itemWidth: 800,
            enableVirtualization: true
        )
        manager.updateConfig(config)
        
        let viewportSize = CGSize(width: 800, height: 600)
        let scrollOffsets: [CGPoint] = [
            CGPoint(x: 0, y: 0),
            CGPoint(x: 0, y: 500),
            CGPoint(x: 0, y: 1000),
            CGPoint(x: 0, y: 2000)
        ]
        
        for scrollOffset in scrollOffsets {
            let visibleRange = manager.updateVisibleRange(
                viewportSize: viewportSize,
                scrollOffset: scrollOffset,
                totalItems: profile.windows.count,
                mode: .list
            )
            
            #expect(visibleRange.startIndex >= 0)
            #expect(visibleRange.endIndex <= profile.windows.count)
            #expect(visibleRange.startIndex <= visibleRange.endIndex)
            
            let virtualizedItems = manager.getVirtualizedItems(from: profile.windows)
            let visibleItems = virtualizedItems.filter { $0.isVisible }
            
            #expect(visibleItems.count <= config.bufferSize * 2)
        }
    }
    
    // MARK: - 小地圖預覽模式視覺測試
    
    @Test("視覺回歸 - 小地圖模式 - 螢幕檢測")
    func testMiniMapModeVisualScreenDetection() {
        let profile = createMultiScreenProfile()
        let screens = ScreenBounds.detectMultipleScreens(from: profile.windows)
        
        #expect(screens.count >= 1)
        
        // 檢查螢幕邊界是否合理
        for screen in screens {
            #expect(screen.bounds.width > 0)
            #expect(screen.bounds.height > 0)
        }
        
        // 檢查視窗是否正確分配到螢幕
        for window in profile.windows {
            let windowFrame = CGRect(
                x: window.frame.x,
                y: window.frame.y,
                width: window.frame.w,
                height: window.frame.h
            )
            
            let containingScreens = screens.filter { screen in
                screen.bounds.intersects(windowFrame)
            }
            
            #expect(containingScreens.count >= 1)
        }
    }
    
    @Test("視覺回歸 - 小地圖模式 - 密度熱力圖")
    func testMiniMapModeVisualDensityHeatmap() {
        let profile = createTestProfile(windowCount: 50)
        let bounds = CGRect(x: 0, y: 0, width: 2000, height: 1500)
        
        // 模擬密度計算
        let gridSize: CGFloat = 100
        let cols = Int(bounds.width / gridSize)
        let rows = Int(bounds.height / gridSize)
        
        var densityGrid = Array(repeating: Array(repeating: 0, count: cols), count: rows)
        
        // 計算每個網格的視窗密度
        for window in profile.windows {
            let centerX = window.frame.x + window.frame.w / 2
            let centerY = window.frame.y + window.frame.h / 2
            
            let gridX = Int(centerX / Double(gridSize))
            let gridY = Int(centerY / Double(gridSize))
            
            if gridX >= 0 && gridX < cols && gridY >= 0 && gridY < rows {
                densityGrid[gridY][gridX] += 1
            }
        }
        
        // 檢查密度分佈
        let totalDensity = densityGrid.flatMap { $0 }.reduce(0, +)
        #expect(totalDensity == profile.windows.count)
        
        let maxDensity = densityGrid.flatMap { $0 }.max() ?? 0
        #expect(maxDensity > 0)
    }
    
    // MARK: - 不同配置下的視覺一致性測試
    
    @Test("視覺一致性 - 不同視窗數量")
    func testVisualConsistencyDifferentWindowCounts() {
        let windowCounts = [1, 5, 10, 20, 50]
        let bounds = CGRect(x: 0, y: 0, width: 1000, height: 800)
        
        for count in windowCounts {
            let profile = createTestProfile(windowCount: count)
            let result = OverlapResolver.resolveOverlaps(
                windows: profile.windows,
                in: bounds
            )
            
            #expect(result.windowDisplayInfos.count == count)
            
            // 檢查視覺一致性
            for info in result.windowDisplayInfos {
                #expect(info.displayFrame.width > 0)
                #expect(info.displayFrame.height > 0)
                #expect(info.opacity >= 0.3)
                #expect(info.opacity <= 1.0)
            }
            
            // 檢查縮放比例的一致性
            if count > 1 {
                let areas = result.windowDisplayInfos.map { $0.displayFrame.width * $0.displayFrame.height }
                let avgArea = areas.reduce(0, +) / CGFloat(areas.count)
                
                for area in areas {
                    // 面積差異不應該太大（除非原始視窗大小差異很大）
                    let ratio = area / avgArea
                    #expect(ratio >= 0.1) // 最小面積不應該太小
                    #expect(ratio <= 10.0) // 最大面積不應該太大
                }
            }
        }
    }
    
    @Test("視覺一致性 - 不同容器尺寸")
    func testVisualConsistencyDifferentContainerSizes() {
        let profile = createTestProfile(windowCount: 10)
        let containerSizes: [CGSize] = [
            CGSize(width: 400, height: 300),   // 小容器
            CGSize(width: 800, height: 600),   // 中等容器
            CGSize(width: 1200, height: 900),  // 大容器
            CGSize(width: 1600, height: 1200)  // 超大容器
        ]
        
        for containerSize in containerSizes {
            let bounds = CGRect(origin: .zero, size: containerSize)
            let result = OverlapResolver.resolveOverlaps(
                windows: profile.windows,
                in: bounds
            )
            
            #expect(result.windowDisplayInfos.count == 10)
            
            // 檢查所有視窗都在容器內
            for info in result.windowDisplayInfos {
                #expect(info.displayFrame.minX >= bounds.minX)
                #expect(info.displayFrame.minY >= bounds.minY)
                #expect(info.displayFrame.maxX <= bounds.maxX)
                #expect(info.displayFrame.maxY <= bounds.maxY)
            }
            
            // 檢查縮放比例是否適應容器大小
            let totalDisplayArea = result.windowDisplayInfos.map { $0.displayFrame.area }.reduce(0, +)
            let containerArea = containerSize.width * containerSize.height
            let utilizationRatio = totalDisplayArea / containerArea
            
            #expect(utilizationRatio > 0.1) // 至少使用10%的容器空間
            #expect(utilizationRatio <= 1.0) // 不應該超出容器
        }
    }
    
    // MARK: - 主題和外觀測試
    
    @Test("視覺回歸 - 淺色主題")
    func testVisualRegressionLightTheme() {
        // 模擬淺色主題的顏色設置
        let lightThemeColors = [
            "background": Color.white,
            "foreground": Color.black,
            "secondary": Color.gray,
            "accent": Color.blue
        ]
        
        for (name, color) in lightThemeColors {
            #expect(color != nil)
        }
        
        #expect(lightThemeColors.count == 4)
    }
    
    @Test("視覺回歸 - 深色主題")
    func testVisualRegressionDarkTheme() {
        // 模擬深色主題的顏色設置
        let darkThemeColors = [
            "background": Color.black,
            "foreground": Color.white,
            "secondary": Color.gray,
            "accent": Color.blue
        ]
        
        for (name, color) in darkThemeColors {
            #expect(color != nil)
        }
        
        #expect(darkThemeColors.count == 4)
    }
    
    @Test("視覺回歸 - 高對比度主題")
    func testVisualRegressionHighContrastTheme() {
        // 模擬高對比度主題的設置
        let highContrastSettings = [
            "borderWidth": 2.0,
            "borderOpacity": 1.0,
            "backgroundOpacity": 1.0,
            "textWeight": 600.0
        ]
        
        for (setting, value) in highContrastSettings {
            #expect(value > 0)
            
            // 檢查高對比度設置是否比正常設置更強烈
            switch setting {
            case "borderWidth":
                #expect(value >= 2.0)
            case "borderOpacity", "backgroundOpacity":
                #expect(value >= 0.8)
            case "textWeight":
                #expect(value >= 500.0)
            default:
                break
            }
        }
    }
    
    // MARK: - PreviewModeSelector 視覺測試
    
    @Test("視覺回歸 - PreviewModeSelector - 基本佈局")
    func testPreviewModeSelectorVisualBasicLayout() {
        let modes = PreviewMode.allCases
        
        // 檢查所有模式都有適當的視覺屬性
        for mode in modes {
            #expect(!mode.displayName.isEmpty)
            #expect(!mode.icon.isEmpty)
            #expect(!mode.description.isEmpty)
        }
        
        // 檢查模式數量
        #expect(modes.count == 4)
        
        // 檢查模式順序和一致性
        let expectedModes: [PreviewMode] = [.scaled, .grid, .list, .miniMap]
        for (index, expectedMode) in expectedModes.enumerated() {
            #expect(modes.contains(expectedMode))
        }
    }
    
    @Test("視覺回歸 - PreviewModeSelector - 選中狀態")
    func testPreviewModeSelectorVisualSelectedState() {
        var selectedMode: PreviewMode = .scaled
        
        // 測試每個模式的選中狀態
        for mode in PreviewMode.allCases {
            selectedMode = mode
            
            // 檢查選中狀態的視覺屬性
            let isSelected = (selectedMode == mode)
            #expect(isSelected == true)
            
            // 模擬選中狀態的視覺變化
            let backgroundColor = isSelected ? Color.accentColor : Color.clear
            let foregroundColor = isSelected ? Color.white : Color.primary
            let scale: CGFloat = isSelected ? 1.0 : 1.0
            
            #expect(backgroundColor != nil)
            #expect(foregroundColor != nil)
            #expect(scale > 0)
        }
    }
    
    @Test("視覺回歸 - PreviewModeSelector - 懸停效果")
    func testPreviewModeSelectorVisualHoverEffect() {
        var isHovered = false
        var hoverScale: CGFloat = 1.0
        var hoverOpacity: Double = 1.0
        
        // 模擬懸停狀態
        isHovered = true
        if isHovered {
            hoverScale = 1.02
            hoverOpacity = 0.8
        }
        
        #expect(hoverScale >= 1.0)
        #expect(hoverOpacity > 0.0)
        #expect(hoverOpacity <= 1.0)
        
        // 檢查懸停動畫參數
        let hoverAnimationDuration: TimeInterval = 0.15
        #expect(hoverAnimationDuration > 0)
        #expect(hoverAnimationDuration <= 0.3)
    }
    
    @Test("視覺回歸 - PreviewModeSelector - 響應式設計")
    func testPreviewModeSelectorVisualResponsiveDesign() {
        let containerWidths: [CGFloat] = [300, 500, 800, 1200]
        
        for containerWidth in containerWidths {
            // 計算按鈕佈局
            let buttonCount = PreviewMode.allCases.count
            let spacing: CGFloat = 8
            let padding: CGFloat = 4
            let minButtonWidth: CGFloat = 60
            
            let availableWidth = containerWidth - padding * 2 - spacing * CGFloat(buttonCount - 1)
            let buttonWidth = max(minButtonWidth, availableWidth / CGFloat(buttonCount))
            
            #expect(buttonWidth >= minButtonWidth)
            #expect(buttonWidth * CGFloat(buttonCount) + spacing * CGFloat(buttonCount - 1) + padding * 2 <= containerWidth + 1) // +1 for floating point precision
            
            // 檢查文字是否需要截斷
            let shouldTruncateText = buttonWidth < 80
            if shouldTruncateText {
                // 在窄容器中，可能需要隱藏文字，只顯示圖標
                #expect(buttonWidth >= 40) // 至少要能顯示圖標
            }
        }
    }
    
    @Test("視覺回歸 - PreviewModeSelector - 無障礙支持")
    func testPreviewModeSelectorVisualAccessibilitySupport() {
        // 檢查每個模式的無障礙屬性
        for mode in PreviewMode.allCases {
            let accessibilityLabel = mode.displayName
            let accessibilityHint = "切換到\(mode.displayName)模式"
            let accessibilityDescription = mode.description
            
            #expect(!accessibilityLabel.isEmpty)
            #expect(!accessibilityHint.isEmpty)
            #expect(!accessibilityDescription.isEmpty)
            #expect(accessibilityHint.contains("切換到"))
            #expect(accessibilityHint.contains("模式"))
        }
        
        // 檢查容器的無障礙屬性
        let containerLabel = "預覽模式選擇器"
        let containerHint = "選擇不同的視窗預覽模式"
        
        #expect(!containerLabel.isEmpty)
        #expect(!containerHint.isEmpty)
        #expect(containerLabel.contains("選擇器"))
    }
    
    @Test("視覺回歸 - PreviewModeSelector - 主題適應")
    func testPreviewModeSelectorVisualThemeAdaptation() {
        // 測試淺色主題
        let lightTheme = [
            "containerBackground": Color(NSColor.controlBackgroundColor),
            "selectedBackground": Color.accentColor,
            "selectedForeground": Color.white,
            "unselectedForeground": Color.primary,
            "borderColor": Color.secondary.opacity(0.2)
        ]
        
        for (element, color) in lightTheme {
            #expect(color != nil)
        }
        
        // 測試深色主題適應
        let darkTheme = [
            "containerBackground": Color(NSColor.controlBackgroundColor),
            "selectedBackground": Color.accentColor,
            "selectedForeground": Color.white,
            "unselectedForeground": Color.primary,
            "borderColor": Color.secondary.opacity(0.2)
        ]
        
        for (element, color) in darkTheme {
            #expect(color != nil)
        }
        
        // 檢查主題切換時的一致性
        #expect(lightTheme.count == darkTheme.count)
    }
    
    @Test("視覺回歸 - PreviewModeSelector - 動畫流暢性")
    func testPreviewModeSelectorVisualAnimationSmoothness() {
        var currentMode: PreviewMode = .scaled
        let animationDuration: TimeInterval = 0.2
        let animationCurve = "easeInOut"
        
        // 測試模式切換動畫
        for targetMode in PreviewMode.allCases {
            if targetMode != currentMode {
                // 模擬動畫參數
                #expect(animationDuration > 0)
                #expect(animationDuration <= 0.5) // 不應該太慢
                #expect(!animationCurve.isEmpty)
                
                currentMode = targetMode
            }
        }
        
        // 檢查動畫的視覺連續性
        let frameRate: Double = 60.0
        let expectedFrames = Int(animationDuration * frameRate)
        #expect(expectedFrames >= 6) // 至少6幀保證流暢性
        #expect(expectedFrames <= 30) // 不超過30幀避免過長
    }
    
    // MARK: - 動畫和過渡測試
    
    @Test("視覺回歸 - 模式切換動畫")
    func testVisualRegressionModeTransitionAnimation() {
        var currentMode: PreviewMode = .scaled
        var animationDuration: TimeInterval = 0.3
        var enableAnimations = true
        
        // 模擬模式切換
        let targetMode: PreviewMode = .grid
        
        if enableAnimations {
            // 檢查動畫參數
            #expect(animationDuration > 0)
            #expect(animationDuration <= 1.0) // 動畫不應該太長
        }
        
        currentMode = targetMode
        #expect(currentMode == .grid)
    }
    
    @Test("視覺回歸 - 選擇狀態動畫")
    func testVisualRegressionSelectionAnimation() {
        var isSelected = false
        var selectionScale: CGFloat = 1.0
        var selectionOpacity: Double = 1.0
        var animationDuration: TimeInterval = 0.2
        
        // 模擬選擇動畫
        isSelected = true
        if isSelected {
            selectionScale = 1.05
            selectionOpacity = 0.9
        }
        
        #expect(selectionScale > 1.0)
        #expect(selectionOpacity < 1.0)
        #expect(animationDuration > 0)
        #expect(animationDuration <= 0.5)
    }
    
    // MARK: - 錯誤狀態視覺測試
    
    @Test("視覺回歸 - 空狀態顯示")
    func testVisualRegressionEmptyState() {
        let emptyProfile = Profile(name: "Empty", windows: [])
        
        // 檢查空狀態的視覺元素
        let emptyStateIcon = "rectangle.3.group"
        let emptyStateTitle = "沒有視窗"
        let emptyStateMessage = "當前配置中沒有視窗可以預覽"
        
        #expect(!emptyStateIcon.isEmpty)
        #expect(!emptyStateTitle.isEmpty)
        #expect(!emptyStateMessage.isEmpty)
        #expect(emptyStateMessage.contains("沒有視窗"))
    }
    
    @Test("視覺回歸 - 載入狀態顯示")
    func testVisualRegressionLoadingState() {
        var isLoading = true
        var loadingProgress: Double = 0.5
        
        // 檢查載入狀態的視覺元素
        let loadingTitle = "載入中..."
        let progressText = "\(Int(loadingProgress * 100))%"
        
        #expect(isLoading == true)
        #expect(loadingProgress >= 0.0)
        #expect(loadingProgress <= 1.0)
        #expect(!loadingTitle.isEmpty)
        #expect(progressText == "50%")
    }
    
    @Test("視覺回歸 - 錯誤狀態顯示")
    func testVisualRegressionErrorState() {
        let errorTitle = "渲染失敗"
        let errorMessage = "預覽渲染時發生錯誤"
        let errorIcon = "exclamationmark.circle"
        let errorColor = Color.red
        
        #expect(!errorTitle.isEmpty)
        #expect(!errorMessage.isEmpty)
        #expect(!errorIcon.isEmpty)
        #expect(errorColor != nil)
        #expect(errorMessage.contains("錯誤"))
    }
    
    // MARK: - 性能視覺化測試
    
    @Test("視覺回歸 - 性能指標顯示")
    func testVisualRegressionPerformanceMetrics() {
        let performanceMetrics = [
            "frameRate": 60.0,
            "renderTime": 16.7, // ms
            "memoryUsage": 45.2, // MB
            "cpuUsage": 12.5     // %
        ]
        
        for (metric, value) in performanceMetrics {
            #expect(value > 0)
            
            // 檢查性能指標的合理範圍
            switch metric {
            case "frameRate":
                #expect(value >= 30.0) // 至少30fps
                #expect(value <= 120.0) // 最多120fps
            case "renderTime":
                #expect(value <= 33.3) // 不超過30fps的幀時間
            case "memoryUsage":
                #expect(value <= 500.0) // 不超過500MB
            case "cpuUsage":
                #expect(value <= 100.0) // 不超過100%
            default:
                break
            }
        }
    }
    
    // MARK: - 輔助方法
    
    private func calculateVisualHash(for displayInfos: [WindowDisplayInfo]) -> String {
        // 簡化的視覺哈希計算，用於檢測視覺變化
        let positions = displayInfos.map { info in
            "\(Int(info.displayFrame.origin.x)),\(Int(info.displayFrame.origin.y)),\(Int(info.displayFrame.width)),\(Int(info.displayFrame.height)),\(Int(info.opacity * 100))"
        }.joined(separator: "|")
        
        return positions
    }
    
    private func compareVisualLayouts(_ layout1: [WindowDisplayInfo], _ layout2: [WindowDisplayInfo], tolerance: CGFloat = 1.0) -> Bool {
        guard layout1.count == layout2.count else { return false }
        
        for (info1, info2) in zip(layout1, layout2) {
            let positionDiff = abs(info1.displayFrame.origin.x - info2.displayFrame.origin.x) +
                              abs(info1.displayFrame.origin.y - info2.displayFrame.origin.y)
            let sizeDiff = abs(info1.displayFrame.width - info2.displayFrame.width) +
                          abs(info1.displayFrame.height - info2.displayFrame.height)
            let opacityDiff = abs(info1.opacity - info2.opacity)
            
            if positionDiff > tolerance || sizeDiff > tolerance || opacityDiff > 0.01 {
                return false
            }
        }
        
        return true
    }
}