import Testing
import Foundation
import SwiftUI
@testable @testable import Workspace

// MARK: - 用戶交互測試套件
@Suite("User Interaction Tests")
struct UserInteractionTests {
    
    // MARK: - 測試數據
    
    private func createTestProfile() -> Profile {
        let windows = [
            WindowLayout(
                app: "Safari",
                bundleID: "com.apple.Safari",
                title: "Test Page 1",
                frame: WindowLayout.WindowFrame(x: 100, y: 100, w: 800, h: 600)
            ),
            WindowLayout(
                app: "Chrome",
                bundleID: "com.google.Chrome",
                title: "Test Page 2",
                frame: WindowLayout.WindowFrame(x: 200, y: 200, w: 700, h: 500)
            ),
            WindowLayout(
                app: "Terminal",
                bundleID: "com.apple.Terminal",
                title: "Terminal",
                frame: WindowLayout.WindowFrame(x: 300, y: 300, w: 600, h: 400)
            )
        ]
        
        return Profile(name: "Test Profile", windows: windows)
    }
    
    // MARK: - 預覽模式選擇器交互測試
    
    @Test("預覽模式選擇器 - 模式切換")
    func testPreviewModeSelectorModeSwitch() {
        var selectedMode: PreviewMode = .scaled
        var configuration = PreviewConfiguration.default
        
        // 模擬用戶點擊不同模式
        for targetMode in PreviewMode.allCases {
            selectedMode = targetMode
            configuration.mode = targetMode
            
            #expect(selectedMode == targetMode)
            #expect(configuration.mode == targetMode)
        }
    }
    
    @Test("預覽模式選擇器 - 配置更新")
    func testPreviewModeSelectorConfigurationUpdate() {
        var selectedMode: PreviewMode = .scaled
        var configuration = PreviewConfiguration.default
        
        // 模擬配置變更
        configuration.showLabels = false
        configuration.maxWindowsPerView = 25
        
        #expect(configuration.showLabels == false)
        #expect(configuration.maxWindowsPerView == 25)
    }
    
    // MARK: - 視窗選擇交互測試
    
    @Test("視窗選擇 - 單選模式")
    func testWindowSelectionSingleMode() {
        let profile = createTestProfile()
        var selectedWindow: WindowLayout? = nil
        var selectedWindows: Set<UUID> = []
        
        // 模擬選擇第一個視窗
        let firstWindow = profile.windows[0]
        selectedWindow = firstWindow
        selectedWindows = [firstWindow.id]
        
        #expect(selectedWindow?.id == firstWindow.id)
        #expect(selectedWindows.count == 1)
        #expect(selectedWindows.contains(firstWindow.id))
        
        // 模擬選擇第二個視窗
        let secondWindow = profile.windows[1]
        selectedWindow = secondWindow
        selectedWindows = [secondWindow.id]
        
        #expect(selectedWindow?.id == secondWindow.id)
        #expect(selectedWindows.count == 1)
        #expect(selectedWindows.contains(secondWindow.id))
        #expect(!selectedWindows.contains(firstWindow.id))
    }
    
    @Test("視窗選擇 - 多選模式")
    func testWindowSelectionMultiMode() {
        let profile = createTestProfile()
        var selectedWindow: WindowLayout? = nil
        var selectedWindows: Set<UUID> = []
        let isMultiSelectMode = true
        
        // 模擬多選第一個視窗
        let firstWindow = profile.windows[0]
        selectedWindow = firstWindow
        selectedWindows.insert(firstWindow.id)
        
        #expect(selectedWindow?.id == firstWindow.id)
        #expect(selectedWindows.count == 1)
        
        // 模擬多選第二個視窗
        let secondWindow = profile.windows[1]
        selectedWindow = secondWindow
        selectedWindows.insert(secondWindow.id)
        
        #expect(selectedWindow?.id == secondWindow.id)
        #expect(selectedWindows.count == 2)
        #expect(selectedWindows.contains(firstWindow.id))
        #expect(selectedWindows.contains(secondWindow.id))
        
        // 模擬取消選擇第一個視窗
        selectedWindows.remove(firstWindow.id)
        if selectedWindow?.id == firstWindow.id {
            selectedWindow = profile.windows.first { selectedWindows.contains($0.id) }
        }
        
        #expect(selectedWindows.count == 1)
        #expect(!selectedWindows.contains(firstWindow.id))
        #expect(selectedWindows.contains(secondWindow.id))
        #expect(selectedWindow?.id == secondWindow.id)
    }
    
    @Test("視窗選擇 - 清空選擇")
    func testWindowSelectionClear() {
        let profile = createTestProfile()
        var selectedWindow: WindowLayout? = profile.windows[0]
        var selectedWindows: Set<UUID> = [profile.windows[0].id]
        
        // 模擬清空選擇
        selectedWindow = nil
        selectedWindows.removeAll()
        
        #expect(selectedWindow == nil)
        #expect(selectedWindows.isEmpty)
    }
    
    // MARK: - 懸停交互測試
    
    @Test("視窗懸停 - 工具提示顯示")
    func testWindowHoverTooltip() {
        let profile = createTestProfile()
        var hoveredWindow: WindowLayout? = nil
        var showingTooltip = false
        var tooltipPosition = CGPoint.zero
        
        let testWindow = profile.windows[0]
        let testPosition = CGPoint(x: 100, y: 200)
        
        // 模擬懸停開始
        hoveredWindow = testWindow
        tooltipPosition = testPosition
        
        // 模擬延遲顯示工具提示
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            if hoveredWindow?.id == testWindow.id {
                showingTooltip = true
            }
        }
        
        #expect(hoveredWindow?.id == testWindow.id)
        #expect(tooltipPosition == testPosition)
        
        // 模擬懸停結束
        hoveredWindow = nil
        showingTooltip = false
        
        #expect(hoveredWindow == nil)
        #expect(showingTooltip == false)
    }
    
    @Test("視窗懸停 - 快速移動")
    func testWindowHoverQuickMovement() {
        let profile = createTestProfile()
        var hoveredWindow: WindowLayout? = nil
        var showingTooltip = false
        
        // 模擬快速在多個視窗間移動
        for window in profile.windows {
            hoveredWindow = window
            // 立即移動到下一個視窗，不應該顯示工具提示
        }
        
        hoveredWindow = nil
        
        #expect(hoveredWindow == nil)
        #expect(showingTooltip == false)
    }
    
    // MARK: - 鍵盤導航測試
    
    @Test("鍵盤導航 - 方向鍵移動")
    func testKeyboardNavigationArrowKeys() {
        let profile = createTestProfile()
        var focusedWindowIndex = 0
        
        // 模擬向下箭頭
        let newIndex = min(focusedWindowIndex + 1, profile.windows.count - 1)
        focusedWindowIndex = newIndex
        
        #expect(focusedWindowIndex == 1)
        
        // 模擬向上箭頭
        let prevIndex = max(focusedWindowIndex - 1, 0)
        focusedWindowIndex = prevIndex
        
        #expect(focusedWindowIndex == 0)
        
        // 模擬超出邊界
        focusedWindowIndex = max(focusedWindowIndex - 1, 0)
        #expect(focusedWindowIndex == 0) // 應該保持在邊界內
        
        focusedWindowIndex = min(focusedWindowIndex + 10, profile.windows.count - 1)
        #expect(focusedWindowIndex == profile.windows.count - 1) // 應該保持在邊界內
    }
    
    @Test("鍵盤導航 - 空格鍵多選切換")
    func testKeyboardNavigationSpaceToggle() {
        var isMultiSelectMode = false
        var selectedWindows: Set<UUID> = []
        let profile = createTestProfile()
        
        // 模擬空格鍵切換多選模式
        isMultiSelectMode.toggle()
        #expect(isMultiSelectMode == true)
        
        // 在多選模式下選擇視窗
        selectedWindows.insert(profile.windows[0].id)
        selectedWindows.insert(profile.windows[1].id)
        
        // 模擬退出多選模式
        isMultiSelectMode.toggle()
        #expect(isMultiSelectMode == false)
        
        // 退出多選模式時應該保留最後選中的視窗
        if selectedWindows.count > 1 {
            let lastSelected = profile.windows.first { selectedWindows.contains($0.id) }
            if let lastSelected = lastSelected {
                selectedWindows = [lastSelected.id]
            }
        }
        
        #expect(selectedWindows.count <= 1)
    }
    
    @Test("鍵盤導航 - 回車鍵選擇")
    func testKeyboardNavigationEnterSelect() {
        let profile = createTestProfile()
        var focusedWindowIndex = 1
        var selectedWindow: WindowLayout? = nil
        var selectedWindows: Set<UUID> = []
        
        // 模擬回車鍵選擇當前焦點視窗
        let focusedWindow = profile.windows[focusedWindowIndex]
        selectedWindow = focusedWindow
        selectedWindows = [focusedWindow.id]
        
        #expect(selectedWindow?.id == focusedWindow.id)
        #expect(selectedWindows.contains(focusedWindow.id))
    }
    
    // MARK: - 滾動交互測試
    
    @Test("滾動交互 - 虛擬化滾動")
    func testVirtualizedScrolling() {
        let manager = VirtualizationManager()
        let windows = (0..<100).map { i in
            WindowLayout(
                app: "App\(i)",
                bundleID: "com.app\(i)",
                title: "Window \(i)",
                frame: WindowLayout.WindowFrame(x: 0, y: Double(i * 50), w: 200, h: 100)
            )
        }
        
        let config = VirtualizationManager.VirtualizationConfig(
            bufferSize: 10,
            itemHeight: 120,
            itemWidth: 160,
            enableVirtualization: true
        )
        manager.updateConfig(config)
        
        // 模擬滾動到不同位置
        let scrollPositions: [CGPoint] = [
            CGPoint(x: 0, y: 0),
            CGPoint(x: 0, y: 500),
            CGPoint(x: 0, y: 1000),
            CGPoint(x: 0, y: 2000)
        ]
        
        for scrollOffset in scrollPositions {
            let visibleRange = manager.updateVisibleRange(
                viewportSize: CGSize(width: 800, height: 600),
                scrollOffset: scrollOffset,
                totalItems: windows.count,
                mode: .grid
            )
            
            let virtualizedItems = manager.getVirtualizedItems(from: windows)
            let visibleItems = virtualizedItems.filter { $0.isVisible }
            
            #expect(visibleRange.startIndex >= 0)
            #expect(visibleRange.endIndex <= windows.count)
            #expect(visibleItems.count <= config.bufferSize * 2) // 緩衝區大小的兩倍
        }
    }
    
    @Test("滾動交互 - 快速滾動性能")
    func testFastScrollingPerformance() {
        let manager = VirtualizationManager()
        let windows = (0..<500).map { i in
            WindowLayout(
                app: "App\(i)",
                bundleID: "com.app\(i)",
                title: "Window \(i)",
                frame: WindowLayout.WindowFrame(x: 0, y: Double(i * 50), w: 200, h: 100)
            )
        }
        
        let config = VirtualizationManager.VirtualizationConfig(
            bufferSize: 20,
            itemHeight: 120,
            itemWidth: 160,
            enableVirtualization: true
        )
        manager.updateConfig(config)
        
        let startTime = Date()
        
        // 模擬快速滾動
        for i in 0..<100 {
            let scrollOffset = CGPoint(x: 0, y: CGFloat(i * 50))
            let _ = manager.updateVisibleRange(
                viewportSize: CGSize(width: 800, height: 600),
                scrollOffset: scrollOffset,
                totalItems: windows.count,
                mode: .list
            )
            
            let _ = manager.getVirtualizedItems(from: windows)
        }
        
        let elapsedTime = Date().timeIntervalSince(startTime)
        
        #expect(elapsedTime < 0.1) // 快速滾動應該在100ms內完成
    }
    
    // MARK: - 拖拽交互測試
    
    @Test("拖拽交互 - 視窗拖拽選擇")
    func testWindowDragSelection() {
        let profile = createTestProfile()
        var selectedWindows: Set<UUID> = []
        var isDragging = false
        var dragStartPoint = CGPoint.zero
        var dragCurrentPoint = CGPoint.zero
        
        // 模擬開始拖拽
        dragStartPoint = CGPoint(x: 50, y: 50)
        isDragging = true
        
        #expect(isDragging == true)
        #expect(dragStartPoint == CGPoint(x: 50, y: 50))
        
        // 模擬拖拽過程
        dragCurrentPoint = CGPoint(x: 300, y: 300)
        
        // 計算拖拽矩形
        let dragRect = CGRect(
            x: min(dragStartPoint.x, dragCurrentPoint.x),
            y: min(dragStartPoint.y, dragCurrentPoint.y),
            width: abs(dragCurrentPoint.x - dragStartPoint.x),
            height: abs(dragCurrentPoint.y - dragStartPoint.y)
        )
        
        // 模擬檢查哪些視窗在拖拽矩形內
        for window in profile.windows {
            let windowRect = CGRect(
                x: window.frame.x,
                y: window.frame.y,
                width: window.frame.w,
                height: window.frame.h
            )
            
            if dragRect.intersects(windowRect) {
                selectedWindows.insert(window.id)
            }
        }
        
        // 模擬結束拖拽
        isDragging = false
        
        #expect(isDragging == false)
        #expect(selectedWindows.count >= 0)
    }
    
    // MARK: - 縮放交互測試
    
    @Test("縮放交互 - 捏合縮放")
    func testPinchZoom() {
        var zoomScale: CGFloat = 1.0
        var zoomOffset = CGPoint.zero
        
        // 模擬放大
        let newScale = zoomScale * 1.5
        zoomScale = max(0.5, min(newScale, 3.0)) // 限制縮放範圍
        
        #expect(zoomScale == 1.5)
        
        // 模擬縮小
        let smallerScale = zoomScale * 0.7
        zoomScale = max(0.5, min(smallerScale, 3.0))
        
        #expect(zoomScale > 1.0)
        
        // 模擬超出範圍的縮放
        let tooLargeScale = zoomScale * 10
        zoomScale = max(0.5, min(tooLargeScale, 3.0))
        
        #expect(zoomScale == 3.0) // 應該被限制在最大值
        
        let tooSmallScale = zoomScale * 0.1
        zoomScale = max(0.5, min(tooSmallScale, 3.0))
        
        #expect(zoomScale == 0.5) // 應該被限制在最小值
    }
    
    @Test("縮放交互 - 雙擊縮放")
    func testDoubleTapZoom() {
        var zoomScale: CGFloat = 1.0
        var zoomOffset = CGPoint.zero
        let tapLocation = CGPoint(x: 400, y: 300)
        
        // 模擬雙擊放大
        if zoomScale == 1.0 {
            zoomScale = 2.0
            // 計算縮放中心偏移
            zoomOffset = CGPoint(
                x: -(tapLocation.x * (zoomScale - 1)),
                y: -(tapLocation.y * (zoomScale - 1))
            )
        } else {
            zoomScale = 1.0
            zoomOffset = .zero
        }
        
        #expect(zoomScale == 2.0)
        #expect(zoomOffset.x < 0) // 應該向左偏移
        #expect(zoomOffset.y < 0) // 應該向上偏移
        
        // 模擬再次雙擊恢復
        if zoomScale == 2.0 {
            zoomScale = 1.0
            zoomOffset = .zero
        }
        
        #expect(zoomScale == 1.0)
        #expect(zoomOffset == .zero)
    }
    
    // MARK: - 搜索交互測試
    
    @Test("搜索交互 - 視窗搜索")
    func testWindowSearch() {
        let profile = createTestProfile()
        var searchText = ""
        var filteredWindows: [WindowLayout] = []
        
        // 模擬搜索應用程式名稱
        searchText = "Safari"
        filteredWindows = profile.windows.filter { window in
            window.app.localizedCaseInsensitiveContains(searchText) ||
            window.title.localizedCaseInsensitiveContains(searchText)
        }
        
        #expect(filteredWindows.count == 1)
        #expect(filteredWindows[0].app == "Safari")
        
        // 模擬搜索視窗標題
        searchText = "Test"
        filteredWindows = profile.windows.filter { window in
            window.app.localizedCaseInsensitiveContains(searchText) ||
            window.title.localizedCaseInsensitiveContains(searchText)
        }
        
        #expect(filteredWindows.count >= 2) // 應該找到包含 "Test" 的視窗
        
        // 模擬清空搜索
        searchText = ""
        filteredWindows = profile.windows
        
        #expect(filteredWindows.count == profile.windows.count)
    }
    
    @Test("搜索交互 - 實時搜索")
    func testRealTimeSearch() {
        let profile = createTestProfile()
        var searchText = ""
        var filteredWindows: [WindowLayout] = []
        
        let searchQueries = ["S", "Sa", "Saf", "Safa", "Safar", "Safari"]
        
        for query in searchQueries {
            searchText = query
            filteredWindows = profile.windows.filter { window in
                window.app.localizedCaseInsensitiveContains(searchText) ||
                window.title.localizedCaseInsensitiveContains(searchText)
            }
            
            // 隨著搜索詞變長，結果應該更精確
            if query == "Safari" {
                #expect(filteredWindows.count == 1)
                #expect(filteredWindows[0].app == "Safari")
            }
        }
    }
    
    // MARK: - 錯誤處理交互測試
    
    @Test("錯誤處理 - 無效視窗選擇")
    func testInvalidWindowSelection() {
        let profile = createTestProfile()
        var selectedWindow: WindowLayout? = nil
        var selectedWindows: Set<UUID> = []
        
        // 模擬選擇不存在的視窗ID
        let invalidId = UUID()
        selectedWindows.insert(invalidId)
        
        // 清理無效選擇
        let validWindowIds = Set(profile.windows.map { $0.id })
        selectedWindows = selectedWindows.intersection(validWindowIds)
        
        #expect(selectedWindows.isEmpty)
        #expect(selectedWindow == nil)
    }
    
    @Test("錯誤處理 - 空配置文件")
    func testEmptyProfile() {
        let emptyProfile = Profile(name: "Empty Profile", windows: [])
        var selectedWindow: WindowLayout? = nil
        var selectedWindows: Set<UUID> = []
        var errorState: String? = nil
        
        // 模擬處理空配置文件
        if emptyProfile.windows.isEmpty {
            errorState = "沒有視窗可以預覽"
            selectedWindow = nil
            selectedWindows.removeAll()
        }
        
        #expect(errorState == "沒有視窗可以預覽")
        #expect(selectedWindow == nil)
        #expect(selectedWindows.isEmpty)
    }
}