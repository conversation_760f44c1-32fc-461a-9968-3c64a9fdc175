import Foundation
@testable import Workspace

/// 簡單的驗證測試，不依賴 Testing 框架
class ProfileManagerSpaceValidation {
    
    static func runValidationTests() {
        print("開始 ProfileManager Space 整合驗證測試...")
        
        testSpaceAwareFiltering()
        testProfileGrouping()
        testSpaceIDInference()
        testMoveProfileLogic()
        
        print("所有驗證測試完成！")
    }
    
    private static func testSpaceAwareFiltering() {
        print("測試 Space 感知過濾功能...")
        
        let profileManager = ProfileManager.shared
        
        // 建立測試設定檔
        let testProfiles = [
            Profile(name: "Global Profile 1"),
            Profile(name: "Space 1 Profile", spaceID: 1, isSpaceSpecific: true),
            Profile(name: "Space 2 Profile", spaceID: 2, isSpaceSpecific: true),
            Profile(name: "Global Profile 2")
        ]
        
        // 暫時設置測試資料
        profileManager.profiles = testProfiles
        
        // 測試獲取 Space 1 的設定檔
        let space1Profiles = profileManager.getProfilesForSpace(1)
        assert(space1Profiles.count == 1, "Space 1 應該有 1 個設定檔")
        assert(space1Profiles.first?.name == "Space 1 Profile", "Space 1 設定檔名稱不正確")
        
        // 測試獲取全域設定檔
        let globalProfiles = profileManager.getGlobalProfiles()
        assert(globalProfiles.count == 2, "應該有 2 個全域設定檔")
        
        // 測試獲取 Space 感知設定檔
        let spaceAwareProfiles = profileManager.getSpaceAwareProfiles()
        assert(spaceAwareProfiles.count == 2, "應該有 2 個 Space 感知設定檔")
        
        print("✅ Space 感知過濾功能測試通過")
    }
    
    private static func testProfileGrouping() {
        print("測試設定檔分組功能...")
        
        let profileManager = ProfileManager.shared
        
        let testProfiles = [
            Profile(name: "Space 1 Profile 1", spaceID: 1, isSpaceSpecific: true),
            Profile(name: "Space 1 Profile 2", spaceID: 1, isSpaceSpecific: true),
            Profile(name: "Space 2 Profile", spaceID: 2, isSpaceSpecific: true)
        ]
        
        profileManager.profiles = testProfiles
        
        let groupedProfiles = profileManager.getProfilesGroupedBySpace()
        
        assert(groupedProfiles[1]?.count == 2, "Space 1 應該有 2 個設定檔")
        assert(groupedProfiles[2]?.count == 1, "Space 2 應該有 1 個設定檔")
        assert(groupedProfiles[3]?.count == 0, "Space 3 應該有 0 個設定檔")
        
        // 測試檢查 Space 是否有設定檔
        assert(profileManager.hasProfilesForSpace(1) == true, "Space 1 應該有設定檔")
        assert(profileManager.hasProfilesForSpace(4) == false, "Space 4 不應該有設定檔")
        
        print("✅ 設定檔分組功能測試通過")
    }
    
    private static func testSpaceIDInference() {
        print("測試 Space ID 推斷功能...")
        
        // 測試檔案名稱推斷邏輯
        let testCases = [
            ("Profile_space1.json", 1),
            ("Test Profile_space2.json", 2),
            ("Another_space3.json", 3),
            ("NoSpace.json", nil),
            ("Profile_space.json", nil)
        ]
        
        for (fileName, expectedSpaceID) in testCases {
            var inferredSpaceID: Int? = nil
            
            if fileName.contains("_space") {
                let components = fileName.components(separatedBy: "_space")
                if components.count >= 2 {
                    let spaceComponent = components[1].replacingOccurrences(of: ".json", with: "")
                    if !spaceComponent.isEmpty {
                        inferredSpaceID = Int(spaceComponent)
                    }
                }
            }
            
            assert(inferredSpaceID == expectedSpaceID, "檔案名稱 \(fileName) 推斷結果不正確")
        }
        
        print("✅ Space ID 推斷功能測試通過")
    }
    
    private static func testMoveProfileLogic() {
        print("測試設定檔移動邏輯...")
        
        let originalProfile = Profile(name: "Movable Profile", spaceID: 1, isSpaceSpecific: true)
        let targetSpaceID = 2
        
        // 模擬移動邏輯
        var movedProfile = originalProfile
        movedProfile.spaceID = targetSpaceID
        movedProfile.isSpaceSpecific = true
        
        assert(movedProfile.spaceID == targetSpaceID, "移動後的 Space ID 不正確")
        assert(movedProfile.isSpaceSpecific == true, "移動後應該保持 Space 專用")
        assert(movedProfile.name == originalProfile.name, "移動後名稱應該保持不變")
        
        // 檢查檔案名稱更新
        assert(movedProfile.jsonFileName.contains("_space\(targetSpaceID)"), "移動後檔案名稱應該包含新的 Space ID")
        
        print("✅ 設定檔移動邏輯測試通過")
    }
}

// 如果直接執行此檔案，運行驗證測試
#if DEBUG
// ProfileManagerSpaceValidation.runValidationTests()
#endif