import Testing
import Foundation
import SwiftUI
@testable @testable import Workspace

// MARK: - PreviewConfigurationManager 單元測試
@Suite("PreviewConfigurationManager Tests")
struct PreviewConfigurationManagerTests {
    
    // MARK: - 測試輔助方法
    
    private func createTestUserDefaults() -> UserDefaults {
        let suiteName = "test.preview.configuration.\(UUID().uuidString)"
        return UserDefaults(suiteName: suiteName)!
    }
    
    private func createTestManager(with userDefaults: UserDefaults) -> PreviewConfigurationManager {
        // 由於 PreviewConfigurationManager 是單例，我們需要創建一個測試版本
        // 這裡我們直接測試其行為，而不是創建新實例
        return PreviewConfigurationManager.shared
    }
    
    // MARK: - 初始化測試
    
    @Test("初始化 - 默認配置")
    func testInitializationWithDefaults() {
        let manager = PreviewConfigurationManager.shared
        
        // 檢查初始配置是否為默認值或已保存的值
        #expect(PreviewMode.allCases.contains(manager.currentConfiguration.mode))
        #expect(manager.currentConfiguration.maxWindowsPerView > 0)
    }
    
    // MARK: - 配置更新測試
    
    @Test("更新配置")
    func testUpdateConfiguration() {
        let manager = PreviewConfigurationManager.shared
        let originalConfig = manager.currentConfiguration
        
        var newConfig = PreviewConfiguration.default
        newConfig.mode = .grid
        newConfig.showLabels = false
        newConfig.maxWindowsPerView = 25
        
        manager.updateConfiguration(newConfig)
        
        #expect(manager.currentConfiguration.mode == .grid)
        #expect(manager.currentConfiguration.showLabels == false)
        #expect(manager.currentConfiguration.maxWindowsPerView == 25)
        
        // 恢復原始配置
        manager.updateConfiguration(originalConfig)
    }
    
    @Test("更新預覽模式")
    func testUpdatePreviewMode() {
        let manager = PreviewConfigurationManager.shared
        let originalMode = manager.currentConfiguration.mode
        
        manager.updatePreviewMode(.list)
        #expect(manager.currentConfiguration.mode == .list)
        
        manager.updatePreviewMode(.miniMap)
        #expect(manager.currentConfiguration.mode == .miniMap)
        
        // 恢復原始模式
        manager.updatePreviewMode(originalMode)
    }
    
    @Test("優化配置 - 少量視窗")
    func testOptimizeConfigurationFewWindows() {
        let manager = PreviewConfigurationManager.shared
        let originalConfig = manager.currentConfiguration
        
        manager.optimizeConfiguration(for: 5)
        
        // 少量視窗應該使用縮放模式
        #expect(manager.currentConfiguration.mode == .scaled)
        
        // 恢復原始配置
        manager.updateConfiguration(originalConfig)
    }
    
    @Test("優化配置 - 中等數量視窗")
    func testOptimizeConfigurationMediumWindows() {
        let manager = PreviewConfigurationManager.shared
        let originalConfig = manager.currentConfiguration
        
        manager.optimizeConfiguration(for: 20)
        
        // 中等數量視窗應該使用縮放模式並啟用重疊指示器
        #expect(manager.currentConfiguration.mode == .scaled)
        #expect(manager.currentConfiguration.showOverlapIndicators == true)
        
        // 恢復原始配置
        manager.updateConfiguration(originalConfig)
    }
    
    @Test("優化配置 - 大量視窗")
    func testOptimizeConfigurationManyWindows() {
        let manager = PreviewConfigurationManager.shared
        let originalConfig = manager.currentConfiguration
        
        manager.optimizeConfiguration(for: 50)
        
        // 大量視窗應該使用網格模式並關閉一些功能以提高性能
        #expect(manager.currentConfiguration.mode == .grid)
        #expect(manager.currentConfiguration.showLabels == false)
        #expect(manager.currentConfiguration.enableAnimations == false)
        
        // 恢復原始配置
        manager.updateConfiguration(originalConfig)
    }
    
    // MARK: - 視窗選擇測試
    
    @Test("最後選中視窗ID")
    func testLastSelectedWindowId() {
        let manager = PreviewConfigurationManager.shared
        let originalWindowId = manager.lastSelectedWindowId
        
        let testWindowId = UUID()
        manager.lastSelectedWindowId = testWindowId
        
        #expect(manager.lastSelectedWindowId == testWindowId)
        
        // 恢復原始值
        manager.lastSelectedWindowId = originalWindowId
    }
    
    // MARK: - 配置持久化測試（模擬）
    
    @Test("配置變更通知")
    func testConfigurationChangeNotification() async {
        let manager = PreviewConfigurationManager.shared
        let originalConfig = manager.currentConfiguration
        
        var receivedNotification = false
        var newConfig = PreviewConfiguration.default
        newConfig.mode = .grid
        
        // 監聽配置變更
        let cancellable = manager.$currentConfiguration.sink { _ in
            receivedNotification = true
        }
        
        // 更新配置
        manager.updateConfiguration(newConfig)
        
        // 等待通知
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
        
        #expect(receivedNotification == true)
        
        cancellable.cancel()
        
        // 恢復原始配置
        manager.updateConfiguration(originalConfig)
    }
    
    // MARK: - 邊界情況測試
    
    @Test("邊界情況 - 零視窗優化")
    func testOptimizeConfigurationZeroWindows() {
        let manager = PreviewConfigurationManager.shared
        let originalConfig = manager.currentConfiguration
        
        manager.optimizeConfiguration(for: 0)
        
        // 即使是零視窗，也應該有有效的配置
        #expect(PreviewMode.allCases.contains(manager.currentConfiguration.mode))
        #expect(manager.currentConfiguration.maxWindowsPerView > 0)
        
        // 恢復原始配置
        manager.updateConfiguration(originalConfig)
    }
    
    @Test("邊界情況 - 負數視窗優化")
    func testOptimizeConfigurationNegativeWindows() {
        let manager = PreviewConfigurationManager.shared
        let originalConfig = manager.currentConfiguration
        
        manager.optimizeConfiguration(for: -5)
        
        // 負數應該被處理為有效配置
        #expect(PreviewMode.allCases.contains(manager.currentConfiguration.mode))
        #expect(manager.currentConfiguration.maxWindowsPerView > 0)
        
        // 恢復原始配置
        manager.updateConfiguration(originalConfig)
    }
    
    @Test("邊界情況 - 極大數量視窗優化")
    func testOptimizeConfigurationVeryManyWindows() {
        let manager = PreviewConfigurationManager.shared
        let originalConfig = manager.currentConfiguration
        
        manager.optimizeConfiguration(for: 1000)
        
        // 極大數量視窗應該使用最優化的配置
        #expect(manager.currentConfiguration.mode == .grid)
        #expect(manager.currentConfiguration.showLabels == false)
        #expect(manager.currentConfiguration.enableAnimations == false)
        
        // 恢復原始配置
        manager.updateConfiguration(originalConfig)
    }
    
    // MARK: - 配置一致性測試
    
    @Test("配置一致性 - 所有模式")
    func testConfigurationConsistencyAllModes() {
        let manager = PreviewConfigurationManager.shared
        let originalConfig = manager.currentConfiguration
        
        for mode in PreviewMode.allCases {
            manager.updatePreviewMode(mode)
            
            #expect(manager.currentConfiguration.mode == mode)
            #expect(manager.currentConfiguration.maxWindowsPerView > 0)
            #expect(manager.currentConfiguration.maxWindowsPerView <= 200) // 合理的上限
        }
        
        // 恢復原始配置
        manager.updateConfiguration(originalConfig)
    }
    
    @Test("配置驗證 - 有效範圍")
    func testConfigurationValidation() {
        let manager = PreviewConfigurationManager.shared
        let originalConfig = manager.currentConfiguration
        
        var testConfig = PreviewConfiguration.default
        testConfig.maxWindowsPerView = 100
        
        manager.updateConfiguration(testConfig)
        
        #expect(manager.currentConfiguration.maxWindowsPerView == 100)
        #expect(manager.currentConfiguration.maxWindowsPerView > 0)
        
        // 恢復原始配置
        manager.updateConfiguration(originalConfig)
    }
    
    // MARK: - 性能測試
    
    @Test("性能 - 快速配置更新")
    func testPerformanceFastConfigurationUpdates() {
        let manager = PreviewConfigurationManager.shared
        let originalConfig = manager.currentConfiguration
        
        let startTime = Date()
        
        // 執行100次配置更新
        for i in 0..<100 {
            var config = PreviewConfiguration.default
            config.maxWindowsPerView = i + 1
            manager.updateConfiguration(config)
        }
        
        let elapsedTime = Date().timeIntervalSince(startTime)
        
        // 100次更新應該在0.1秒內完成
        #expect(elapsedTime < 0.1)
        
        // 恢復原始配置
        manager.updateConfiguration(originalConfig)
    }
    
    @Test("性能 - 快速模式切換")
    func testPerformanceFastModeSwitch() {
        let manager = PreviewConfigurationManager.shared
        let originalMode = manager.currentConfiguration.mode
        
        let startTime = Date()
        
        // 執行100次模式切換
        for i in 0..<100 {
            let mode = PreviewMode.allCases[i % PreviewMode.allCases.count]
            manager.updatePreviewMode(mode)
        }
        
        let elapsedTime = Date().timeIntervalSince(startTime)
        
        // 100次切換應該在0.05秒內完成
        #expect(elapsedTime < 0.05)
        
        // 恢復原始模式
        manager.updatePreviewMode(originalMode)
    }
}