import Testing
import Foundation
import <PERSON><PERSON>
@testable @testable import Workspace

// MARK: - PreviewModels 單元測試
@Suite("PreviewModels Tests")
struct PreviewModelsTests {
    
    // MARK: - PreviewMode 測試
    
    @Test("PreviewMode - 所有案例")
    func testPreviewModeAllCases() {
        let allCases = PreviewMode.allCases
        #expect(allCases.count == 4)
        #expect(allCases.contains(.scaled))
        #expect(allCases.contains(.grid))
        #expect(allCases.contains(.list))
        #expect(allCases.contains(.miniMap))
    }
    
    @Test("PreviewMode - 顯示名稱")
    func testPreviewModeDisplayNames() {
        #expect(PreviewMode.scaled.displayName == "縮放預覽")
        #expect(PreviewMode.grid.displayName == "網格預覽")
        #expect(PreviewMode.list.displayName == "列表預覽")
        #expect(PreviewMode.miniMap.displayName == "小地圖")
    }
    
    @Test("PreviewMode - 圖標")
    func testPreviewModeIcons() {
        #expect(PreviewMode.scaled.icon == "rectangle.3.group")
        #expect(PreviewMode.grid.icon == "grid")
        #expect(PreviewMode.list.icon == "list.bullet")
        #expect(PreviewMode.miniMap.icon == "map")
    }
    
    @Test("PreviewMode - 描述")
    func testPreviewModeDescriptions() {
        for mode in PreviewMode.allCases {
            #expect(!mode.description.isEmpty)
        }
    }
    
    @Test("PreviewMode - Codable")
    func testPreviewModeCodable() throws {
        for mode in PreviewMode.allCases {
            let encoded = try JSONEncoder().encode(mode)
            let decoded = try JSONDecoder().decode(PreviewMode.self, from: encoded)
            #expect(decoded == mode)
        }
    }
    
    @Test("PreviewMode - 無效值解碼")
    func testPreviewModeInvalidDecoding() throws {
        let invalidJSON = "\"invalid_mode\"".data(using: .utf8)!
        let decoded = try JSONDecoder().decode(PreviewMode.self, from: invalidJSON)
        #expect(decoded == .scaled) // 應該回退到默認值
    }
    
    // MARK: - PreviewConfiguration 測試
    
    @Test("PreviewConfiguration - 默認配置")
    func testPreviewConfigurationDefault() {
        let config = PreviewConfiguration.default
        
        #expect(config.mode == .scaled)
        #expect(config.showLabels == true)
        #expect(config.showOverlapIndicators == true)
        #expect(config.groupByApplication == false)
        #expect(config.maxWindowsPerView == 50)
        #expect(config.enableAnimations == true)
        #expect(config.showWindowDetails == true)
    }
    
    @Test("PreviewConfiguration - 優化配置 - 少量視窗")
    func testPreviewConfigurationOptimizedFewWindows() {
        let config = PreviewConfiguration.optimized(for: 10)
        
        #expect(config.mode == .scaled)
        #expect(config.showOverlapIndicators == true)
    }
    
    @Test("PreviewConfiguration - 優化配置 - 中等數量視窗")
    func testPreviewConfigurationOptimizedMediumWindows() {
        let config = PreviewConfiguration.optimized(for: 20)
        
        #expect(config.mode == .scaled)
        #expect(config.showOverlapIndicators == true)
    }
    
    @Test("PreviewConfiguration - 優化配置 - 大量視窗")
    func testPreviewConfigurationOptimizedManyWindows() {
        let config = PreviewConfiguration.optimized(for: 50)
        
        #expect(config.mode == .grid)
        #expect(config.showLabels == false)
        #expect(config.enableAnimations == false)
    }
    
    @Test("PreviewConfiguration - Codable")
    func testPreviewConfigurationCodable() throws {
        let config = PreviewConfiguration.default
        let encoded = try JSONEncoder().encode(config)
        let decoded = try JSONDecoder().decode(PreviewConfiguration.self, from: encoded)
        
        #expect(decoded == config)
    }
    
    // MARK: - WindowDisplayInfo 測試
    
    private func createTestWindow() -> WindowLayout {
        return WindowLayout(
            app: "Test App",
            bundleID: "com.test.app",
            title: "Test Window",
            frame: WindowLayout.WindowFrame(x: 100, y: 100, w: 800, h: 600)
        )
    }
    
    @Test("WindowDisplayInfo - 初始化")
    func testWindowDisplayInfoInitialization() {
        let window = createTestWindow()
        let displayFrame = CGRect(x: 50, y: 50, width: 400, height: 300)
        
        let info = WindowDisplayInfo(
            window: window,
            displayFrame: displayFrame,
            isOverlapping: true,
            overlapLevel: 2,
            isVisible: true,
            opacity: 0.8,
            zIndex: 1
        )
        
        #expect(info.window.id == window.id)
        #expect(info.displayFrame == displayFrame)
        #expect(info.isOverlapping == true)
        #expect(info.overlapLevel == 2)
        #expect(info.isVisible == true)
        #expect(info.opacity == 0.8)
        #expect(info.zIndex == 1)
    }
    
    @Test("WindowDisplayInfo - 默認值")
    func testWindowDisplayInfoDefaults() {
        let window = createTestWindow()
        let displayFrame = CGRect(x: 50, y: 50, width: 400, height: 300)
        
        let info = WindowDisplayInfo(window: window, displayFrame: displayFrame)
        
        #expect(info.isOverlapping == false)
        #expect(info.overlapLevel == 0)
        #expect(info.isVisible == true)
        #expect(info.opacity == 1.0)
        #expect(info.zIndex == 0)
    }
    
    @Test("WindowDisplayInfo - 實際框架計算")
    func testWindowDisplayInfoActualFrame() {
        let window = createTestWindow()
        let displayFrame = CGRect(x: 50, y: 50, width: 400, height: 300)
        let info = WindowDisplayInfo(window: window, displayFrame: displayFrame)
        
        let expectedFrame = CGRect(x: 100, y: 100, width: 800, height: 600)
        #expect(info.actualFrame == expectedFrame)
    }
    
    @Test("WindowDisplayInfo - 可見性檢查")
    func testWindowDisplayInfoVisibility() {
        let window = createTestWindow()
        let displayFrame = CGRect(x: 50, y: 50, width: 400, height: 300)
        let info = WindowDisplayInfo(window: window, displayFrame: displayFrame)
        
        let bounds1 = CGRect(x: 0, y: 0, width: 500, height: 400)
        #expect(info.isVisible(in: bounds1) == true)
        
        let bounds2 = CGRect(x: 500, y: 500, width: 100, height: 100)
        #expect(info.isVisible(in: bounds2) == false)
    }
    
    @Test("WindowDisplayInfo - 重疊面積計算")
    func testWindowDisplayInfoOverlapArea() {
        let window1 = createTestWindow()
        let window2 = createTestWindow()
        
        let info1 = WindowDisplayInfo(
            window: window1,
            displayFrame: CGRect(x: 0, y: 0, width: 100, height: 100)
        )
        let info2 = WindowDisplayInfo(
            window: window2,
            displayFrame: CGRect(x: 50, y: 50, width: 100, height: 100)
        )
        
        let overlapArea = info1.overlapArea(with: info2)
        #expect(overlapArea == 2500) // 50x50 的重疊區域
    }
    
    // MARK: - ScreenBounds 測試
    
    @Test("ScreenBounds - 初始化")
    func testScreenBoundsInitialization() {
        let bounds = CGRect(x: 0, y: 0, width: 1920, height: 1080)
        let screenBounds = ScreenBounds(
            bounds: bounds,
            isPrimary: true,
            displayName: "主螢幕",
            scaleFactor: 2.0
        )
        
        #expect(screenBounds.bounds == bounds)
        #expect(screenBounds.isPrimary == true)
        #expect(screenBounds.displayName == "主螢幕")
        #expect(screenBounds.scaleFactor == 2.0)
    }
    
    @Test("ScreenBounds - 默認值")
    func testScreenBoundsDefaults() {
        let bounds = CGRect(x: 0, y: 0, width: 1920, height: 1080)
        let screenBounds = ScreenBounds(bounds: bounds)
        
        #expect(screenBounds.isPrimary == false)
        #expect(screenBounds.displayName == "Display")
        #expect(screenBounds.scaleFactor == 1.0)
    }
    
    @Test("ScreenBounds - 檢測螢幕邊界 - 空視窗")
    func testScreenBoundsDetectEmpty() {
        let screens = ScreenBounds.detectScreenBounds(from: [])
        
        #expect(screens.count == 1)
        #expect(screens[0].isPrimary == true)
        #expect(screens[0].displayName == "主螢幕")
    }
    
    @Test("ScreenBounds - 檢測螢幕邊界 - 單個視窗")
    func testScreenBoundsDetectSingleWindow() {
        let window = createTestWindow()
        let screens = ScreenBounds.detectScreenBounds(from: [window])
        
        #expect(screens.count == 1)
        #expect(screens[0].bounds.contains(CGPoint(x: 100, y: 100)))
        #expect(screens[0].bounds.width >= 800)
        #expect(screens[0].bounds.height >= 600)
    }
    
    @Test("ScreenBounds - 檢測螢幕邊界 - 多個視窗")
    func testScreenBoundsDetectMultipleWindows() {
        let windows = [
            WindowLayout(
                app: "App1", bundleID: "com.app1", title: "Window1",
                frame: WindowLayout.WindowFrame(x: 0, y: 0, w: 800, h: 600)
            ),
            WindowLayout(
                app: "App2", bundleID: "com.app2", title: "Window2",
                frame: WindowLayout.WindowFrame(x: 1000, y: 500, w: 600, h: 400)
            )
        ]
        
        let screens = ScreenBounds.detectScreenBounds(from: windows)
        
        #expect(screens.count == 1)
        #expect(screens[0].bounds.width >= 1600) // 應該包含所有視窗
        #expect(screens[0].bounds.height >= 900)
    }
    
    @Test("ScreenBounds - 包含視窗檢查")
    func testScreenBoundsContainsWindow() {
        let screenBounds = ScreenBounds(
            bounds: CGRect(x: 0, y: 0, width: 1920, height: 1080)
        )
        
        let windowInside = WindowLayout(
            app: "Inside App", bundleID: "com.inside", title: "Inside Window",
            frame: WindowLayout.WindowFrame(x: 100, y: 100, w: 800, h: 600)
        )
        
        let windowOutside = WindowLayout(
            app: "Outside App", bundleID: "com.outside", title: "Outside Window",
            frame: WindowLayout.WindowFrame(x: 2000, y: 2000, w: 800, h: 600)
        )
        
        #expect(screenBounds.contains(window: windowInside) == true)
        #expect(screenBounds.contains(window: windowOutside) == false)
    }
    
    @Test("ScreenBounds - 相對位置計算")
    func testScreenBoundsRelativePosition() {
        let screenBounds = ScreenBounds(
            bounds: CGRect(x: 0, y: 0, width: 1000, height: 1000)
        )
        
        // 中心視窗
        let centerWindow = WindowLayout(
            app: "Center App", bundleID: "com.center", title: "Center Window",
            frame: WindowLayout.WindowFrame(x: 450, y: 450, w: 100, h: 100)
        )
        
        let relativePos = screenBounds.relativePosition(for: centerWindow)
        
        // 應該接近 (0, 0) 因為視窗在螢幕中心
        #expect(abs(relativePos.x) < 0.1)
        #expect(abs(relativePos.y) < 0.1)
    }
    
    @Test("ScreenBounds - Codable")
    func testScreenBoundsCodable() throws {
        let screenBounds = ScreenBounds(
            bounds: CGRect(x: 0, y: 0, width: 1920, height: 1080),
            isPrimary: true,
            displayName: "主螢幕",
            scaleFactor: 2.0
        )
        
        let encoded = try JSONEncoder().encode(screenBounds)
        let decoded = try JSONDecoder().decode(ScreenBounds.self, from: encoded)
        
        #expect(decoded.bounds == screenBounds.bounds)
        #expect(decoded.isPrimary == screenBounds.isPrimary)
        #expect(decoded.displayName == screenBounds.displayName)
        #expect(decoded.scaleFactor == screenBounds.scaleFactor)
    }
    
    // MARK: - CGRect 擴展測試
    
    @Test("CGRect - 安全初始化")
    func testCGRectSafeInitialization() {
        // 正常值
        let normalRect = CGRect.safe(x: 10, y: 20, width: 100, height: 200)
        #expect(normalRect == CGRect(x: 10, y: 20, width: 100, height: 200))
        
        // 無限值
        let infiniteRect = CGRect.safe(x: Double.infinity, y: 10, width: 100, height: 200)
        #expect(infiniteRect.origin.x == 0)
        #expect(infiniteRect.origin.y == 10)
        
        // 負寬度/高度
        let negativeRect = CGRect.safe(x: 10, y: 20, width: -100, height: -200)
        #expect(negativeRect.width == 100)
        #expect(negativeRect.height == 100)
        
        // NaN 值
        let nanRect = CGRect.safe(x: Double.nan, y: 20, width: 100, height: 200)
        #expect(nanRect.origin.x == 0)
        #expect(nanRect.origin.y == 20)
    }
    
    @Test("CGRect - 重疊百分比計算")
    func testCGRectOverlapPercentage() {
        let rect1 = CGRect(x: 0, y: 0, width: 100, height: 100)
        let rect2 = CGRect(x: 50, y: 50, width: 100, height: 100)
        
        let overlapPercentage = rect1.overlapPercentage(with: rect2)
        
        // 重疊區域是 50x50 = 2500
        // 聯合區域是 100x100 + 100x100 - 50x50 = 17500
        // 重疊百分比應該是 2500/17500 ≈ 0.143
        #expect(abs(overlapPercentage - 0.142857) < 0.001)
    }
    
    @Test("CGRect - 無重疊百分比")
    func testCGRectNoOverlapPercentage() {
        let rect1 = CGRect(x: 0, y: 0, width: 100, height: 100)
        let rect2 = CGRect(x: 200, y: 200, width: 100, height: 100)
        
        let overlapPercentage = rect1.overlapPercentage(with: rect2)
        #expect(overlapPercentage == 0)
    }
    
    @Test("CGRect - 面積計算")
    func testCGRectArea() {
        let rect = CGRect(x: 10, y: 20, width: 100, height: 200)
        #expect(rect.area == 20000)
        
        let zeroRect = CGRect.zero
        #expect(zeroRect.area == 0)
    }
}