import Testing
import Foundation
@testable import Workspace

@Suite("ProfileManager Space Integration Tests")
struct ProfileManagerSpaceIntegrationTests {
    
    // MARK: - Test Data Setup
    
    private func createTestProfile(name: String, spaceID: Int? = nil, isSpaceSpecific: Bool = false) -> Profile {
        let windows = [
            WindowLayout(
                app: "Safari",
                bundleID: "com.apple.Safari",
                title: "Test Page",
                frame: WindowLayout.WindowFrame(x: 100, y: 100, w: 800, h: 600)
            )
        ]
        return Profile(name: name, windows: windows, isSpaceSpecific: isSpaceSpecific, spaceID: spaceID)
    }
    
    private func createTestProfiles() -> [Profile] {
        return [
            createTestProfile(name: "Global Profile 1"),
            createTestProfile(name: "Space 1 Profile 1", spaceID: 1, isSpaceSpecific: true),
            createTestProfile(name: "Space 1 Profile 2", spaceID: 1, isSpaceSpecific: true),
            createTestProfile(name: "Space 2 Profile 1", spaceID: 2, isSpaceSpecific: true),
            createTestProfile(name: "Space 3 Profile 1", spaceID: 3, isSpaceSpecific: true),
            createTestProfile(name: "Global Profile 2")
        ]
    }
    
    // MARK: - Space-aware Filtering Tests
    
    @Test("getProfilesForSpace - 獲取指定 Space 的設定檔")
    func testGetProfilesForSpace() {
        // 建立測試用的 ProfileManager 實例
        let profileManager = ProfileManager.shared
        
        // 模擬設定檔資料
        let testProfiles = createTestProfiles()
        profileManager.profiles = testProfiles
        
        // 測試獲取 Space 1 的設定檔
        let space1Profiles = profileManager.getProfilesForSpace(1)
        #expect(space1Profiles.count == 2)
        #expect(space1Profiles.allSatisfy { $0.spaceID == 1 })
        
        // 測試獲取 Space 2 的設定檔
        let space2Profiles = profileManager.getProfilesForSpace(2)
        #expect(space2Profiles.count == 1)
        #expect(space2Profiles.first?.spaceID == 2)
        
        // 測試獲取不存在設定檔的 Space
        let emptySpaceProfiles = profileManager.getProfilesForSpace(4)
        #expect(emptySpaceProfiles.isEmpty)
    }
    
    @Test("getSpaceAwareProfiles - 獲取所有 Space 感知設定檔")
    func testGetSpaceAwareProfiles() {
        let profileManager = ProfileManager.shared
        let testProfiles = createTestProfiles()
        profileManager.profiles = testProfiles
        
        let spaceAwareProfiles = profileManager.getSpaceAwareProfiles()
        #expect(spaceAwareProfiles.count == 4) // 4 個 Space 專用設定檔
        #expect(spaceAwareProfiles.allSatisfy { $0.isSpaceAware })
    }
    
    @Test("getGlobalProfiles - 獲取所有全域設定檔")
    func testGetGlobalProfiles() {
        let profileManager = ProfileManager.shared
        let testProfiles = createTestProfiles()
        profileManager.profiles = testProfiles
        
        let globalProfiles = profileManager.getGlobalProfiles()
        #expect(globalProfiles.count == 2) // 2 個全域設定檔
        #expect(globalProfiles.allSatisfy { !$0.isSpaceAware })
    }
    
    @Test("getProfilesGroupedBySpace - 按 Space 分組設定檔")
    func testGetProfilesGroupedBySpace() {
        let profileManager = ProfileManager.shared
        let testProfiles = createTestProfiles()
        profileManager.profiles = testProfiles
        
        let groupedProfiles = profileManager.getProfilesGroupedBySpace()
        
        // 檢查分組結果
        #expect(groupedProfiles[1]?.count == 2) // Space 1 有 2 個設定檔
        #expect(groupedProfiles[2]?.count == 1) // Space 2 有 1 個設定檔
        #expect(groupedProfiles[3]?.count == 1) // Space 3 有 1 個設定檔
        
        // 檢查每個分組中的設定檔都屬於正確的 Space
        #expect(groupedProfiles[1]?.allSatisfy { $0.spaceID == 1 } == true)
        #expect(groupedProfiles[2]?.allSatisfy { $0.spaceID == 2 } == true)
        #expect(groupedProfiles[3]?.allSatisfy { $0.spaceID == 3 } == true)
    }
    
    @Test("hasProfilesForSpace - 檢查 Space 是否有設定檔")
    func testHasProfilesForSpace() {
        let profileManager = ProfileManager.shared
        let testProfiles = createTestProfiles()
        profileManager.profiles = testProfiles
        
        #expect(profileManager.hasProfilesForSpace(1) == true)
        #expect(profileManager.hasProfilesForSpace(2) == true)
        #expect(profileManager.hasProfilesForSpace(3) == true)
        #expect(profileManager.hasProfilesForSpace(4) == false) // 沒有設定檔的 Space
    }
    
    // MARK: - Save Profile with Space Integration Tests
    
    @Test("saveProfile - 儲存帶 Space ID 的設定檔")
    func testSaveProfileWithSpaceID() {
        let profileManager = ProfileManager.shared
        let profile = createTestProfile(name: "Test Space Profile", spaceID: 2, isSpaceSpecific: true)
        
        // 模擬儲存操作（實際測試中可能需要 mock FileManager）
        // 這裡我們主要測試邏輯
        let originalProfilesCount = profileManager.profiles.count
        
        // 直接測試 saveProfile 的邏輯部分
        var profileToSave = profile
        
        // 驗證 Space ID 處理邏輯
        if profileToSave.isSpaceSpecific && profileToSave.spaceID == nil {
            profileToSave.spaceID = SpaceDetector.shared.getCurrentSpace() ?? SpaceDetector.shared.getDefaultSpaceID()
        }
        
        #expect(profileToSave.spaceID == 2)
        #expect(profileToSave.isSpaceSpecific == true)
    }
    
    @Test("saveProfile - 自動分配 Space ID")
    func testSaveProfileAutoAssignSpaceID() {
        let profileManager = ProfileManager.shared
        
        // 建立標記為 Space 專用但沒有 Space ID 的設定檔
        var profile = createTestProfile(name: "Auto Space Profile", isSpaceSpecific: true)
        profile.spaceID = nil
        
        // 模擬 saveProfile 的邏輯
        if profile.isSpaceSpecific && profile.spaceID == nil {
            profile.spaceID = SpaceDetector.shared.getCurrentSpace() ?? SpaceDetector.shared.getDefaultSpaceID()
        }
        
        #expect(profile.spaceID != nil)
        #expect(profile.spaceID == SpaceDetector.shared.getDefaultSpaceID() || profile.spaceID == SpaceDetector.shared.getCurrentSpace())
    }
    
    // MARK: - Space Information Validation Tests
    
    @Test("inferSpaceIDFromFileName - 從檔案名稱推斷 Space ID")
    func testInferSpaceIDFromFileName() {
        let profileManager = ProfileManager.shared
        
        // 使用反射或其他方式測試私有方法，或者建立公開的測試方法
        // 這裡我們測試檔案名稱格式
        let testCases = [
            ("Profile_space1.json", 1),
            ("Test Profile_space2.json", 2),
            ("Another_space3.json", 3),
            ("NoSpace.json", nil),
            ("Profile_space.json", nil) // 沒有數字
        ]
        
        for (fileName, expectedSpaceID) in testCases {
            // 模擬推斷邏輯
            var inferredSpaceID: Int? = nil
            
            if fileName.contains("_space") {
                let components = fileName.components(separatedBy: "_space")
                if components.count >= 2 {
                    let spaceComponent = components[1].replacingOccurrences(of: ".json", with: "")
                    if !spaceComponent.isEmpty {
                        inferredSpaceID = Int(spaceComponent)
                    }
                }
            }
            
            #expect(inferredSpaceID == expectedSpaceID, "檔案名稱 \(fileName) 應該推斷出 Space ID \(expectedSpaceID?.description ?? "nil")")
        }
    }
    
    // MARK: - Move Profile Between Spaces Tests
    
    @Test("moveProfileToSpace - 移動設定檔到不同 Space")
    func testMoveProfileToSpace() {
        let profileManager = ProfileManager.shared
        let originalProfile = createTestProfile(name: "Movable Profile", spaceID: 1, isSpaceSpecific: true)
        
        // 模擬移動邏輯
        let targetSpaceID = 2
        var movedProfile = originalProfile
        movedProfile.spaceID = targetSpaceID
        movedProfile.isSpaceSpecific = true
        
        #expect(movedProfile.spaceID == targetSpaceID)
        #expect(movedProfile.isSpaceSpecific == true)
        #expect(movedProfile.name == originalProfile.name)
        
        // 檢查檔案名稱是否正確更新
        #expect(movedProfile.jsonFileName.contains("_space\(targetSpaceID)"))
    }
    
    // MARK: - Edge Cases Tests
    
    @Test("getProfilesForSpace - 邊界情況測試")
    func testGetProfilesForSpaceEdgeCases() {
        let profileManager = ProfileManager.shared
        
        // 測試空設定檔列表
        profileManager.profiles = []
        let emptyResult = profileManager.getProfilesForSpace(1)
        #expect(emptyResult.isEmpty)
        
        // 測試負數 Space ID
        let testProfiles = [createTestProfile(name: "Negative Space", spaceID: -1)]
        profileManager.profiles = testProfiles
        let negativeSpaceResult = profileManager.getProfilesForSpace(-1)
        #expect(negativeSpaceResult.count == 1)
        
        // 測試零 Space ID
        let zeroSpaceProfiles = [createTestProfile(name: "Zero Space", spaceID: 0)]
        profileManager.profiles = zeroSpaceProfiles
        let zeroSpaceResult = profileManager.getProfilesForSpace(0)
        #expect(zeroSpaceResult.count == 1)
    }
    
    @Test("getProfilesGroupedBySpace - 空設定檔列表")
    func testGetProfilesGroupedBySpaceEmpty() {
        let profileManager = ProfileManager.shared
        profileManager.profiles = []
        
        let groupedProfiles = profileManager.getProfilesGroupedBySpace()
        
        // 應該仍然有 3 個 Space 的空陣列
        #expect(groupedProfiles.keys.count >= 3)
        #expect(groupedProfiles[1]?.isEmpty == true)
        #expect(groupedProfiles[2]?.isEmpty == true)
        #expect(groupedProfiles[3]?.isEmpty == true)
    }
    
    @Test("Space 感知設定檔的一致性檢查")
    func testSpaceAwareProfileConsistency() {
        let profileManager = ProfileManager.shared
        
        // 建立不一致的設定檔（標記為 Space 專用但沒有 Space ID）
        var inconsistentProfile = createTestProfile(name: "Inconsistent Profile", isSpaceSpecific: true)
        inconsistentProfile.spaceID = nil
        
        let testProfiles = [inconsistentProfile]
        profileManager.profiles = testProfiles
        
        // 檢查一致性
        let spaceAwareProfiles = profileManager.getSpaceAwareProfiles()
        #expect(spaceAwareProfiles.count == 1) // 仍然被認為是 Space 感知的
        
        let globalProfiles = profileManager.getGlobalProfiles()
        #expect(globalProfiles.isEmpty) // 不應該被認為是全域的
    }
}