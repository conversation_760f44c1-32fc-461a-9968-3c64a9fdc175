import XCTest
import Foundation
@testable import Workspace

/// 測試 ProfileMigrationManager 與其他組件的整合
class ProfileMigrationIntegrationTests: XCTestCase {
    
    var tempDirectory: URL!
    var originalLayoutsPath: URL!
    var testLayoutsPath: URL!
    
    override func setUp() {
        super.setUp()
        
        // 建立臨時測試目錄
        tempDirectory = FileManager.default.temporaryDirectory.appendingPathComponent(UUID().uuidString)
        testLayoutsPath = tempDirectory.appendingPathComponent(".hammerspoon/layouts")
        
        try! FileManager.default.createDirectory(at: testLayoutsPath, withIntermediateDirectories: true)
        
        // 備份原始路徑（如果存在）
        let homeURL = FileManager.default.homeDirectoryForCurrentUser
        originalLayoutsPath = homeURL.appendingPathComponent(".hammerspoon/layouts")
    }
    
    override func tearDown() {
        // 清理臨時目錄
        try? FileManager.default.removeItem(at: tempDirectory)
        super.tearDown()
    }
    
    // MARK: - Migration Integration Tests
    
    func testMigrationTriggeredByProfileManager() {
        // 建立舊版設定檔
        createLegacyProfile(name: "integration_test", windows: createSampleWindows())
        createLegacySpaceProfile(name: "space_integration", spaceID: 1, windows: createSampleWindows())
        
        // 驗證遷移狀態檢查
        let migrationManager = ProfileMigrationManager.shared
        
        // 測試遷移狀態結構
        let pendingStatus = MigrationStatus.pending()
        let completedStatus = MigrationStatus.completed()
        let notNeededStatus = MigrationStatus.notNeeded()
        
        XCTAssertFalse(pendingStatus.isCompleted, "待處理狀態應該未完成")
        XCTAssertTrue(completedStatus.isCompleted, "完成狀態應該已完成")
        XCTAssertTrue(notNeededStatus.isCompleted, "不需要遷移狀態應該標記為完成")
        
        XCTAssertNil(pendingStatus.completedAt, "待處理狀態不應該有完成時間")
        XCTAssertNotNil(completedStatus.completedAt, "完成狀態應該有完成時間")
        
        XCTAssertEqual(notNeededStatus.migratedProfileCount, 0, "不需要遷移的狀態應該有 0 個遷移檔案")
    }
    
    func testSpaceMappingCreation() {
        // 測試 Space 映射檔案的建立
        let spaceMapping: [String: Any] = [
            "spaceProfileMapping": [
                "1": ["profile1", "profile2"],
                "2": ["profile3"],
                "3": []
            ],
            "defaultSpace": 1,
            "lastUpdated": ISO8601DateFormatter().string(from: Date())
        ]
        
        // 測試映射檔案的序列化
        let data = try! JSONSerialization.data(withJSONObject: spaceMapping)
        let mappingFileURL = testLayoutsPath.appendingPathComponent("space_profile_mapping.json")
        try! data.write(to: mappingFileURL)
        
        // 驗證檔案建立成功
        XCTAssertTrue(FileManager.default.fileExists(atPath: mappingFileURL.path), "映射檔案應該建立成功")
        
        // 驗證檔案內容
        let loadedData = try! Data(contentsOf: mappingFileURL)
        let loadedMapping = try! JSONSerialization.jsonObject(with: loadedData) as! [String: Any]
        
        XCTAssertEqual(loadedMapping["defaultSpace"] as! Int, 1, "預設 Space 應該是 1")
        
        let profileMapping = loadedMapping["spaceProfileMapping"] as! [String: [String]]
        XCTAssertEqual(profileMapping["1"]?.count, 2, "Space 1 應該有 2 個設定檔")
        XCTAssertEqual(profileMapping["2"]?.count, 1, "Space 2 應該有 1 個設定檔")
        XCTAssertEqual(profileMapping["3"]?.count, 0, "Space 3 應該有 0 個設定檔")
    }
    
    func testMigrationErrorHandling() {
        // 測試各種遷移錯誤情況
        
        // 1. 測試驗證失敗錯誤
        let validationError = MigrationError.validationFailed("測試驗證錯誤")
        XCTAssertNotNil(validationError.errorDescription)
        XCTAssertTrue(validationError.errorDescription!.contains("資料驗證失敗"))
        
        // 2. 測試備份不存在錯誤
        let backupError = MigrationError.backupNotFound
        XCTAssertNotNil(backupError.errorDescription)
        XCTAssertTrue(backupError.errorDescription!.contains("找不到備份檔案"))
        
        // 3. 測試遷移失敗但已還原錯誤
        let testError = NSError(domain: "test", code: 1, userInfo: [NSLocalizedDescriptionKey: "測試錯誤"])
        let restoredError = MigrationError.migrationFailedButRestored(testError)
        XCTAssertNotNil(restoredError.errorDescription)
        XCTAssertTrue(restoredError.errorDescription!.contains("遷移失敗但已還原備份"))
        
        // 4. 測試遷移和還原都失敗錯誤
        let bothFailedError = MigrationError.migrationAndRestoreFailed(testError)
        XCTAssertNotNil(bothFailedError.errorDescription)
        XCTAssertTrue(bothFailedError.errorDescription!.contains("遷移失敗且還原備份也失敗"))
    }
    
    func testProfileFormatCompatibility() {
        // 測試不同格式設定檔的相容性
        
        // 1. 測試舊格式（純 WindowLayout 陣列）
        let windows = createSampleWindows()
        let legacyData = try! JSONEncoder().encode(windows)
        
        // 驗證可以解析為 WindowLayout 陣列
        let parsedWindows = try! JSONDecoder().decode([WindowLayout].self, from: legacyData)
        XCTAssertEqual(parsedWindows.count, windows.count)
        XCTAssertEqual(parsedWindows.first?.app, windows.first?.app)
        
        // 2. 測試 Space 舊格式
        let spaceData = SpaceLayoutData(
            windows: windows,
            spaceID: 1,
            screenUUID: "test-uuid",
            runningApps: [
                SpaceLayoutData.RunningApp(name: "Safari", bundleID: "com.apple.Safari", path: "/Applications/Safari.app")
            ],
            version: "1.0",
            type: "space",
            timestamp: Date().timeIntervalSince1970
        )
        
        let spaceDataEncoded = try! JSONEncoder().encode(spaceData)
        let parsedSpaceData = try! JSONDecoder().decode(SpaceLayoutData.self, from: spaceDataEncoded)
        
        XCTAssertEqual(parsedSpaceData.windows.count, windows.count)
        XCTAssertEqual(parsedSpaceData.spaceID, 1)
        XCTAssertEqual(parsedSpaceData.runningApps?.first?.name, "Safari")
        
        // 3. 測試新格式
        let newProfile = Profile(name: "new_format", windows: windows, isSpaceSpecific: true, spaceID: 1)
        
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let newData = try! encoder.encode(newProfile)
        
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        let parsedProfile = try! decoder.decode(Profile.self, from: newData)
        
        XCTAssertEqual(parsedProfile.name, newProfile.name)
        XCTAssertEqual(parsedProfile.isSpaceSpecific, newProfile.isSpaceSpecific)
        XCTAssertEqual(parsedProfile.spaceID, newProfile.spaceID)
        XCTAssertEqual(parsedProfile.windows.count, newProfile.windows.count)
    }
    
    func testMigrationResultHandling() {
        // 測試不同遷移結果的處理
        
        // 1. 測試成功結果
        let successResult = MigrationResult.success
        switch successResult {
        case .success:
            XCTAssertTrue(true, "成功結果應該匹配 .success")
        default:
            XCTFail("成功結果不應該匹配其他情況")
        }
        
        // 2. 測試已完成結果
        let completedResult = MigrationResult.alreadyCompleted
        switch completedResult {
        case .alreadyCompleted:
            XCTAssertTrue(true, "已完成結果應該匹配 .alreadyCompleted")
        default:
            XCTFail("已完成結果不應該匹配其他情況")
        }
        
        // 3. 測試失敗結果
        let testError = NSError(domain: "test", code: 1, userInfo: [NSLocalizedDescriptionKey: "測試錯誤"])
        let failedResult = MigrationResult.failed(testError)
        
        switch failedResult {
        case .failed(let error):
            XCTAssertEqual((error as NSError).domain, "test")
            XCTAssertEqual((error as NSError).code, 1)
        default:
            XCTFail("失敗結果不應該匹配其他情況")
        }
    }
    
    func testBackwardCompatibilityWithExistingProfiles() {
        // 測試與現有設定檔的向後相容性
        
        // 建立混合格式的設定檔
        createLegacyProfile(name: "old_profile", windows: createSampleWindows())
        createNewFormatProfile(name: "new_profile", windows: createSampleWindows(), spaceID: 1)
        createLegacySpaceProfile(name: "old_space", spaceID: 2, windows: createSampleWindows())
        
        // 驗證檔案都存在
        let oldFile = testLayoutsPath.appendingPathComponent("old_profile.json")
        let newFile = testLayoutsPath.appendingPathComponent("new_profile.json")
        let spaceFile = testLayoutsPath.appendingPathComponent("old_space_space2.json")
        
        XCTAssertTrue(FileManager.default.fileExists(atPath: oldFile.path), "舊格式檔案應該存在")
        XCTAssertTrue(FileManager.default.fileExists(atPath: newFile.path), "新格式檔案應該存在")
        XCTAssertTrue(FileManager.default.fileExists(atPath: spaceFile.path), "Space 檔案應該存在")
        
        // 測試檔案內容解析
        let oldData = try! Data(contentsOf: oldFile)
        let newData = try! Data(contentsOf: newFile)
        let spaceData = try! Data(contentsOf: spaceFile)
        
        // 舊格式應該無法解析為 Profile
        XCTAssertNil(try? JSONDecoder().decode(Profile.self, from: oldData), "舊格式不應該能解析為 Profile")
        
        // 新格式應該能解析為 Profile
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        XCTAssertNotNil(try? decoder.decode(Profile.self, from: newData), "新格式應該能解析為 Profile")
        
        // Space 格式應該無法解析為 Profile，但能解析為 SpaceLayoutData
        XCTAssertNil(try? decoder.decode(Profile.self, from: spaceData), "Space 格式不應該能解析為 Profile")
        XCTAssertNotNil(try? JSONDecoder().decode(SpaceLayoutData.self, from: spaceData), "Space 格式應該能解析為 SpaceLayoutData")
    }
    
    // MARK: - Helper Methods
    
    private func createSampleWindows() -> [WindowLayout] {
        return [
            WindowLayout(
                app: "Safari",
                bundleID: "com.apple.Safari",
                title: "Test Page",
                frame: WindowLayout.WindowFrame(x: 100, y: 100, w: 800, h: 600)
            ),
            WindowLayout(
                app: "Terminal",
                bundleID: "com.apple.Terminal",
                title: "Terminal",
                frame: WindowLayout.WindowFrame(x: 200, y: 200, w: 600, h: 400)
            )
        ]
    }
    
    private func createLegacyProfile(name: String, windows: [WindowLayout]) {
        let fileURL = testLayoutsPath.appendingPathComponent("\(name).json")
        let data = try! JSONEncoder().encode(windows)
        try! data.write(to: fileURL)
    }
    
    private func createLegacySpaceProfile(name: String, spaceID: Int, windows: [WindowLayout]) {
        let spaceData = SpaceLayoutData(
            windows: windows,
            spaceID: spaceID,
            screenUUID: nil,
            runningApps: nil,
            version: nil,
            type: nil,
            timestamp: nil
        )
        
        let fileURL = testLayoutsPath.appendingPathComponent("\(name)_space\(spaceID).json")
        let data = try! JSONEncoder().encode(spaceData)
        try! data.write(to: fileURL)
    }
    
    private func createNewFormatProfile(name: String, windows: [WindowLayout], spaceID: Int? = nil) {
        let profile = Profile(
            name: name,
            windows: windows,
            isSpaceSpecific: spaceID != nil,
            spaceID: spaceID
        )
        
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let data = try! encoder.encode(profile)
        
        let fileURL = testLayoutsPath.appendingPathComponent(profile.jsonFileName)
        try! data.write(to: fileURL)
    }
}