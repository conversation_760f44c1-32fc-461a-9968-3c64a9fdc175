import SwiftUI
@testable import Workspace

/// 手動測試 ProfileEditorView 的 Space 功能
struct ProfileEditorViewSpaceManualTest {
    
    static func runTests() {
        print("🧪 開始 ProfileEditorView Space 功能手動測試")
        
        testSpaceInformationDisplay()
        testSpaceMovementFunctionality()
        testSpaceStatusIndicators()
        testSpacePickerOptions()
        
        print("✅ ProfileEditorView Space 功能手動測試完成")
    }
    
    // MARK: - Test Methods
    
    static func testSpaceInformationDisplay() {
        print("📋 測試 Space 資訊顯示...")
        
        // 建立測試 Profile
        let testProfile = createTestProfile(spaceID: 1)
        
        // 驗證 Profile 有正確的 Space 資訊
        assert(testProfile.spaceID == 1, "Profile 應該有正確的 Space ID")
        assert(testProfile.isSpaceSpecific == true, "Profile 應該是 Space 特定的")
        
        print("✅ Space 資訊顯示測試通過")
    }
    
    static func testSpaceMovementFunctionality() {
        print("🔄 測試 Space 移動功能...")
        
        let profileManager = ProfileManager.shared
        let spaceProfileManager = SpaceProfileManager.shared
        
        // 建立測試 Profile
        let testProfile = createTestProfile(spaceID: 1)
        
        // 儲存到 Space 1
        profileManager.saveProfile(testProfile)
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: 1)
        
        // 驗證 Profile 在 Space 1 中
        let profilesInSpace1Before = spaceProfileManager.getProfilesForSpace(1)
        assert(profilesInSpace1Before.contains { $0.id == testProfile.id }, "Profile 應該在 Space 1 中")
        
        // 移動到 Space 2
        spaceProfileManager.moveProfileBetweenSpaces(testProfile, from: 1, to: 2)
        
        // 驗證移動結果
        let profilesInSpace1After = spaceProfileManager.getProfilesForSpace(1)
        let profilesInSpace2After = spaceProfileManager.getProfilesForSpace(2)
        
        assert(!profilesInSpace1After.contains { $0.id == testProfile.id }, "Profile 不應該在 Space 1 中")
        assert(profilesInSpace2After.contains { $0.id == testProfile.id }, "Profile 應該在 Space 2 中")
        
        // 清理
        profileManager.deleteProfile(testProfile)
        
        print("✅ Space 移動功能測試通過")
    }
    
    static func testSpaceStatusIndicators() {
        print("🚦 測試 Space 狀態指示器...")
        
        let spaceDetector = SpaceDetector.shared
        
        // 測試不同 Space 狀態
        let testProfiles = [
            createTestProfile(spaceID: 1),
            createTestProfile(spaceID: 2),
            createTestProfile(spaceID: nil)
        ]
        
        for profile in testProfiles {
            let statusText = getSpaceStatusText(for: profile, spaceDetector: spaceDetector)
            let statusColor = getSpaceStatusColor(for: profile, spaceDetector: spaceDetector)
            
            assert(!statusText.isEmpty, "狀態文字不應該為空")
            
            if profile.spaceID == nil {
                assert(statusText == "未指定", "沒有 Space ID 的 Profile 狀態應該是 '未指定'")
            }
        }
        
        print("✅ Space 狀態指示器測試通過")
    }
    
    static func testSpacePickerOptions() {
        print("📝 測試 Space 選擇器選項...")
        
        let spaceDetector = SpaceDetector.shared
        let availableSpaces = spaceDetector.getAvailableSpaces()
        
        assert(!availableSpaces.isEmpty, "應該有可用的 Spaces")
        
        for space in availableSpaces {
            assert(space.id >= 1 && space.id <= 3, "Space ID 應該在 1-3 範圍內")
            assert(!space.displayName.isEmpty, "Space 顯示名稱不應該為空")
            assert(spaceDetector.isSpaceAccessible(space.id), "可用的 Space 應該是可存取的")
        }
        
        print("✅ Space 選擇器選項測試通過")
    }
    
    // MARK: - Helper Methods
    
    static func createTestProfile(spaceID: Int?) -> Profile {
        let testWindows = [
            WindowLayout(
                id: UUID().uuidString,
                app: "Test App",
                title: "Test Window",
                bundleID: "com.test.app",
                frame: WindowFrame(x: 100, y: 100, w: 800, h: 600)
            )
        ]
        
        return Profile(
            name: "Test Profile \(UUID().uuidString.prefix(8))",
            windows: testWindows,
            spaceID: spaceID
        )
    }
    
    static func getSpaceStatusColor(for profile: Profile, spaceDetector: SpaceDetector) -> Color {
        guard let spaceID = profile.spaceID else {
            return .orange
        }
        
        if spaceDetector.isCurrentSpace(spaceID) {
            return .green
        } else if spaceDetector.isSpaceAccessible(spaceID) {
            return .blue
        } else {
            return .red
        }
    }
    
    static func getSpaceStatusText(for profile: Profile, spaceDetector: SpaceDetector) -> String {
        guard let spaceID = profile.spaceID else {
            return "未指定"
        }
        
        if spaceDetector.isCurrentSpace(spaceID) {
            return "當前 Space"
        } else if spaceDetector.isSpaceAccessible(spaceID) {
            return "可存取"
        } else {
            return "無法存取"
        }
    }
}

// MARK: - Test Runner

extension ProfileEditorViewSpaceManualTest {
    
    /// 執行所有測試的便利方法
    static func executeAllTests() {
        do {
            runTests()
            print("🎉 所有 ProfileEditorView Space 功能測試都通過了！")
        } catch {
            print("❌ 測試失敗: \(error)")
        }
    }
}