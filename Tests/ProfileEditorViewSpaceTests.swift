import XCTest
import SwiftUI
@testable import Workspace

/// 測試 ProfileEditorView 的 Space 功能
class ProfileEditorViewSpaceTests: XCTestCase {
    
    var profileManager: ProfileManager!
    var spaceProfileManager: SpaceProfileManager!
    var spaceDetector: SpaceDetector!
    var testProfile: Profile!
    
    override func setUp() {
        super.setUp()
        
        profileManager = ProfileManager.shared
        spaceProfileManager = SpaceProfileManager.shared
        spaceDetector = SpaceDetector.shared
        
        // 建立測試用的 Profile
        let testWindows = [
            WindowLayout(
                id: UUID().uuidString,
                app: "Test App",
                title: "Test Window",
                bundleID: "com.test.app",
                frame: WindowFrame(x: 100, y: 100, w: 800, h: 600)
            )
        ]
        
        testProfile = Profile(
            name: "Test Profile",
            windows: testWindows,
            spaceID: 1
        )
        
        // 清理測試環境
        profileManager.profiles.removeAll()
        spaceProfileManager.spaceProfileMapping.removeAll()
    }
    
    override func tearDown() {
        // 清理測試資料
        profileManager.profiles.removeAll()
        spaceProfileManager.spaceProfileMapping.removeAll()
        
        profileManager = nil
        spaceProfileManager = nil
        spaceDetector = nil
        testProfile = nil
        
        super.tearDown()
    }
    
    // MARK: - Space 資訊顯示測試
    
    func testProfileEditorDisplaysSpaceInformation() {
        // Given: 有一個指定 Space 的 Profile
        profileManager.saveProfile(testProfile)
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: 1)
        
        // When: 建立 ProfileEditorView
        let editorView = ProfileEditorView(profile: testProfile)
        
        // Then: 應該顯示正確的 Space 資訊
        XCTAssertEqual(testProfile.spaceID, 1)
        XCTAssertTrue(testProfile.isSpaceSpecific)
        
        // 驗證 Space 狀態方法
        let statusColor = getSpaceStatusColor(for: testProfile)
        let statusText = getSpaceStatusText(for: testProfile)
        
        XCTAssertNotNil(statusColor)
        XCTAssertFalse(statusText.isEmpty)
    }
    
    func testProfileEditorHandlesProfileWithoutSpace() {
        // Given: 有一個沒有指定 Space 的 Profile
        var profileWithoutSpace = testProfile!
        profileWithoutSpace.spaceID = nil
        profileWithoutSpace.isSpaceSpecific = false
        
        profileManager.saveProfile(profileWithoutSpace)
        
        // When: 建立 ProfileEditorView
        let editorView = ProfileEditorView(profile: profileWithoutSpace)
        
        // Then: 應該正確處理沒有 Space 的情況
        XCTAssertNil(profileWithoutSpace.spaceID)
        XCTAssertFalse(profileWithoutSpace.isSpaceSpecific)
        
        let statusColor = getSpaceStatusColor(for: profileWithoutSpace)
        let statusText = getSpaceStatusText(for: profileWithoutSpace)
        
        XCTAssertEqual(statusText, "未指定")
    }
    
    // MARK: - Space 移動功能測試
    
    func testMoveProfileBetweenSpaces() {
        // Given: 有一個在 Space 1 的 Profile
        profileManager.saveProfile(testProfile)
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: 1)
        
        // When: 將 Profile 移動到 Space 2
        spaceProfileManager.moveProfileBetweenSpaces(testProfile, from: 1, to: 2)
        
        // Then: Profile 應該在 Space 2 中
        let profilesInSpace1 = spaceProfileManager.getProfilesForSpace(1)
        let profilesInSpace2 = spaceProfileManager.getProfilesForSpace(2)
        
        XCTAssertFalse(profilesInSpace1.contains { $0.id == testProfile.id })
        XCTAssertTrue(profilesInSpace2.contains { $0.id == testProfile.id })
        
        // 驗證 Profile 的 spaceID 已更新
        if let updatedProfile = profileManager.profiles.first(where: { $0.id == testProfile.id }) {
            XCTAssertEqual(updatedProfile.spaceID, 2)
        } else {
            XCTFail("找不到更新後的 Profile")
        }
    }
    
    func testMoveProfileToInaccessibleSpace() {
        // Given: 有一個在 Space 1 的 Profile
        profileManager.saveProfile(testProfile)
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: 1)
        
        // When: 嘗試將 Profile 移動到無法存取的 Space (假設 Space 5 無法存取)
        spaceProfileManager.moveProfileBetweenSpaces(testProfile, from: 1, to: 5)
        
        // Then: Profile 應該仍在原來的 Space
        let profilesInSpace1 = spaceProfileManager.getProfilesForSpace(1)
        let profilesInSpace5 = spaceProfileManager.getProfilesForSpace(5)
        
        XCTAssertTrue(profilesInSpace1.contains { $0.id == testProfile.id })
        XCTAssertFalse(profilesInSpace5.contains { $0.id == testProfile.id })
    }
    
    // MARK: - Space 狀態測試
    
    func testSpaceStatusForCurrentSpace() {
        // Given: 有一個在當前 Space 的 Profile
        let currentSpaceID = spaceDetector.currentSpaceID ?? 1
        var profileInCurrentSpace = testProfile!
        profileInCurrentSpace.spaceID = currentSpaceID
        
        // When: 檢查 Space 狀態
        let statusText = getSpaceStatusText(for: profileInCurrentSpace)
        let statusColor = getSpaceStatusColor(for: profileInCurrentSpace)
        
        // Then: 應該顯示為當前 Space
        if spaceDetector.isCurrentSpace(currentSpaceID) {
            XCTAssertEqual(statusText, "當前 Space")
        }
    }
    
    func testSpaceStatusForAccessibleSpace() {
        // Given: 有一個在可存取但非當前 Space 的 Profile
        let accessibleSpaceID = 2 // 假設 Space 2 是可存取的
        var profileInAccessibleSpace = testProfile!
        profileInAccessibleSpace.spaceID = accessibleSpaceID
        
        // When: 檢查 Space 狀態
        let statusText = getSpaceStatusText(for: profileInAccessibleSpace)
        
        // Then: 應該顯示為可存取
        if spaceDetector.isSpaceAccessible(accessibleSpaceID) && !spaceDetector.isCurrentSpace(accessibleSpaceID) {
            XCTAssertEqual(statusText, "可存取")
        }
    }
    
    // MARK: - UI 整合測試
    
    func testProfileEditorSpacePickerOptions() {
        // Given: 有可用的 Spaces
        let availableSpaces = spaceDetector.getAvailableSpaces()
        
        // When: 檢查可用的 Space 選項
        // Then: 應該包含所有可存取的 Spaces
        XCTAssertFalse(availableSpaces.isEmpty)
        
        for space in availableSpaces {
            XCTAssertTrue(spaceDetector.isSpaceAccessible(space.id))
            XCTAssertFalse(space.displayName.isEmpty)
        }
    }
    
    func testProfileEditorSpaceMovementValidation() {
        // Given: 有一個在 Space 1 的 Profile
        profileManager.saveProfile(testProfile)
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: 1)
        
        // When: 檢查移動驗證邏輯
        let canMoveToSameSpace = (1 != testProfile.spaceID) // 不能移動到相同 Space
        let canMoveToValidSpace = spaceDetector.isSpaceAccessible(2) // 可以移動到有效 Space
        
        // Then: 驗證移動邏輯
        XCTAssertFalse(canMoveToSameSpace)
        XCTAssertTrue(canMoveToValidSpace)
    }
    
    // MARK: - Helper Methods
    
    private func getSpaceStatusColor(for profile: Profile) -> Color {
        guard let spaceID = profile.spaceID else {
            return .orange
        }
        
        if spaceDetector.isCurrentSpace(spaceID) {
            return .green
        } else if spaceDetector.isSpaceAccessible(spaceID) {
            return .blue
        } else {
            return .red
        }
    }
    
    private func getSpaceStatusText(for profile: Profile) -> String {
        guard let spaceID = profile.spaceID else {
            return "未指定"
        }
        
        if spaceDetector.isCurrentSpace(spaceID) {
            return "當前 Space"
        } else if spaceDetector.isSpaceAccessible(spaceID) {
            return "可存取"
        } else {
            return "無法存取"
        }
    }
}

// MARK: - Integration Tests

class ProfileEditorViewSpaceIntegrationTests: XCTestCase {
    
    func testProfileEditorSpaceIntegrationWorkflow() {
        // Given: 完整的 Space 環境
        let profileManager = ProfileManager.shared
        let spaceProfileManager = SpaceProfileManager.shared
        let spaceDetector = SpaceDetector.shared
        
        // 建立測試 Profile
        let testWindows = [
            WindowLayout(
                id: UUID().uuidString,
                app: "Integration Test App",
                title: "Integration Test Window",
                bundleID: "com.test.integration",
                frame: WindowFrame(x: 200, y: 200, w: 1000, h: 700)
            )
        ]
        
        let testProfile = Profile(
            name: "Integration Test Profile",
            windows: testWindows,
            spaceID: 1
        )
        
        // When: 執行完整的工作流程
        // 1. 儲存 Profile 到 Space
        profileManager.saveProfile(testProfile)
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: 1)
        
        // 2. 驗證 Profile 在正確的 Space 中
        let profilesInSpace1 = spaceProfileManager.getProfilesForSpace(1)
        XCTAssertTrue(profilesInSpace1.contains { $0.id == testProfile.id })
        
        // 3. 移動 Profile 到另一個 Space
        spaceProfileManager.moveProfileBetweenSpaces(testProfile, from: 1, to: 2)
        
        // 4. 驗證移動結果
        let profilesInSpace1After = spaceProfileManager.getProfilesForSpace(1)
        let profilesInSpace2After = spaceProfileManager.getProfilesForSpace(2)
        
        XCTAssertFalse(profilesInSpace1After.contains { $0.id == testProfile.id })
        XCTAssertTrue(profilesInSpace2After.contains { $0.id == testProfile.id })
        
        // 5. 清理測試資料
        profileManager.deleteProfile(testProfile)
        
        // Then: 所有操作都應該成功
        let finalProfilesInSpace2 = spaceProfileManager.getProfilesForSpace(2)
        XCTAssertFalse(finalProfilesInSpace2.contains { $0.id == testProfile.id })
    }
}