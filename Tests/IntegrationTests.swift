import Testing
import Foundation
import Swift<PERSON>
@testable @testable import Workspace

// MARK: - 集成測試套件
@Suite("Integration Tests")
struct IntegrationTests {
    
    // MARK: - 測試數據
    
    private func createCompleteTestProfile() -> Profile {
        let windows = [
            WindowLayout(
                app: "Safari",
                bundleID: "com.apple.Safari",
                title: "Apple - Official Website",
                frame: WindowLayout.WindowFrame(x: 100, y: 100, w: 1200, h: 800)
            ),
            WindowLayout(
                app: "Chrome",
                bundleID: "com.google.Chrome",
                title: "Google Search - Chrome",
                frame: WindowLayout.WindowFrame(x: 200, y: 200, w: 1100, h: 700)
            ),
            WindowLayout(
                app: "Firefox",
                bundleID: "org.mozilla.firefox",
                title: "Mozilla Firefox",
                frame: WindowLayout.WindowFrame(x: 300, y: 300, w: 1000, h: 600)
            ),
            WindowLayout(
                app: "Terminal",
                bundleID: "com.apple.Terminal",
                title: "bash - 80x24",
                frame: WindowLayout.WindowFrame(x: 50, y: 50, w: 800, h: 500)
            ),
            WindowLayout(
                app: "Xcode",
                bundleID: "com.apple.dt.Xcode",
                title: "Workspace.xcodeproj",
                frame: WindowLayout.WindowFrame(x: 400, y: 100, w: 1400, h: 900)
            )
        ]
        
        return Profile(name: "Complete Test Profile", windows: windows)
    }
    
    // MARK: - 完整工作流程集成測試
    
    @Test("集成測試 - 完整預覽工作流程")
    func testCompletePreviewWorkflow() {
        let profile = createCompleteTestProfile()
        let configManager = PreviewConfigurationManager.shared
        let originalConfig = configManager.currentConfiguration
        
        // 1. 初始化預覽狀態
        var selectedWindow: WindowLayout? = nil
        var selectedWindows: Set<UUID> = []
        var currentMode: PreviewMode = .scaled
        var showingDetail = false
        
        // 2. 測試配置優化
        configManager.optimizeConfiguration(for: profile.windows.count)
        let optimizedConfig = configManager.currentConfiguration
        
        #expect(optimizedConfig.mode == .scaled) // 5個視窗應該使用縮放模式
        #expect(optimizedConfig.showOverlapIndicators == true)
        
        // 3. 測試重疊檢測
        let bounds = CGRect(x: 0, y: 0, width: 1000, height: 800)
        let overlapResult = OverlapResolver.resolveOverlaps(
            windows: profile.windows,
            in: bounds
        )
        
        #expect(overlapResult.windowDisplayInfos.count == 5)
        #expect(overlapResult.totalOverlapCount > 0) // 應該有重疊
        
        // 4. 測試視窗選擇
        selectedWindow = profile.windows[0]
        selectedWindows = [selectedWindow!.id]
        showingDetail = true
        
        #expect(selectedWindow?.app == "Safari")
        #expect(selectedWindows.count == 1)
        #expect(showingDetail == true)
        
        // 5. 測試模式切換
        currentMode = .grid
        configManager.updatePreviewMode(currentMode)
        
        #expect(configManager.currentConfiguration.mode == .grid)
        
        // 6. 測試多選模式
        selectedWindows.insert(profile.windows[1].id)
        selectedWindows.insert(profile.windows[2].id)
        
        #expect(selectedWindows.count == 3)
        
        // 7. 測試虛擬化
        let virtualizationManager = VirtualizationManager()
        let config = VirtualizationManager.VirtualizationConfig(
            bufferSize: 10,
            itemHeight: 120,
            itemWidth: 160,
            enableVirtualization: true
        )
        virtualizationManager.updateConfig(config)
        
        let visibleRange = virtualizationManager.updateVisibleRange(
            viewportSize: CGSize(width: 800, height: 600),
            scrollOffset: .zero,
            totalItems: profile.windows.count,
            mode: .grid
        )
        
        #expect(visibleRange.startIndex >= 0)
        #expect(visibleRange.endIndex <= profile.windows.count)
        
        // 8. 測試快取系統
        let cacheManager = RenderCacheManager()
        let testData = Data("test cache data".utf8)
        cacheManager.setCachedData(testData, forKey: "test_key")
        let cachedData = cacheManager.getCachedData(forKey: "test_key")
        
        #expect(cachedData == testData)
        
        // 恢復原始配置
        configManager.updateConfiguration(originalConfig)
    }
    
    @Test("集成測試 - 預覽模式切換流程")
    func testPreviewModeSwitchingFlow() {
        let profile = createCompleteTestProfile()
        let configManager = PreviewConfigurationManager.shared
        let originalConfig = configManager.currentConfiguration
        
        var selectedWindow: WindowLayout? = profile.windows[0]
        var selectedWindows: Set<UUID> = [profile.windows[0].id]
        
        // 測試所有預覽模式的切換
        for mode in PreviewMode.allCases {
            configManager.updatePreviewMode(mode)
            
            #expect(configManager.currentConfiguration.mode == mode)
            
            // 測試每種模式下的基本功能
            switch mode {
            case .scaled:
                let bounds = CGRect(x: 0, y: 0, width: 1000, height: 800)
                let result = OverlapResolver.resolveOverlaps(
                    windows: profile.windows,
                    in: bounds
                )
                #expect(result.windowDisplayInfos.count == profile.windows.count)
                
            case .grid:
                let containerWidth: CGFloat = 800
                let itemWidth: CGFloat = 160
                let spacing: CGFloat = 8
                let padding: CGFloat = 16
                
                let availableWidth = containerWidth - padding * 2
                let columnsPerRow = max(1, Int(availableWidth / (itemWidth + spacing)))
                
                #expect(columnsPerRow > 0)
                
            case .list:
                let itemHeight: CGFloat = 80
                let totalHeight = CGFloat(profile.windows.count) * itemHeight
                
                #expect(totalHeight > 0)
                
            case .miniMap:
                let screens = ScreenBounds.detectScreenBounds(from: profile.windows)
                #expect(screens.count >= 1)
            }
            
            // 確保選擇狀態在模式切換後保持
            #expect(selectedWindow != nil)
            #expect(selectedWindows.count == 1)
        }
        
        // 恢復原始配置
        configManager.updateConfiguration(originalConfig)
    }
    
    @Test("集成測試 - 性能優化組件協作")
    func testPerformanceOptimizationComponentsIntegration() {
        let profile = createCompleteTestProfile()
        
        // 初始化所有性能優化組件
        let virtualizationManager = VirtualizationManager()
        let cacheManager = RenderCacheManager()
        let batchUpdateManager = BatchUpdateManager()
        let performanceMonitor = PerformanceMonitor()
        let memoryOptimizer = MemoryOptimizer()
        
        // 配置虛擬化管理器
        let virtualizationConfig = VirtualizationManager.VirtualizationConfig(
            bufferSize: 20,
            itemHeight: 120,
            itemWidth: 160,
            enableVirtualization: true
        )
        virtualizationManager.updateConfig(virtualizationConfig)
        
        // 設置性能監控器的依賴
        performanceMonitor.cacheManager = cacheManager
        performanceMonitor.virtualizationManager = virtualizationManager
        performanceMonitor.batchUpdateManager = batchUpdateManager
        
        // 開始性能監控
        performanceMonitor.startMonitoring()
        
        let startTime = Date()
        
        // 模擬複雜的操作流程
        for i in 0..<10 {
            // 1. 更新虛擬化範圍
            let scrollOffset = CGPoint(x: 0, y: CGFloat(i * 100))
            let _ = virtualizationManager.updateVisibleRange(
                viewportSize: CGSize(width: 800, height: 600),
                scrollOffset: scrollOffset,
                totalItems: profile.windows.count,
                mode: .grid
            )
            
            // 2. 獲取虛擬化項目
            let virtualizedItems = virtualizationManager.getVirtualizedItems(from: profile.windows)
            
            // 3. 快取渲染數據
            let cacheKey = "render_data_\(i)"
            let renderData = Data("render data for iteration \(i)".utf8)
            cacheManager.setCachedData(renderData, forKey: cacheKey)
            
            // 4. 批次更新選擇
            let selectedIds = [profile.windows[i % profile.windows.count].id]
            batchUpdateManager.updateWindowSelection(selectedIds) {
                // 模擬選擇更新操作
            }
            
            // 5. 記錄性能指標
            performanceMonitor.recordFrameTime()
            performanceMonitor.recordRenderingTime(0.016) // 16ms
            performanceMonitor.recordLayoutTime(0.008)    // 8ms
            
            #expect(virtualizedItems.count <= profile.windows.count)
        }
        
        let elapsedTime = Date().timeIntervalSince(startTime)
        
        // 檢查整體性能
        #expect(elapsedTime < 1.0) // 整個流程應該在1秒內完成
        
        // 檢查性能指標
        let metrics = performanceMonitor.currentMetrics
        #expect(metrics.averageFrameTime > 0)
        #expect(metrics.averageRenderTime > 0)
        #expect(metrics.averageLayoutTime > 0)
        
        // 應用記憶體優化
        memoryOptimizer.optimizeMemoryUsage()
        
        // 清理快取
        cacheManager.clearExpiredEntries()
        
        // 停止性能監控
        performanceMonitor.stopMonitoring()
    }
    
    @Test("集成測試 - 錯誤處理和恢復")
    func testErrorHandlingAndRecovery() {
        let configManager = PreviewConfigurationManager.shared
        let originalConfig = configManager.currentConfiguration
        
        // 1. 測試空配置文件處理
        let emptyProfile = Profile(name: "Empty", windows: [])
        var errorState: String? = nil
        
        if emptyProfile.windows.isEmpty {
            errorState = "沒有視窗可以預覽"
        }
        
        #expect(errorState == "沒有視窗可以預覽")
        
        // 2. 測試無效視窗數據處理
        let invalidWindows = [
            WindowLayout(
                app: "Invalid App",
                bundleID: "com.invalid",
                title: "Invalid Window",
                frame: WindowLayout.WindowFrame(
                    x: Double.infinity,
                    y: Double.nan,
                    w: -100,
                    h: 0
                )
            )
        ]
        
        let invalidProfile = Profile(name: "Invalid", windows: invalidWindows)
        
        // 應該能夠處理無效數據而不崩潰
        let bounds = CGRect(x: 0, y: 0, width: 1000, height: 800)
        let result = OverlapResolver.resolveOverlaps(
            windows: invalidProfile.windows,
            in: bounds
        )
        
        #expect(result.windowDisplayInfos.count == 1)
        
        // 3. 測試配置恢復
        var testConfig = PreviewConfiguration.default
        testConfig.maxWindowsPerView = -1 // 無效值
        
        configManager.updateConfiguration(testConfig)
        
        // 系統應該能夠處理無效配置
        #expect(configManager.currentConfiguration.maxWindowsPerView != -1)
        
        // 4. 測試選擇狀態清理
        let profile = createCompleteTestProfile()
        var selectedWindows: Set<UUID> = []
        
        // 添加一些有效和無效的選擇
        selectedWindows.insert(profile.windows[0].id) // 有效
        selectedWindows.insert(UUID()) // 無效
        selectedWindows.insert(profile.windows[1].id) // 有效
        
        // 清理無效選擇
        let validWindowIds = Set(profile.windows.map { $0.id })
        selectedWindows = selectedWindows.intersection(validWindowIds)
        
        #expect(selectedWindows.count == 2)
        #expect(selectedWindows.contains(profile.windows[0].id))
        #expect(selectedWindows.contains(profile.windows[1].id))
        
        // 恢復原始配置
        configManager.updateConfiguration(originalConfig)
    }
    
    @Test("集成測試 - 大量數據處理")
    func testLargeDataHandling() {
        // 創建大量視窗數據
        let largeWindowCount = 500
        let largeWindows = (0..<largeWindowCount).map { i in
            WindowLayout(
                app: "App\(i)",
                bundleID: "com.app\(i)",
                title: "Window \(i)",
                frame: WindowLayout.WindowFrame(
                    x: Double(i % 100 * 20),
                    y: Double(i / 100 * 30),
                    w: Double(200 + i % 100),
                    h: Double(150 + i % 80)
                )
            )
        }
        
        let largeProfile = Profile(name: "Large Profile", windows: largeWindows)
        
        let startTime = Date()
        
        // 1. 測試配置優化
        let configManager = PreviewConfigurationManager.shared
        let originalConfig = configManager.currentConfiguration
        
        configManager.optimizeConfiguration(for: largeProfile.windows.count)
        let optimizedConfig = configManager.currentConfiguration
        
        #expect(optimizedConfig.mode == .grid) // 大量視窗應該使用網格模式
        #expect(optimizedConfig.showLabels == false) // 應該關閉標籤以提高性能
        #expect(optimizedConfig.enableAnimations == false) // 應該關閉動畫
        
        // 2. 測試虛擬化處理
        let virtualizationManager = VirtualizationManager()
        let config = VirtualizationManager.VirtualizationConfig(
            bufferSize: 50,
            itemHeight: 120,
            itemWidth: 160,
            enableVirtualization: true
        )
        virtualizationManager.updateConfig(config)
        
        let visibleRange = virtualizationManager.updateVisibleRange(
            viewportSize: CGSize(width: 1200, height: 800),
            scrollOffset: .zero,
            totalItems: largeProfile.windows.count,
            mode: .grid
        )
        
        let virtualizedItems = virtualizationManager.getVirtualizedItems(from: largeProfile.windows)
        let visibleItems = virtualizedItems.filter { $0.isVisible }
        
        #expect(visibleItems.count < largeProfile.windows.count) // 虛擬化應該減少渲染項目
        #expect(visibleItems.count <= config.bufferSize * 2) // 不應該超過緩衝區大小
        
        // 3. 測試記憶體優化
        let memoryOptimizer = MemoryOptimizer()
        let initialMemory = getMemoryUsage()
        
        memoryOptimizer.optimizeMemoryUsage()
        
        let optimizedMemory = getMemoryUsage()
        
        // 記憶體使用應該得到控制
        #expect(optimizedMemory <= initialMemory * 1.1) // 允許10%的增長
        
        // 4. 測試性能監控
        let performanceMonitor = PerformanceMonitor()
        performanceMonitor.startMonitoring()
        
        // 模擬一些操作
        for i in 0..<10 {
            let scrollOffset = CGPoint(x: 0, y: CGFloat(i * 200))
            let _ = virtualizationManager.updateVisibleRange(
                viewportSize: CGSize(width: 1200, height: 800),
                scrollOffset: scrollOffset,
                totalItems: largeProfile.windows.count,
                mode: .grid
            )
            
            performanceMonitor.recordFrameTime()
        }
        
        let metrics = performanceMonitor.currentMetrics
        #expect(metrics.averageFrameTime < 0.033) // 應該保持30fps以上
        
        performanceMonitor.stopMonitoring()
        
        let elapsedTime = Date().timeIntervalSince(startTime)
        #expect(elapsedTime < 2.0) // 整個大量數據處理應該在2秒內完成
        
        // 恢復原始配置
        configManager.updateConfiguration(originalConfig)
    }
    
    @Test("集成測試 - 多線程安全性")
    func testMultiThreadSafety() async {
        let profile = createCompleteTestProfile()
        let configManager = PreviewConfigurationManager.shared
        let originalConfig = configManager.currentConfiguration
        
        // 並發執行多個操作
        await withTaskGroup(of: Void.self) { group in
            // 任務1：配置更新
            group.addTask {
                for i in 0..<10 {
                    let mode = PreviewMode.allCases[i % PreviewMode.allCases.count]
                    configManager.updatePreviewMode(mode)
                    try? await Task.sleep(nanoseconds: 10_000_000) // 10ms
                }
            }
            
            // 任務2：重疊檢測
            group.addTask {
                for _ in 0..<5 {
                    let bounds = CGRect(x: 0, y: 0, width: 1000, height: 800)
                    let _ = OverlapResolver.resolveOverlaps(
                        windows: profile.windows,
                        in: bounds
                    )
                    try? await Task.sleep(nanoseconds: 20_000_000) // 20ms
                }
            }
            
            // 任務3：虛擬化操作
            group.addTask {
                let virtualizationManager = VirtualizationManager()
                let config = VirtualizationManager.VirtualizationConfig(
                    bufferSize: 20,
                    itemHeight: 120,
                    itemWidth: 160,
                    enableVirtualization: true
                )
                virtualizationManager.updateConfig(config)
                
                for i in 0..<10 {
                    let scrollOffset = CGPoint(x: 0, y: CGFloat(i * 50))
                    let _ = virtualizationManager.updateVisibleRange(
                        viewportSize: CGSize(width: 800, height: 600),
                        scrollOffset: scrollOffset,
                        totalItems: profile.windows.count,
                        mode: .grid
                    )
                    try? await Task.sleep(nanoseconds: 15_000_000) // 15ms
                }
            }
            
            // 任務4：快取操作
            group.addTask {
                let cacheManager = RenderCacheManager()
                for i in 0..<20 {
                    let key = "concurrent_key_\(i)"
                    let data = Data("concurrent data \(i)".utf8)
                    cacheManager.setCachedData(data, forKey: key)
                    let _ = cacheManager.getCachedData(forKey: key)
                    try? await Task.sleep(nanoseconds: 5_000_000) // 5ms
                }
            }
        }
        
        // 檢查系統狀態是否仍然一致
        #expect(PreviewMode.allCases.contains(configManager.currentConfiguration.mode))
        
        // 恢復原始配置
        configManager.updateConfiguration(originalConfig)
    }
    
    // MARK: - 輔助方法
    
    private func getMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return info.resident_size
        } else {
            return 0
        }
    }
}