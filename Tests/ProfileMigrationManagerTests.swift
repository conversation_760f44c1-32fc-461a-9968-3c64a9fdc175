import XCTest
import Foundation
@testable import Workspace

class ProfileMigrationManagerTests: XCTestCase {
    
    var tempDirectory: URL!
    var layoutsPath: URL!
    var migrationManager: ProfileMigrationManager!
    
    override func setUp() {
        super.setUp()
        
        // 建立臨時測試目錄
        tempDirectory = FileManager.default.temporaryDirectory.appendingPathComponent(UUID().uuidString)
        layoutsPath = tempDirectory.appendingPathComponent(".hammerspoon/layouts")
        
        try! FileManager.default.createDirectory(at: layoutsPath, withIntermediateDirectories: true)
        
        // 注意：這裡我們無法直接測試 ProfileMigrationManager.shared，
        // 因為它使用固定的路徑。在實際實作中，可能需要讓 ProfileMigrationManager 支援自訂路徑
        // 或者使用依賴注入來進行測試
    }
    
    override func tearDown() {
        // 清理臨時目錄
        try? FileManager.default.removeItem(at: tempDirectory)
        super.tearDown()
    }
    
    // MARK: - Legacy Profile Detection Tests
    
    func testDetectLegacyProfiles() {
        // 建立舊格式設定檔
        createLegacyProfile(name: "test_profile", windows: createSampleWindows())
        createLegacySpaceProfile(name: "space_profile", spaceID: 1, windows: createSampleWindows())
        
        // 使用 Mock 管理器進行測試
        let mockManager = MockProfileMigrationManager(testLayoutsPath: layoutsPath)
        
        // 測試偵測功能
        XCTAssertTrue(mockManager.hasLegacyProfiles(), "應該偵測到舊版設定檔")
        
        // 建立新格式設定檔後，應該不再偵測到舊版
        createNewFormatProfile(name: "new_profile", windows: createSampleWindows())
        
        // 清理舊檔案
        try! FileManager.default.removeItem(at: layoutsPath.appendingPathComponent("test_profile.json"))
        try! FileManager.default.removeItem(at: layoutsPath.appendingPathComponent("space_profile_space1.json"))
        
        XCTAssertFalse(mockManager.hasLegacyProfiles(), "清理舊檔案後不應該偵測到舊版設定檔")
    }
    
    // MARK: - Migration Process Tests
    
    func testMigrationProcess() {
        // 建立測試資料
        createLegacyProfile(name: "legacy_profile", windows: createSampleWindows())
        createLegacySpaceProfile(name: "legacy_space", spaceID: 2, windows: createSampleWindows())
        
        // 使用 Mock 管理器測試遷移流程
        let mockManager = MockProfileMigrationManager(testLayoutsPath: layoutsPath)
        
        // 驗證有舊版設定檔
        XCTAssertTrue(mockManager.hasLegacyProfiles(), "應該有舊版設定檔需要遷移")
        
        // 執行遷移
        let result = mockManager.performTestMigration()
        
        // 驗證遷移結果
        switch result {
        case .success:
            XCTAssertTrue(true, "遷移應該成功")
        case .alreadyCompleted:
            XCTAssertTrue(true, "遷移已完成也是有效結果")
        case .failed(let error):
            XCTFail("遷移不應該失敗: \(error)")
        }
    }
    
    // MARK: - Backup and Restore Tests
    
    func testBackupCreation() {
        // 建立測試檔案
        createLegacyProfile(name: "backup_test", windows: createSampleWindows())
        
        // 驗證檔案存在
        let testFile = layoutsPath.appendingPathComponent("backup_test.json")
        XCTAssertTrue(FileManager.default.fileExists(atPath: testFile.path), "測試檔案應該存在")
        
        // 測試備份資訊結構
        let backupInfo = BackupInfo(
            createdAt: Date(),
            originalFileCount: 1,
            backupPath: layoutsPath.appendingPathComponent("backup").path
        )
        
        // 驗證備份資訊可以正確編碼和解碼
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let data = try! encoder.encode(backupInfo)
        
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        let decodedBackupInfo = try! decoder.decode(BackupInfo.self, from: data)
        
        XCTAssertEqual(decodedBackupInfo.originalFileCount, backupInfo.originalFileCount)
        XCTAssertEqual(decodedBackupInfo.backupPath, backupInfo.backupPath)
    }
    
    func testBackupRestore() {
        // 建立測試檔案
        createLegacyProfile(name: "restore_test", windows: createSampleWindows())
        
        // 模擬備份和還原流程
        let originalFile = layoutsPath.appendingPathComponent("restore_test.json")
        let backupDir = layoutsPath.appendingPathComponent("backup")
        let backupFile = backupDir.appendingPathComponent("restore_test.json")
        
        // 建立備份目錄
        try! FileManager.default.createDirectory(at: backupDir, withIntermediateDirectories: true)
        
        // 複製檔案到備份目錄
        try! FileManager.default.copyItem(at: originalFile, to: backupFile)
        
        // 驗證備份檔案存在
        XCTAssertTrue(FileManager.default.fileExists(atPath: backupFile.path), "備份檔案應該存在")
        
        // 刪除原始檔案（模擬遷移失敗）
        try! FileManager.default.removeItem(at: originalFile)
        XCTAssertFalse(FileManager.default.fileExists(atPath: originalFile.path), "原始檔案應該被刪除")
        
        // 從備份還原
        try! FileManager.default.copyItem(at: backupFile, to: originalFile)
        XCTAssertTrue(FileManager.default.fileExists(atPath: originalFile.path), "檔案應該從備份還原")
    }
    
    // MARK: - Data Validation Tests
    
    func testDataValidation() {
        // 建立有效的設定檔
        let validProfile = Profile(name: "valid_profile", windows: createSampleWindows(), isSpaceSpecific: true, spaceID: 1)
        createNewFormatProfile(name: "valid_profile", windows: createSampleWindows(), spaceID: 1)
        
        // 建立無效的設定檔（空名稱）
        let invalidProfile = Profile(name: "", windows: createSampleWindows())
        
        // 測試設定檔驗證
        XCTAssertFalse(validProfile.name.isEmpty, "有效設定檔名稱不應為空")
        XCTAssertTrue(validProfile.isSpaceSpecific, "Space 設定檔應該標記為 Space 專用")
        XCTAssertNotNil(validProfile.spaceID, "Space 設定檔應該有 Space ID")
        
        XCTAssertTrue(invalidProfile.name.isEmpty, "無效設定檔名稱應為空")
        
        // 測試 Space 資訊一致性
        let inconsistentProfile = Profile(name: "inconsistent", windows: createSampleWindows(), isSpaceSpecific: true, spaceID: nil)
        XCTAssertTrue(inconsistentProfile.isSpaceSpecific, "設定檔標記為 Space 專用")
        XCTAssertNil(inconsistentProfile.spaceID, "但缺少 Space ID（不一致）")
    }
    
    // MARK: - Backward Compatibility Tests
    
    func testBackwardCompatibility() {
        // 測試向後相容性
        let profile = Profile(name: "test", windows: createSampleWindows())
        
        // 測試新格式設定檔的載入
        XCTAssertEqual(profile.name, "test")
        XCTAssertFalse(profile.isSpaceSpecific)
        XCTAssertNil(profile.spaceID)
    }
    
    func testSpaceProfileCompatibility() {
        // 測試 Space 設定檔的相容性
        let spaceProfile = Profile(name: "space_test", windows: createSampleWindows(), isSpaceSpecific: true, spaceID: 1)
        
        XCTAssertEqual(spaceProfile.name, "space_test")
        XCTAssertTrue(spaceProfile.isSpaceSpecific)
        XCTAssertEqual(spaceProfile.spaceID, 1)
        XCTAssertTrue(spaceProfile.isSpaceAware)
    }
    
    // MARK: - File Format Tests
    
    func testLegacyFileFormatParsing() {
        // 測試舊格式檔案的解析
        let windows = createSampleWindows()
        let legacyData = try! JSONEncoder().encode(windows)
        
        // 驗證可以解析為 WindowLayout 陣列
        let parsedWindows = try! JSONDecoder().decode([WindowLayout].self, from: legacyData)
        XCTAssertEqual(parsedWindows.count, windows.count)
        XCTAssertEqual(parsedWindows.first?.app, windows.first?.app)
    }
    
    func testSpaceLegacyFileFormatParsing() {
        // 測試 Space 舊格式檔案的解析
        let windows = createSampleWindows()
        let spaceData = SpaceLayoutData(
            windows: windows,
            spaceID: 1,
            screenUUID: "test-uuid",
            runningApps: nil,
            version: "1.0",
            type: "space",
            timestamp: Date().timeIntervalSince1970
        )
        
        let legacyData = try! JSONEncoder().encode(spaceData)
        
        // 驗證可以解析為 SpaceLayoutData
        let parsedSpaceData = try! JSONDecoder().decode(SpaceLayoutData.self, from: legacyData)
        XCTAssertEqual(parsedSpaceData.windows.count, windows.count)
        XCTAssertEqual(parsedSpaceData.spaceID, 1)
    }
    
    func testNewFileFormatParsing() {
        // 測試新格式檔案的解析
        let profile = Profile(name: "new_format", windows: createSampleWindows(), isSpaceSpecific: true, spaceID: 2)
        
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let data = try! encoder.encode(profile)
        
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        let parsedProfile = try! decoder.decode(Profile.self, from: data)
        
        XCTAssertEqual(parsedProfile.name, profile.name)
        XCTAssertEqual(parsedProfile.isSpaceSpecific, profile.isSpaceSpecific)
        XCTAssertEqual(parsedProfile.spaceID, profile.spaceID)
        XCTAssertEqual(parsedProfile.windows.count, profile.windows.count)
    }
    
    // MARK: - Migration Status Tests
    
    func testMigrationStatusTracking() {
        // 測試遷移狀態追蹤
        let completedStatus = MigrationStatus.completed()
        XCTAssertTrue(completedStatus.isCompleted)
        XCTAssertNotNil(completedStatus.completedAt)
        XCTAssertEqual(completedStatus.version, "1.0")
        
        let pendingStatus = MigrationStatus.pending()
        XCTAssertFalse(pendingStatus.isCompleted)
        XCTAssertNil(pendingStatus.completedAt)
        
        let notNeededStatus = MigrationStatus.notNeeded()
        XCTAssertTrue(notNeededStatus.isCompleted)
        XCTAssertEqual(notNeededStatus.migratedProfileCount, 0)
    }
    
    // MARK: - Error Handling Tests
    
    func testMigrationErrorHandling() {
        // 測試遷移錯誤處理
        let validationError = MigrationError.validationFailed("Test validation error")
        XCTAssertNotNil(validationError.errorDescription)
        XCTAssertTrue(validationError.errorDescription!.contains("資料驗證失敗"))
        
        let backupError = MigrationError.backupNotFound
        XCTAssertNotNil(backupError.errorDescription)
        XCTAssertTrue(backupError.errorDescription!.contains("找不到備份檔案"))
    }
    
    // MARK: - Integration Tests
    
    func testFullMigrationWorkflow() {
        // 建立完整的測試場景
        createLegacyProfile(name: "workflow_test1", windows: createSampleWindows())
        createLegacySpaceProfile(name: "workflow_space", spaceID: 1, windows: createSampleWindows())
        createLegacyProfile(name: "workflow_test2", windows: createSampleWindows())
        
        let mockManager = MockProfileMigrationManager(testLayoutsPath: layoutsPath)
        
        // 驗證初始狀態
        XCTAssertTrue(mockManager.hasLegacyProfiles(), "應該有舊版設定檔")
        
        // 執行完整遷移工作流程
        let result = mockManager.performTestMigration()
        
        // 驗證結果
        switch result {
        case .success:
            // 驗證遷移後的檔案結構
            let files = try! FileManager.default.contentsOfDirectory(at: layoutsPath, includingPropertiesForKeys: nil)
            let jsonFiles = files.filter { $0.pathExtension == "json" }
            
            XCTAssertGreaterThan(jsonFiles.count, 0, "應該有遷移後的檔案")
            
        case .alreadyCompleted:
            XCTAssertTrue(true, "已完成的遷移也是有效結果")
            
        case .failed(let error):
            XCTFail("完整工作流程不應該失敗: \(error)")
        }
    }
    
    func testMigrationWithProfileManager() {
        // 測試 ProfileManager 的遷移整合
        // 由於 ProfileManager 使用單例，我們測試其遷移相關的邏輯
        
        // 建立測試設定檔
        createLegacyProfile(name: "profile_manager_test", windows: createSampleWindows())
        
        // 驗證 ProfileManager 會在初始化時檢查遷移
        // 這裡我們測試遷移狀態的結構
        let completedStatus = MigrationStatus.completed()
        let pendingStatus = MigrationStatus.pending()
        
        XCTAssertTrue(completedStatus.isCompleted)
        XCTAssertFalse(pendingStatus.isCompleted)
        XCTAssertNotNil(completedStatus.completedAt)
        XCTAssertNil(pendingStatus.completedAt)
    }
    
    func testMigrationWithSpaceProfileManager() {
        // 測試 SpaceProfileManager 的遷移整合
        
        // 建立 Space 設定檔
        createLegacySpaceProfile(name: "space_manager_test", spaceID: 1, windows: createSampleWindows())
        createLegacySpaceProfile(name: "space_manager_test2", spaceID: 2, windows: createSampleWindows())
        
        // 驗證 Space 映射檔案的結構
        let spaceMapping: [String: [String]] = [
            "1": ["space_manager_test"],
            "2": ["space_manager_test2"]
        ]
        
        // 測試映射檔案的編碼和解碼
        let encoder = JSONEncoder()
        let data = try! encoder.encode(spaceMapping)
        
        let decoder = JSONDecoder()
        let decodedMapping = try! decoder.decode([String: [String]].self, from: data)
        
        XCTAssertEqual(decodedMapping["1"]?.first, "space_manager_test")
        XCTAssertEqual(decodedMapping["2"]?.first, "space_manager_test2")
    }
    
    // MARK: - Helper Methods
    
    private func createSampleWindows() -> [WindowLayout] {
        return [
            WindowLayout(
                app: "Safari",
                bundleID: "com.apple.Safari",
                title: "Test Page",
                frame: WindowLayout.WindowFrame(x: 100, y: 100, w: 800, h: 600)
            ),
            WindowLayout(
                app: "Terminal",
                bundleID: "com.apple.Terminal",
                title: "Terminal",
                frame: WindowLayout.WindowFrame(x: 200, y: 200, w: 600, h: 400)
            )
        ]
    }
    
    private func createLegacyProfile(name: String, windows: [WindowLayout]) {
        let fileURL = layoutsPath.appendingPathComponent("\(name).json")
        let data = try! JSONEncoder().encode(windows)
        try! data.write(to: fileURL)
    }
    
    private func createLegacySpaceProfile(name: String, spaceID: Int, windows: [WindowLayout]) {
        let spaceData = SpaceLayoutData(
            windows: windows,
            spaceID: spaceID,
            screenUUID: nil,
            runningApps: nil,
            version: nil,
            type: nil,
            timestamp: nil
        )
        
        let fileURL = layoutsPath.appendingPathComponent("\(name)_space\(spaceID).json")
        let data = try! JSONEncoder().encode(spaceData)
        try! data.write(to: fileURL)
    }
    
    private func createNewFormatProfile(name: String, windows: [WindowLayout], spaceID: Int? = nil) {
        let profile = Profile(
            name: name,
            windows: windows,
            isSpaceSpecific: spaceID != nil,
            spaceID: spaceID
        )
        
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let data = try! encoder.encode(profile)
        
        let fileURL = layoutsPath.appendingPathComponent(profile.jsonFileName)
        try! data.write(to: fileURL)
    }
}

// MARK: - Mock Classes for Testing

/// 用於測試的模擬 ProfileMigrationManager
class MockProfileMigrationManager {
    private let testLayoutsPath: URL
    
    init(testLayoutsPath: URL) {
        self.testLayoutsPath = testLayoutsPath
    }
    
    func performTestMigration() -> MigrationResult {
        // 實作測試用的遷移邏輯
        do {
            let files = try FileManager.default.contentsOfDirectory(at: testLayoutsPath, includingPropertiesForKeys: nil)
            let jsonFiles = files.filter { $0.pathExtension == "json" }
            
            var migratedCount = 0
            
            for file in jsonFiles {
                if try migrateSingleTestProfile(from: file) {
                    migratedCount += 1
                }
            }
            
            // 建立測試用的映射檔案
            createTestSpaceMapping()
            
            return migratedCount > 0 ? .success : .alreadyCompleted
            
        } catch {
            return .failed(error)
        }
    }
    
    func hasLegacyProfiles() -> Bool {
        do {
            let files = try FileManager.default.contentsOfDirectory(at: testLayoutsPath, includingPropertiesForKeys: nil)
            let jsonFiles = files.filter { $0.pathExtension == "json" && !isSystemFile($0) }
            
            for file in jsonFiles {
                let data = try Data(contentsOf: file)
                // 如果無法解析為新格式，則認為是舊版
                if (try? JSONDecoder().decode(Profile.self, from: data)) == nil {
                    return true
                }
            }
            
            return false
        } catch {
            return false
        }
    }
    
    private func migrateSingleTestProfile(from fileURL: URL) throws -> Bool {
        let data = try Data(contentsOf: fileURL)
        let fileName = fileURL.deletingPathExtension().lastPathComponent
        
        // 檢查是否已經是新格式
        if let _ = try? JSONDecoder().decode(Profile.self, from: data) {
            return false
        }
        
        // 解析舊格式並轉換為新格式
        let profile = try parseTestLegacyProfile(data: data, fileName: fileName)
        
        // 儲存為新格式
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let newData = try encoder.encode(profile)
        
        try newData.write(to: fileURL)
        return true
    }
    
    private func parseTestLegacyProfile(data: Data, fileName: String) throws -> Profile {
        var name = fileName
        var spaceID: Int? = nil
        var isSpaceSpecific = false
        
        // 檢查是否為 Space-specific 檔案
        if fileName.contains("_space") {
            isSpaceSpecific = true
            let components = fileName.components(separatedBy: "_space")
            if components.count >= 2 {
                name = components[0]
                let spaceComponent = components[1]
                if !spaceComponent.isEmpty {
                    spaceID = Int(spaceComponent)
                }
            }
        }
        
        let windows: [WindowLayout]
        
        if isSpaceSpecific {
            // 嘗試解析 Space-specific 格式
            if let spaceData = try? JSONDecoder().decode(SpaceLayoutData.self, from: data) {
                windows = spaceData.windows
                if spaceID == nil {
                    spaceID = spaceData.spaceID
                }
            } else {
                windows = try JSONDecoder().decode([WindowLayout].self, from: data)
            }
        } else {
            windows = try JSONDecoder().decode([WindowLayout].self, from: data)
        }
        
        return Profile(name: name, windows: windows, isSpaceSpecific: isSpaceSpecific, spaceID: spaceID)
    }
    
    private func createTestSpaceMapping() {
        let mappingFileURL = testLayoutsPath.appendingPathComponent("space_profile_mapping.json")
        
        // 建立簡單的測試映射
        let mapping: [String: [String]] = [
            "1": [],
            "2": [],
            "3": []
        ]
        
        do {
            let data = try JSONEncoder().encode(mapping)
            try data.write(to: mappingFileURL)
        } catch {
            print("建立測試映射檔案失敗: \(error)")
        }
    }
    
    private func isSystemFile(_ url: URL) -> Bool {
        let fileName = url.lastPathComponent
        return fileName == "migration_status.json" || 
               fileName == "space_profile_mapping.json" ||
               fileName.hasPrefix("backup_")
    }
}

// MARK: - Test Data Structures

/// 測試用的備份資訊
private struct BackupInfo: Codable {
    let createdAt: Date
    let originalFileCount: Int
    let backupPath: String
}

/// 測試用的遷移結果驗證
struct MigrationTestResult {
    let originalProfileCount: Int
    let migratedProfileCount: Int
    let errorCount: Int
    let backupCreated: Bool
    let mappingFileCreated: Bool
    
    var isSuccessful: Bool {
        return errorCount == 0 && migratedProfileCount > 0 && mappingFileCreated
    }
}