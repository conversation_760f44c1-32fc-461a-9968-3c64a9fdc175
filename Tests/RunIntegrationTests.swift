import Foundation
@testable import Workspace

/**
 * 整合測試執行器
 *
 * 提供簡單的方式來執行所有 Space 整合測試
 */
class IntegrationTestRunner {
    
    /**
     * 執行所有整合測試
     */
    static func runAllTests() {
        print("🚀 開始執行 Space 整合測試...")
        print("=" * 60)
        
        var allTestsPassed = true
        
        // 執行 SpaceTabView 整合測試
        print("\n📋 執行 SpaceTabView 整合測試")
        let spaceTabViewTestsPassed = runSpaceTabViewTests()
        if !spaceTabViewTestsPassed {
            allTestsPassed = false
        }
        
        // 執行完整整合驗證
        print("\n📋 執行完整整合驗證")
        let completeValidationPassed = validateCompleteSpaceIntegration()
        if !completeValidationPassed {
            allTestsPassed = false
        }
        
        // 輸出最終結果
        print("\n" + "=" * 60)
        if allTestsPassed {
            print("🎉 所有整合測試都通過了！")
            print("✅ Space 整合功能已完全實現並驗證")
        } else {
            print("⚠️  部分整合測試失敗")
            print("❌ 需要檢查和修復相關問題")
        }
        print("=" * 60)
    }
    
    /**
     * 執行基本功能驗證
     */
    static func runBasicValidation() -> Bool {
        print("🔍 執行基本功能驗證...")
        
        var allPassed = true
        
        // 驗證核心組件
        let spaceDetector = SpaceDetector.shared
        let spaceProfileManager = SpaceProfileManager.shared
        let profileManager = ProfileManager.shared
        
        if spaceDetector == nil {
            print("❌ SpaceDetector 初始化失敗")
            allPassed = false
        } else {
            print("✅ SpaceDetector 初始化成功")
        }
        
        if spaceProfileManager == nil {
            print("❌ SpaceProfileManager 初始化失敗")
            allPassed = false
        } else {
            print("✅ SpaceProfileManager 初始化成功")
        }
        
        if profileManager == nil {
            print("❌ ProfileManager 初始化失敗")
            allPassed = false
        } else {
            print("✅ ProfileManager 初始化成功")
        }
        
        // 測試基本功能
        let testSpaces = [
            SpaceInfo(id: 1, name: "Space 1", isActive: true, displayName: "工作區 1"),
            SpaceInfo(id: 2, name: "Space 2", isActive: false, displayName: "工作區 2")
        ]
        
        spaceDetector.setTestSpaces(testSpaces)
        
        if spaceDetector.availableSpaces.count == 2 {
            print("✅ Space 偵測功能正常")
        } else {
            print("❌ Space 偵測功能異常")
            allPassed = false
        }
        
        return allPassed
    }
}

// MARK: - String Extension

extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}

// MARK: - Main Execution

/**
 * 主要執行函數
 */
func runIntegrationTests() {
    IntegrationTestRunner.runAllTests()
}

/**
 * 基本驗證函數
 */
func runBasicValidation() -> Bool {
    return IntegrationTestRunner.runBasicValidation()
}