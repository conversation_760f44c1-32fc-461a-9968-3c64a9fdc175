import Foundation
import SwiftUI
@testable import Workspace

// MARK: - SpaceTabViewIntegrationTests

/**
 * Space 標籤視圖整合測試
 *
 * 提供手動測試和驗證 SpaceTabView 和 SpaceTabButton 功能的工具。
 * 這些測試可以通過程式碼檢查和手動驗證來確保組件正常工作。
 *
 * ## 測試方法
 * - 創建測試實例並檢查屬性
 * - 驗證狀態變更的響應
 * - 測試邊界條件和錯誤處理
 *
 * @since 1.0.0
 * <AUTHOR> Integration Team
 */
class SpaceTabViewIntegrationTests {
    
    // MARK: - Test Data
    
    /// 測試用的 Space 資料
    private let testSpaces = [
        SpaceInfo(id: 1, name: "Space 1", isActive: true),
        SpaceInfo(id: 2, name: "Space 2", isActive: false),
        SpaceInfo(id: 3, name: "Space 3", isActive: false)
    ]
    
    // MARK: - Component Creation Tests
    
    /**
     * 測試 SpaceTabView 的創建
     */
    func testSpaceTabViewCreation() -> Bool {
        print("🧪 測試 SpaceTabView 創建...")
        
        // 創建綁定
        var selectedSpaceID: Int? = 1
        let binding = Binding<Int?>(
            get: { selectedSpaceID },
            set: { selectedSpaceID = $0 }
        )
        
        // 創建視圖
        let view = SpaceTabView(selectedSpaceID: binding)
        
        // 驗證視圖創建成功
        let success = view != nil
        print(success ? "✅ SpaceTabView 創建成功" : "❌ SpaceTabView 創建失敗")
        
        return success
    }
    
    /**
     * 測試 SpaceTabButton 的創建
     */
    func testSpaceTabButtonCreation() -> Bool {
        print("🧪 測試 SpaceTabButton 創建...")
        
        let space = testSpaces[0]
        var tapCalled = false
        
        // 創建按鈕
        let button = SpaceTabButton(
            space: space,
            isSelected: true,
            isCurrentSpace: true,
            onTap: { tapCalled = true }
        )
        
        // 驗證按鈕屬性
        let propertiesCorrect = (
            button.space.id == space.id &&
            button.space.name == space.name &&
            button.isSelected == true &&
            button.isCurrentSpace == true
        )
        
        // 測試點擊回調
        button.onTap()
        let callbackWorks = tapCalled
        
        let success = propertiesCorrect && callbackWorks
        print(success ? "✅ SpaceTabButton 創建和功能正常" : "❌ SpaceTabButton 創建或功能異常")
        
        return success
    }
    
    // MARK: - State Management Tests
    
    /**
     * 測試 Space 選擇狀態管理
     */
    func testSpaceSelectionState() -> Bool {
        print("🧪 測試 Space 選擇狀態管理...")
        
        var selectedSpaceID: Int? = 1
        let binding = Binding<Int?>(
            get: { selectedSpaceID },
            set: { selectedSpaceID = $0 }
        )
        
        // 測試初始狀態
        let initialState = selectedSpaceID == 1
        
        // 測試狀態變更
        selectedSpaceID = 2
        let stateChanged = selectedSpaceID == 2
        
        // 測試 nil 狀態
        selectedSpaceID = nil
        let nilStateHandled = selectedSpaceID == nil
        
        let success = initialState && stateChanged && nilStateHandled
        print(success ? "✅ Space 選擇狀態管理正常" : "❌ Space 選擇狀態管理異常")
        
        return success
    }
    
    /**
     * 測試不同按鈕狀態組合
     */
    func testButtonStateCombinations() -> Bool {
        print("🧪 測試按鈕狀態組合...")
        
        let space = testSpaces[0]
        let combinations = [
            (isSelected: true, isCurrentSpace: true, description: "選中+當前"),
            (isSelected: true, isCurrentSpace: false, description: "選中+非當前"),
            (isSelected: false, isCurrentSpace: true, description: "非選中+當前"),
            (isSelected: false, isCurrentSpace: false, description: "非選中+非當前")
        ]
        
        var allSuccess = true
        
        for combination in combinations {
            let button = SpaceTabButton(
                space: space,
                isSelected: combination.isSelected,
                isCurrentSpace: combination.isCurrentSpace,
                onTap: {}
            )
            
            let stateCorrect = (
                button.isSelected == combination.isSelected &&
                button.isCurrentSpace == combination.isCurrentSpace
            )
            
            if !stateCorrect {
                print("❌ 狀態組合失敗: \(combination.description)")
                allSuccess = false
            } else {
                print("✅ 狀態組合成功: \(combination.description)")
            }
        }
        
        return allSuccess
    }
    
    // MARK: - Accessibility Tests
    
    /**
     * 測試無障礙功能
     */
    func testAccessibilityFeatures() -> Bool {
        print("🧪 測試無障礙功能...")
        
        let space = SpaceInfo(id: 1, name: "Test Space", isActive: true)
        
        // 測試選中狀態的無障礙標籤
        let selectedButton = SpaceTabButton(
            space: space,
            isSelected: true,
            isCurrentSpace: true,
            onTap: {}
        )
        
        let selectedLabelCorrect = selectedButton.accessibilityLabel.contains("工作區 1") &&
                                  selectedButton.accessibilityLabel.contains("當前 Space")
        
        let selectedHintCorrect = selectedButton.accessibilityHint == "已選中此 Space"
        
        // 測試未選中狀態的無障礙標籤
        let unselectedButton = SpaceTabButton(
            space: space,
            isSelected: false,
            isCurrentSpace: false,
            onTap: {}
        )
        
        let unselectedHintCorrect = unselectedButton.accessibilityHint == "點擊選擇此 Space"
        
        let success = selectedLabelCorrect && selectedHintCorrect && unselectedHintCorrect
        print(success ? "✅ 無障礙功能正常" : "❌ 無障礙功能異常")
        
        return success
    }
    
    // MARK: - Edge Case Tests
    
    /**
     * 測試邊界條件
     */
    func testEdgeCases() -> Bool {
        print("🧪 測試邊界條件...")
        
        var allSuccess = true
        
        // 測試空 Space 名稱
        let emptyNameSpace = SpaceInfo(id: 1, name: "", isActive: false)
        let emptyNameButton = SpaceTabButton(
            space: emptyNameSpace,
            isSelected: false,
            isCurrentSpace: false,
            onTap: {}
        )
        
        let emptyNameHandled = emptyNameButton.space.name == ""
        if !emptyNameHandled {
            print("❌ 空名稱處理失敗")
            allSuccess = false
        } else {
            print("✅ 空名稱處理成功")
        }
        
        // 測試極大 Space ID
        let largeIDSpace = SpaceInfo(id: 999, name: "Large ID Space", isActive: false)
        let largeIDButton = SpaceTabButton(
            space: largeIDSpace,
            isSelected: false,
            isCurrentSpace: false,
            onTap: {}
        )
        
        let largeIDHandled = largeIDButton.space.id == 999
        if !largeIDHandled {
            print("❌ 大 ID 處理失敗")
            allSuccess = false
        } else {
            print("✅ 大 ID 處理成功")
        }
        
        return allSuccess
    }
    
    // MARK: - Performance Tests
    
    /**
     * 測試效能
     */
    func testPerformance() -> Bool {
        print("🧪 測試效能...")
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // 創建大量按鈕實例
        for i in 1...1000 {
            let space = SpaceInfo(id: i, name: "Space \(i)", isActive: i == 1)
            let button = SpaceTabButton(
                space: space,
                isSelected: i == 1,
                isCurrentSpace: i == 1,
                onTap: {}
            )
            
            // 確保按鈕被創建
            _ = button.space.id
        }
        
        let endTime = CFAbsoluteTimeGetCurrent()
        let duration = endTime - startTime
        
        let success = duration < 1.0 // 應該在 1 秒內完成
        print(success ? "✅ 效能測試通過 (\(String(format: "%.3f", duration))s)" : "❌ 效能測試失敗 (\(String(format: "%.3f", duration))s)")
        
        return success
    }
    
    // MARK: - Test Runner
    
    /**
     * 執行所有測試
     */
    func runAllTests() -> Bool {
        print("🚀 開始執行 SpaceTabView 整合測試...")
        print("=" * 50)
        
        let tests: [(String, () -> Bool)] = [
            ("SpaceTabView 創建", testSpaceTabViewCreation),
            ("SpaceTabButton 創建", testSpaceTabButtonCreation),
            ("Space 選擇狀態管理", testSpaceSelectionState),
            ("按鈕狀態組合", testButtonStateCombinations),
            ("無障礙功能", testAccessibilityFeatures),
            ("邊界條件", testEdgeCases),
            ("效能測試", testPerformance)
        ]
        
        var passedTests = 0
        let totalTests = tests.count
        
        for (testName, testFunction) in tests {
            print("\n📋 執行測試: \(testName)")
            let result = testFunction()
            if result {
                passedTests += 1
            }
        }
        
        print("\n" + "=" * 50)
        print("📊 測試結果: \(passedTests)/\(totalTests) 通過")
        
        let allPassed = passedTests == totalTests
        print(allPassed ? "🎉 所有測試通過！" : "⚠️  部分測試失敗")
        
        return allPassed
    }
}

// MARK: - String Extension

extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}

// MARK: - Test Execution

/**
 * 執行 SpaceTabView 整合測試的便利函數
 */
func runSpaceTabViewTests() -> Bool {
    let tester = SpaceTabViewIntegrationTests()
    return tester.runAllTests()
}