import XCTest
import Foundation
@testable import Workspace

class SpaceProfileManagerTests: XCTestCase {
    var spaceProfileManager: SpaceProfileManager!
    var testProfileManager: ProfileManager!
    var testSpaceDetector: SpaceDetector!
    var tempDirectory: URL!
    
    override func setUp() {
        super.setUp()
        
        // 建立臨時目錄用於測試
        tempDirectory = FileManager.default.temporaryDirectory
            .appendingPathComponent("SpaceProfileManagerTests")
            .appendingPathComponent(UUID().uuidString)
        
        try? FileManager.default.createDirectory(at: tempDirectory, withIntermediateDirectories: true)
        
        // 使用 shared instances 進行測試
        spaceProfileManager = SpaceProfileManager.shared
        testProfileManager = ProfileManager.shared
        testSpaceDetector = SpaceDetector.shared
    }
    
    override func tearDown() {
        // 清理臨時目錄
        try? FileManager.default.removeItem(at: tempDirectory)
        super.tearDown()
    }
    
    // MARK: - 基本功能測試
    
    func testInitialization() {
        XCTAssertNotNil(spaceProfileManager)
        XCTAssertNotNil(spaceProfileManager.spaceProfileMapping)
        XCTAssertTrue(spaceProfileManager.spaceProfileMapping.isEmpty || !spaceProfileManager.spaceProfileMapping.isEmpty)
    }
    
    func testGetProfilesForSpace() {
        // 測試獲取空 Space 的設定檔
        let emptySpaceProfiles = spaceProfileManager.getProfilesForSpace(999)
        XCTAssertTrue(emptySpaceProfiles.isEmpty)
        
        // 建立測試設定檔
        let testProfile = createTestProfile(name: "TestProfile", spaceID: 1)
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: 1)
        
        // 測試獲取有設定檔的 Space
        let spaceProfiles = spaceProfileManager.getProfilesForSpace(1)
        XCTAssertFalse(spaceProfiles.isEmpty)
        XCTAssertTrue(spaceProfiles.contains { $0.name == "TestProfile" })
    }
    
    func testGetProfilesForCurrentSpace() {
        // 測試獲取當前 Space 的設定檔
        let currentSpaceProfiles = spaceProfileManager.getProfilesForCurrentSpace()
        XCTAssertNotNil(currentSpaceProfiles)
        
        // 建立當前 Space 的測試設定檔
        let currentSpaceID = testSpaceDetector.currentSpaceID ?? testSpaceDetector.getDefaultSpaceID()
        let testProfile = createTestProfile(name: "CurrentSpaceProfile", spaceID: currentSpaceID)
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: currentSpaceID)
        
        let updatedProfiles = spaceProfileManager.getProfilesForCurrentSpace()
        XCTAssertTrue(updatedProfiles.contains { $0.name == "CurrentSpaceProfile" })
    }
    
    // MARK: - 設定檔儲存測試
    
    func testSaveProfileToSpace() {
        let testProfile = createTestProfile(name: "SaveTest", spaceID: nil)
        let targetSpaceID = 1
        
        // 儲存設定檔到指定 Space
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: targetSpaceID)
        
        // 驗證設定檔已儲存到正確的 Space
        let spaceProfiles = spaceProfileManager.getProfilesForSpace(targetSpaceID)
        XCTAssertTrue(spaceProfiles.contains { $0.name == "SaveTest" })
        
        // 驗證設定檔的 Space 資訊已更新
        let savedProfile = spaceProfiles.first { $0.name == "SaveTest" }
        XCTAssertNotNil(savedProfile)
        XCTAssertEqual(savedProfile?.spaceID, targetSpaceID)
        XCTAssertTrue(savedProfile?.isSpaceSpecific == true)
    }
    
    func testSaveProfileToInaccessibleSpace() {
        let testProfile = createTestProfile(name: "InaccessibleTest", spaceID: nil)
        let inaccessibleSpaceID = 999 // 假設這個 Space 不存在
        
        // 嘗試儲存到不可存取的 Space
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: inaccessibleSpaceID)
        
        // 驗證設定檔沒有被儲存到不可存取的 Space
        let spaceProfiles = spaceProfileManager.getProfilesForSpace(inaccessibleSpaceID)
        XCTAssertFalse(spaceProfiles.contains { $0.name == "InaccessibleTest" })
    }
    
    // MARK: - 設定檔移動測試
    
    func testMoveProfileBetweenSpaces() {
        let testProfile = createTestProfile(name: "MoveTest", spaceID: 1)
        let sourceSpaceID = 1
        let targetSpaceID = 2
        
        // 先儲存到來源 Space
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: sourceSpaceID)
        
        // 驗證設定檔在來源 Space
        var sourceProfiles = spaceProfileManager.getProfilesForSpace(sourceSpaceID)
        XCTAssertTrue(sourceProfiles.contains { $0.name == "MoveTest" })
        
        // 移動設定檔
        spaceProfileManager.moveProfileBetweenSpaces(testProfile, from: sourceSpaceID, to: targetSpaceID)
        
        // 驗證設定檔已從來源 Space 移除
        sourceProfiles = spaceProfileManager.getProfilesForSpace(sourceSpaceID)
        XCTAssertFalse(sourceProfiles.contains { $0.name == "MoveTest" })
        
        // 驗證設定檔已移動到目標 Space
        let targetProfiles = spaceProfileManager.getProfilesForSpace(targetSpaceID)
        XCTAssertTrue(targetProfiles.contains { $0.name == "MoveTest" })
        
        // 驗證設定檔的 Space ID 已更新
        let movedProfile = targetProfiles.first { $0.name == "MoveTest" }
        XCTAssertEqual(movedProfile?.spaceID, targetSpaceID)
    }
    
    func testMoveProfileToInaccessibleSpace() {
        let testProfile = createTestProfile(name: "MoveToInaccessibleTest", spaceID: 1)
        let sourceSpaceID = 1
        let inaccessibleSpaceID = 999
        
        // 先儲存到來源 Space
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: sourceSpaceID)
        
        // 嘗試移動到不可存取的 Space
        spaceProfileManager.moveProfileBetweenSpaces(testProfile, from: sourceSpaceID, to: inaccessibleSpaceID)
        
        // 驗證設定檔仍在來源 Space
        let sourceProfiles = spaceProfileManager.getProfilesForSpace(sourceSpaceID)
        XCTAssertTrue(sourceProfiles.contains { $0.name == "MoveToInaccessibleTest" })
        
        // 驗證設定檔沒有移動到不可存取的 Space
        let inaccessibleProfiles = spaceProfileManager.getProfilesForSpace(inaccessibleSpaceID)
        XCTAssertFalse(inaccessibleProfiles.contains { $0.name == "MoveToInaccessibleTest" })
    }
    
    // MARK: - 設定檔刪除測試
    
    func testDeleteProfileFromSpace() {
        let testProfile = createTestProfile(name: "DeleteTest", spaceID: 1)
        let spaceID = 1
        
        // 先儲存設定檔
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: spaceID)
        
        // 驗證設定檔存在
        var spaceProfiles = spaceProfileManager.getProfilesForSpace(spaceID)
        XCTAssertTrue(spaceProfiles.contains { $0.name == "DeleteTest" })
        
        // 刪除設定檔
        spaceProfileManager.deleteProfileFromSpace(testProfile, spaceID: spaceID)
        
        // 驗證設定檔已被刪除
        spaceProfiles = spaceProfileManager.getProfilesForSpace(spaceID)
        XCTAssertFalse(spaceProfiles.contains { $0.name == "DeleteTest" })
    }
    
    // MARK: - 映射管理測試
    
    func testReloadMappings() {
        // 建立一些測試設定檔
        let profile1 = createTestProfile(name: "ReloadTest1", spaceID: 1)
        let profile2 = createTestProfile(name: "ReloadTest2", spaceID: 2)
        
        spaceProfileManager.saveProfileToSpace(profile1, spaceID: 1)
        spaceProfileManager.saveProfileToSpace(profile2, spaceID: 2)
        
        // 重新載入映射
        spaceProfileManager.reloadMappings()
        
        // 驗證映射已正確重新載入
        let space1Profiles = spaceProfileManager.getProfilesForSpace(1)
        let space2Profiles = spaceProfileManager.getProfilesForSpace(2)
        
        XCTAssertTrue(space1Profiles.contains { $0.name == "ReloadTest1" })
        XCTAssertTrue(space2Profiles.contains { $0.name == "ReloadTest2" })
    }
    
    // MARK: - 工具方法測試
    
    func testSpacesWithProfiles() {
        // 建立不同 Space 的設定檔
        let profile1 = createTestProfile(name: "UtilTest1", spaceID: 1)
        let profile2 = createTestProfile(name: "UtilTest2", spaceID: 3)
        
        spaceProfileManager.saveProfileToSpace(profile1, spaceID: 1)
        spaceProfileManager.saveProfileToSpace(profile2, spaceID: 3)
        
        let spacesWithProfiles = spaceProfileManager.spacesWithProfiles
        XCTAssertTrue(spacesWithProfiles.contains(1))
        XCTAssertTrue(spacesWithProfiles.contains(3))
        XCTAssertFalse(spacesWithProfiles.contains(2)) // Space 2 沒有設定檔
    }
    
    func testGetProfileCount() {
        let spaceID = 1
        
        // 測試空 Space 的設定檔數量
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: spaceID), 0)
        
        // 添加設定檔
        let profile1 = createTestProfile(name: "CountTest1", spaceID: spaceID)
        let profile2 = createTestProfile(name: "CountTest2", spaceID: spaceID)
        
        spaceProfileManager.saveProfileToSpace(profile1, spaceID: spaceID)
        spaceProfileManager.saveProfileToSpace(profile2, spaceID: spaceID)
        
        // 測試有設定檔的 Space 數量
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: spaceID), 2)
    }
    
    func testHasProfiles() {
        let spaceID = 1
        
        // 測試空 Space
        XCTAssertFalse(spaceProfileManager.hasProfiles(in: spaceID))
        
        // 添加設定檔
        let testProfile = createTestProfile(name: "HasProfilesTest", spaceID: spaceID)
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: spaceID)
        
        // 測試有設定檔的 Space
        XCTAssertTrue(spaceProfileManager.hasProfiles(in: spaceID))
    }
    
    func testGetSpaceIDForProfile() {
        let testProfile = createTestProfile(name: "GetSpaceIDTest", spaceID: 2)
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: 2)
        
        let spaceID = spaceProfileManager.getSpaceID(for: testProfile)
        XCTAssertEqual(spaceID, 2)
    }
    
    func testIsProfileInSpace() {
        let testProfile = createTestProfile(name: "IsInSpaceTest", spaceID: 1)
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: 1)
        
        XCTAssertTrue(spaceProfileManager.isProfileInSpace(testProfile, spaceID: 1))
        XCTAssertFalse(spaceProfileManager.isProfileInSpace(testProfile, spaceID: 2))
    }
    
    // MARK: - 輔助方法
    
    private func createTestProfile(name: String, spaceID: Int?) -> Profile {
        let testWindows = [
            WindowLayout(
                app: "TestApp",
                bundleID: "com.test.app",
                title: "Test Window",
                frame: WindowLayout.WindowFrame(x: 100, y: 100, w: 800, h: 600)
            )
        ]
        
        return Profile(
            name: name,
            windows: testWindows,
            isSpaceSpecific: spaceID != nil,
            spaceID: spaceID
        )
    }
    
    private func waitForAsyncOperation(timeout: TimeInterval = 1.0) {
        let expectation = XCTestExpectation(description: "Async operation")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            expectation.fulfill()
        }
        wait(for: [expectation], timeout: timeout)
    }
}

// MARK: - 效能測試

extension SpaceProfileManagerTests {
    func testPerformanceOfLargeProfileSet() {
        measure {
            // 建立大量設定檔
            for i in 1...100 {
                let profile = createTestProfile(name: "PerfTest\(i)", spaceID: (i % 3) + 1)
                spaceProfileManager.saveProfileToSpace(profile, spaceID: (i % 3) + 1)
            }
            
            // 測試獲取設定檔的效能
            for spaceID in 1...3 {
                _ = spaceProfileManager.getProfilesForSpace(spaceID)
            }
        }
    }
    
    func testPerformanceOfMappingReload() {
        // 先建立一些設定檔
        for i in 1...50 {
            let profile = createTestProfile(name: "ReloadPerfTest\(i)", spaceID: (i % 3) + 1)
            spaceProfileManager.saveProfileToSpace(profile, spaceID: (i % 3) + 1)
        }
        
        measure {
            spaceProfileManager.reloadMappings()
        }
    }
}