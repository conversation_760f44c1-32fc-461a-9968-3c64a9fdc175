import XCTest
import SwiftUI
@testable import Workspace

/**
 * SpaceAwareProfileView UI 測試
 *
 * 測試 Space 感知的設定檔列表 UI 組件的各種功能和狀態
 *
 * ## 測試範圍
 * - Space 分組顯示功能
 * - 空 Space 狀態顯示
 * - 設定檔列表渲染
 * - Space 切換功能
 * - 使用者互動測試
 *
 * ## 需求對應
 * - 需求 1.1: 依 Space 分組顯示設定檔
 * - 需求 1.4: 空 Space 顯示空白狀態
 * - 需求 5.1: 清楚的 Space 視覺指示
 * - 需求 5.4: 顯示每個 Space 的設定檔數量
 *
 * @since 1.0.0
 * <AUTHOR> Integration Team
 */
class SpaceAwareProfileViewTests: XCTestCase {
    
    var spaceProfileManager: SpaceProfileManager!
    var spaceDetector: SpaceDetector!
    var profileManager: ProfileManager!
    
    override func setUp() {
        super.setUp()
        
        // 設定測試環境
        spaceProfileManager = SpaceProfileManager.shared
        spaceDetector = SpaceDetector.shared
        profileManager = ProfileManager.shared
        
        // 清理測試資料
        clearTestData()
    }
    
    override func tearDown() {
        // 清理測試資料
        clearTestData()
        super.tearDown()
    }
    
    // MARK: - 基本 UI 渲染測試
    
    /**
     * 測試 SpaceAwareProfileView 基本渲染
     *
     * 驗證視圖能夠正確初始化和顯示基本元素
     */
    func testSpaceAwareProfileViewBasicRendering() {
        // Given: 建立測試視圖
        let view = SpaceAwareProfileView()
        
        // When: 渲染視圖
        let hostingController = NSHostingController(rootView: view)
        let window = NSWindow(contentViewController: hostingController)
        window.makeKeyAndOrderFront(nil)
        
        // Then: 驗證視圖存在
        XCTAssertNotNil(hostingController.view)
        XCTAssertEqual(hostingController.view.frame.width, 320)
        
        // 清理
        window.close()
    }
    
    /**
     * 測試標題區域顯示
     *
     * 驗證標題區域包含正確的應用程式名稱和圖示
     */
    func testHeaderViewDisplay() {
        // Given: 建立測試視圖
        let view = SpaceAwareProfileView()
        let hostingController = NSHostingController(rootView: view)
        
        // When: 渲染視圖
        let window = NSWindow(contentViewController: hostingController)
        window.makeKeyAndOrderFront(nil)
        
        // Then: 驗證標題元素存在
        // 注意：在實際的 UI 測試中，這裡會檢查視圖層次結構
        XCTAssertNotNil(hostingController.view)
        
        // 清理
        window.close()
    }
    
    // MARK: - Space 標籤測試
    
    /**
     * 測試 Space 標籤選擇器顯示
     *
     * 驗證 Space 標籤能夠正確顯示可用的 Spaces
     */
    func testSpaceTabSectionDisplay() {
        // Given: 設定測試 Spaces
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        
        // When: 建立視圖
        let view = SpaceAwareProfileView()
        let hostingController = NSHostingController(rootView: view)
        let window = NSWindow(contentViewController: hostingController)
        window.makeKeyAndOrderFront(nil)
        
        // Then: 驗證 Space 標籤存在
        XCTAssertNotNil(hostingController.view)
        
        // 清理
        window.close()
    }
    
    /**
     * 測試 Space 統計資訊顯示
     *
     * 驗證每個 Space 的設定檔數量能夠正確顯示
     */
    func testSpaceStatisticsDisplay() {
        // Given: 建立測試資料
        let testSpaces = createTestSpaces()
        let testProfiles = createTestProfiles()
        
        spaceDetector.setTestSpaces(testSpaces)
        
        // 將設定檔分配到不同 Space
        spaceProfileManager.saveProfileToSpace(testProfiles[0], spaceID: 1)
        spaceProfileManager.saveProfileToSpace(testProfiles[1], spaceID: 1)
        spaceProfileManager.saveProfileToSpace(testProfiles[2], spaceID: 2)
        
        // When: 建立視圖
        let view = SpaceAwareProfileView()
        let hostingController = NSHostingController(rootView: view)
        let window = NSWindow(contentViewController: hostingController)
        window.makeKeyAndOrderFront(nil)
        
        // Then: 驗證統計資訊
        let space1Count = spaceProfileManager.getProfileCount(for: 1)
        let space2Count = spaceProfileManager.getProfileCount(for: 2)
        
        XCTAssertEqual(space1Count, 2, "Space 1 應該有 2 個設定檔")
        XCTAssertEqual(space2Count, 1, "Space 2 應該有 1 個設定檔")
        
        // 清理
        window.close()
    }
    
    // MARK: - 設定檔列表測試
    
    /**
     * 測試設定檔列表顯示
     *
     * 驗證設定檔能夠正確按 Space 分組顯示
     */
    func testProfileListDisplay() {
        // Given: 建立測試資料
        let testSpaces = createTestSpaces()
        let testProfiles = createTestProfiles()
        
        spaceDetector.setTestSpaces(testSpaces)
        spaceDetector.setCurrentSpace(1)
        
        // 將設定檔分配到 Space 1
        for profile in testProfiles {
            spaceProfileManager.saveProfileToSpace(profile, spaceID: 1)
        }
        
        // When: 建立視圖
        let view = SpaceAwareProfileView()
        let hostingController = NSHostingController(rootView: view)
        let window = NSWindow(contentViewController: hostingController)
        window.makeKeyAndOrderFront(nil)
        
        // Then: 驗證設定檔列表
        let space1Profiles = spaceProfileManager.getProfilesForSpace(1)
        XCTAssertEqual(space1Profiles.count, testProfiles.count)
        
        // 清理
        window.close()
    }
    
    /**
     * 測試空 Space 狀態顯示
     *
     * 驗證當 Space 沒有設定檔時顯示正確的空狀態
     */
    func testEmptySpaceStateDisplay() {
        // Given: 建立空的 Space
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        spaceDetector.setCurrentSpace(1)
        
        // 確保 Space 1 沒有設定檔
        XCTAssertTrue(spaceProfileManager.getProfilesForSpace(1).isEmpty)
        
        // When: 建立視圖
        let view = SpaceAwareProfileView()
        let hostingController = NSHostingController(rootView: view)
        let window = NSWindow(contentViewController: hostingController)
        window.makeKeyAndOrderFront(nil)
        
        // Then: 驗證空狀態顯示
        let hasProfiles = spaceProfileManager.hasProfiles(in: 1)
        XCTAssertFalse(hasProfiles, "Space 1 應該沒有設定檔")
        
        // 清理
        window.close()
    }
    
    // MARK: - Space 切換測試
    
    /**
     * 測試 Space 切換功能
     *
     * 驗證切換 Space 時設定檔列表能夠正確更新
     */
    func testSpaceSwitching() {
        // Given: 建立測試資料
        let testSpaces = createTestSpaces()
        let testProfiles = createTestProfiles()
        
        spaceDetector.setTestSpaces(testSpaces)
        
        // 將不同設定檔分配到不同 Space
        spaceProfileManager.saveProfileToSpace(testProfiles[0], spaceID: 1)
        spaceProfileManager.saveProfileToSpace(testProfiles[1], spaceID: 2)
        
        // When & Then: 測試 Space 切換
        let space1Profiles = spaceProfileManager.getProfilesForSpace(1)
        let space2Profiles = spaceProfileManager.getProfilesForSpace(2)
        
        XCTAssertEqual(space1Profiles.count, 1)
        XCTAssertEqual(space2Profiles.count, 1)
        XCTAssertEqual(space1Profiles[0].name, testProfiles[0].name)
        XCTAssertEqual(space2Profiles[0].name, testProfiles[1].name)
    }
    
    // MARK: - 設定檔操作測試
    
    /**
     * 測試設定檔還原功能
     *
     * 驗證點擊設定檔能夠觸發正確的還原操作
     */
    func testProfileRestoreOperation() {
        // Given: 建立測試設定檔
        let testProfile = createTestProfile(name: "測試設定檔", spaceID: 1)
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: 1)
        
        // When: 模擬還原操作
        let profiles = spaceProfileManager.getProfilesForSpace(1)
        XCTAssertFalse(profiles.isEmpty)
        
        let profileToRestore = profiles[0]
        
        // Then: 驗證設定檔屬性
        XCTAssertEqual(profileToRestore.name, "測試設定檔")
        XCTAssertEqual(profileToRestore.spaceID, 1)
        XCTAssertTrue(profileToRestore.isSpaceSpecific)
    }
    
    /**
     * 測試跨 Space 還原警告
     *
     * 驗證嘗試在不同 Space 還原設定檔時顯示警告
     */
    func testCrossSpaceRestoreWarning() {
        // Given: 建立跨 Space 的設定檔
        let testProfile = createTestProfile(name: "跨Space設定檔", spaceID: 1)
        spaceProfileManager.saveProfileToSpace(testProfile, spaceID: 1)
        
        // 切換到不同的 Space
        spaceDetector.setCurrentSpace(2)
        
        // When: 嘗試還原設定檔
        let profileSpaceID = spaceProfileManager.getSpaceID(for: testProfile)
        let currentSpaceID = spaceDetector.currentSpaceID
        
        // Then: 驗證跨 Space 情況
        XCTAssertEqual(profileSpaceID, 1)
        XCTAssertEqual(currentSpaceID, 2)
        XCTAssertNotEqual(profileSpaceID, currentSpaceID, "應該偵測到跨 Space 情況")
    }
    
    // MARK: - 儲存功能測試
    
    /**
     * 測試新設定檔儲存功能
     *
     * 驗證儲存按鈕能夠正確觸發設定檔儲存流程
     */
    func testNewProfileSaving() {
        // Given: 設定當前 Space
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        spaceDetector.setCurrentSpace(1)
        
        let initialCount = spaceProfileManager.getProfileCount(for: 1)
        
        // When: 模擬儲存新設定檔
        let newProfile = createTestProfile(name: "新設定檔", spaceID: 1)
        spaceProfileManager.saveProfileToSpace(newProfile, spaceID: 1)
        
        // Then: 驗證設定檔已儲存
        let finalCount = spaceProfileManager.getProfileCount(for: 1)
        XCTAssertEqual(finalCount, initialCount + 1, "應該增加一個設定檔")
        
        let savedProfiles = spaceProfileManager.getProfilesForSpace(1)
        XCTAssertTrue(savedProfiles.contains { $0.name == "新設定檔" })
    }
    
    // MARK: - 無障礙測試
    
    /**
     * 測試無障礙標籤和提示
     *
     * 驗證視圖元素包含適當的無障礙資訊
     */
    func testAccessibilityLabels() {
        // Given: 建立測試視圖
        let view = SpaceAwareProfileView()
        let hostingController = NSHostingController(rootView: view)
        
        // When: 渲染視圖
        let window = NSWindow(contentViewController: hostingController)
        window.makeKeyAndOrderFront(nil)
        
        // Then: 驗證無障礙設定
        // 注意：實際的無障礙測試需要更詳細的視圖檢查
        XCTAssertNotNil(hostingController.view)
        
        // 清理
        window.close()
    }
    
    // MARK: - 效能測試
    
    /**
     * 測試大量設定檔的渲染效能
     *
     * 驗證視圖在處理大量設定檔時的效能表現
     */
    func testLargeProfileListPerformance() {
        // Given: 建立大量測試設定檔
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        
        let largeProfileCount = 50
        for i in 0..<largeProfileCount {
            let profile = createTestProfile(name: "設定檔\(i)", spaceID: 1)
            spaceProfileManager.saveProfileToSpace(profile, spaceID: 1)
        }
        
        // When & Then: 測試渲染效能
        measure {
            let view = SpaceAwareProfileView()
            let hostingController = NSHostingController(rootView: view)
            let window = NSWindow(contentViewController: hostingController)
            window.makeKeyAndOrderFront(nil)
            window.close()
        }
    }
    
    // MARK: - 輔助方法
    
    /**
     * 清理測試資料
     */
    private func clearTestData() {
        // 清理 SpaceProfileManager 的測試資料
        spaceProfileManager.spaceProfileMapping.removeAll()
        
        // 重設 SpaceDetector 的測試狀態
        spaceDetector.resetTestState()
    }
    
    /**
     * 建立測試 Spaces
     */
    private func createTestSpaces() -> [SpaceInfo] {
        return [
            SpaceInfo(id: 1, name: "Space 1", isActive: true, displayName: "工作區 1"),
            SpaceInfo(id: 2, name: "Space 2", isActive: false, displayName: "工作區 2"),
            SpaceInfo(id: 3, name: "Space 3", isActive: false, displayName: "工作區 3")
        ]
    }
    
    /**
     * 建立測試設定檔
     */
    private func createTestProfiles() -> [Profile] {
        return [
            createTestProfile(name: "測試設定檔1", spaceID: nil),
            createTestProfile(name: "測試設定檔2", spaceID: nil),
            createTestProfile(name: "測試設定檔3", spaceID: nil)
        ]
    }
    
    /**
     * 建立單個測試設定檔
     */
    private func createTestProfile(name: String, spaceID: Int?) -> Profile {
        let testWindows = [
            WindowLayout(
                id: UUID().uuidString,
                app: "測試應用程式",
                title: "測試視窗",
                bundleID: "com.test.app",
                frame: WindowFrame(x: 100, y: 100, w: 800, h: 600)
            )
        ]
        
        return Profile(
            name: name,
            windows: testWindows,
            spaceID: spaceID,
            isSpaceSpecific: spaceID != nil,
            createdAt: Date(),
            modifiedAt: Date()
        )
    }
}

// MARK: - 測試擴展

/**
 * SpaceDetector 測試擴展
 *
 * 提供測試專用的方法來設定測試狀態
 */
extension SpaceDetector {
    
    /// 設定測試用的 Spaces
    func setTestSpaces(_ spaces: [SpaceInfo]) {
        DispatchQueue.main.async {
            self.availableSpaces = spaces
        }
    }
    
    /// 設定測試用的當前 Space
    func setCurrentSpace(_ spaceID: Int) {
        DispatchQueue.main.async {
            self.currentSpaceID = spaceID
        }
    }
    
    /// 重設測試狀態
    func resetTestState() {
        DispatchQueue.main.async {
            self.availableSpaces = []
            self.currentSpaceID = nil
        }
    }
}

/**
 * SpaceProfileManager 測試擴展
 *
 * 提供測試專用的方法來驗證內部狀態
 */
extension SpaceProfileManager {
    
    /// 獲取所有映射資料（測試用）
    var testMappingData: [Int: [Profile]] {
        return spaceProfileMapping
    }
    
    /// 清理所有映射資料（測試用）
    func clearAllMappings() {
        DispatchQueue.main.async {
            self.spaceProfileMapping.removeAll()
        }
    }
}