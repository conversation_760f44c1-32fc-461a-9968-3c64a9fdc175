
import XCTest
@testable @testable import Workspace

class GridLayoutCalculatorTests: XCTestCase {

    func testCalculateLayout_zeroWindows() {
        let config = GridLayoutCalculator.calculateLayout(for: 0, in: CGSize(width: 800, height: 600))
        XCTAssertEqual(config.columns.count, 1)
        XCTAssertEqual(config.itemSize, CGSize(width: 120, height: 90))
    }

    func testCalculateLayout_singleWindow() {
        let config = GridLayoutCalculator.calculateLayout(for: 1, in: CGSize(width: 800, height: 600))
        XCTAssertEqual(config.columns.count, 1)
        XCTAssertTrue(config.itemSize.width > 120)
    }

    func testCalculateLayout_multipleWindows_fitsWithoutScroll() {
        let config = GridLayoutCalculator.calculateLayout(for: 6, in: CGSize(width: 800, height: 600))
        XCTAssertTrue(config.columns.count > 1)
        let totalWidth = CGFloat(config.columns.count) * config.itemSize.width + CGFloat(config.columns.count - 1) * 16
        XCTAssertLessThanOrEqual(totalWidth, 800)
    }

    func testCalculateLayout_multipleWindows_requiresScroll() {
        let config = GridLayoutCalculator.calculateLayout(for: 20, in: CGSize(width: 400, height: 600))
        XCTAssertTrue(config.columns.count > 1)
    }
    
    func testCalculateLayout_respectsMinWidth() {
        let config = GridLayoutCalculator.calculateLayout(for: 10, in: CGSize(width: 200, height: 1000), minItemSize: CGSize(width: 100, height: 80))
        XCTAssertGreaterThanOrEqual(config.itemSize.width, 100)
    }
}
