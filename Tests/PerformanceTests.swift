import Testing
import Foundation
import SwiftUI
@testable @testable import Workspace

// MARK: - 性能測試套件
@Suite("Performance Tests")
struct PerformanceTests {
    
    // MARK: - 測試數據生成
    
    private func generateTestWindows(count: Int) -> [WindowLayout] {
        return (0..<count).map { i in
            WindowLayout(
                app: "TestApp\(i)",
                bundleID: "com.test.app\(i)",
                title: "Test Window \(i)",
                frame: WindowLayout.WindowFrame(
                    x: Double(i * 50 % 1920),
                    y: Double(i * 30 % 1080),
                    w: Double(200 + i % 400),
                    h: Double(150 + i % 300)
                )
            )
        }
    }
    
    private func generateOverlappingWindows(count: Int) -> [WindowLayout] {
        return (0..<count).map { i in
            WindowLayout(
                app: "OverlapApp\(i)",
                bundleID: "com.overlap.app\(i)",
                title: "Overlapping Window \(i)",
                frame: WindowLayout.WindowFrame(
                    x: Double(100 + i * 10),
                    y: Double(100 + i * 10),
                    w: 400,
                    h: 300
                )
            )
        }
    }
    
    // MARK: - 重疊檢測性能測試
    
    @Test("性能 - 重疊檢測 - 50個視窗")
    func testOverlapDetectionPerformance50Windows() {
        let windows = generateOverlappingWindows(count: 50)
        let bounds = CGRect(x: 0, y: 0, width: 1000, height: 800)
        
        let startTime = Date()
        let result = OverlapResolver.resolveOverlaps(windows: windows, in: bounds)
        let elapsedTime = Date().timeIntervalSince(startTime)
        
        #expect(result.windowDisplayInfos.count == 50)
        #expect(elapsedTime < 0.1) // 應該在100ms內完成
    }
    
    @Test("性能 - 重疊檢測 - 100個視窗")
    func testOverlapDetectionPerformance100Windows() {
        let windows = generateOverlappingWindows(count: 100)
        let bounds = CGRect(x: 0, y: 0, width: 1000, height: 800)
        
        let startTime = Date()
        let result = OverlapResolver.resolveOverlaps(windows: windows, in: bounds)
        let elapsedTime = Date().timeIntervalSince(startTime)
        
        #expect(result.windowDisplayInfos.count == 100)
        #expect(elapsedTime < 0.5) // 應該在500ms內完成
    }
    
    @Test("性能 - 重疊檢測 - 200個視窗")
    func testOverlapDetectionPerformance200Windows() {
        let windows = generateOverlappingWindows(count: 200)
        let bounds = CGRect(x: 0, y: 0, width: 1000, height: 800)
        
        let startTime = Date()
        let result = OverlapResolver.resolveOverlaps(windows: windows, in: bounds)
        let elapsedTime = Date().timeIntervalSince(startTime)
        
        #expect(result.windowDisplayInfos.count == 200)
        #expect(elapsedTime < 2.0) // 應該在2秒內完成
    }
    
    // MARK: - 螢幕檢測性能測試
    
    @Test("性能 - 螢幕邊界檢測 - 大量視窗")
    func testScreenDetectionPerformanceManyWindows() {
        let windows = generateTestWindows(count: 500)
        
        let startTime = Date()
        let screens = ScreenBounds.detectScreenBounds(from: windows)
        let elapsedTime = Date().timeIntervalSince(startTime)
        
        #expect(screens.count >= 1)
        #expect(elapsedTime < 0.1) // 應該在100ms內完成
    }
    
    @Test("性能 - 多螢幕檢測算法")
    func testMultiScreenDetectionPerformance() {
        // 創建模擬多螢幕配置的視窗
        var windows: [WindowLayout] = []
        
        // 第一個螢幕的視窗
        for i in 0..<50 {
            windows.append(WindowLayout(
                app: "Screen1App\(i)",
                bundleID: "com.screen1.app\(i)",
                title: "Screen 1 Window \(i)",
                frame: WindowLayout.WindowFrame(
                    x: Double(i * 20 % 1920),
                    y: Double(i * 15 % 1080),
                    w: 300,
                    h: 200
                )
            ))
        }
        
        // 第二個螢幕的視窗
        for i in 0..<50 {
            windows.append(WindowLayout(
                app: "Screen2App\(i)",
                bundleID: "com.screen2.app\(i)",
                title: "Screen 2 Window \(i)",
                frame: WindowLayout.WindowFrame(
                    x: Double(1920 + i * 20 % 1920),
                    y: Double(i * 15 % 1080),
                    w: 300,
                    h: 200
                )
            ))
        }
        
        let startTime = Date()
        let screens = ScreenBounds.detectMultipleScreens(from: windows)
        let elapsedTime = Date().timeIntervalSince(startTime)
        
        #expect(screens.count >= 1)
        #expect(elapsedTime < 0.2) // 應該在200ms內完成
    }
    
    // MARK: - 虛擬化性能測試
    
    @Test("性能 - 虛擬化管理器 - 大量項目")
    func testVirtualizationManagerPerformance() {
        let manager = VirtualizationManager()
        let windows = generateTestWindows(count: 1000)
        
        let config = VirtualizationManager.VirtualizationConfig(
            bufferSize: 20,
            itemHeight: 120,
            itemWidth: 160,
            enableVirtualization: true
        )
        manager.updateConfig(config)
        
        let startTime = Date()
        
        // 模擬多次視口更新
        for i in 0..<100 {
            let offset = CGPoint(x: 0, y: CGFloat(i * 10))
            let _ = manager.updateVisibleRange(
                viewportSize: CGSize(width: 800, height: 600),
                scrollOffset: offset,
                totalItems: windows.count,
                mode: .grid
            )
            
            let _ = manager.getVirtualizedItems(from: windows)
        }
        
        let elapsedTime = Date().timeIntervalSince(startTime)
        
        #expect(elapsedTime < 0.5) // 100次更新應該在500ms內完成
    }
    
    // MARK: - 快取性能測試
    
    @Test("性能 - 渲染快取管理器")
    func testRenderCacheManagerPerformance() {
        let cacheManager = RenderCacheManager()
        
        let startTime = Date()
        
        // 模擬大量快取操作
        for i in 0..<1000 {
            let key = "test_key_\(i)"
            let data = Data(repeating: UInt8(i % 256), count: 1024) // 1KB 數據
            
            cacheManager.setCachedData(data, forKey: key)
            let _ = cacheManager.getCachedData(forKey: key)
        }
        
        let elapsedTime = Date().timeIntervalSince(startTime)
        
        #expect(elapsedTime < 0.2) // 1000次快取操作應該在200ms內完成
    }
    
    @Test("性能 - 快取清理")
    func testCacheClearingPerformance() {
        let cacheManager = RenderCacheManager()
        
        // 填充大量快取數據
        for i in 0..<1000 {
            let key = "test_key_\(i)"
            let data = Data(repeating: UInt8(i % 256), count: 1024)
            cacheManager.setCachedData(data, forKey: key)
        }
        
        let startTime = Date()
        cacheManager.clearExpiredEntries()
        let elapsedTime = Date().timeIntervalSince(startTime)
        
        #expect(elapsedTime < 0.1) // 快取清理應該在100ms內完成
    }
    
    // MARK: - 批次更新性能測試
    
    @Test("性能 - 批次更新管理器")
    func testBatchUpdateManagerPerformance() {
        let batchManager = BatchUpdateManager()
        
        let startTime = Date()
        
        // 模擬大量批次更新
        for i in 0..<100 {
            let windowIds = (0..<10).map { _ in UUID() }
            
            batchManager.updateWindowSelection(windowIds) {
                // 模擬更新操作
                Thread.sleep(forTimeInterval: 0.001) // 1ms
            }
        }
        
        let elapsedTime = Date().timeIntervalSince(startTime)
        
        // 批次更新應該能夠優化性能
        #expect(elapsedTime < 0.5) // 應該在500ms內完成
    }
    
    // MARK: - 記憶體使用測試
    
    @Test("記憶體 - 大量視窗處理")
    func testMemoryUsageWithManyWindows() {
        let initialMemory = getMemoryUsage()
        
        // 創建大量視窗
        let windows = generateTestWindows(count: 1000)
        let bounds = CGRect(x: 0, y: 0, width: 1000, height: 800)
        
        // 執行重疊檢測
        let result = OverlapResolver.resolveOverlaps(windows: windows, in: bounds)
        
        let finalMemory = getMemoryUsage()
        let memoryIncrease = finalMemory - initialMemory
        
        #expect(result.windowDisplayInfos.count == 1000)
        #expect(memoryIncrease < 50 * 1024 * 1024) // 記憶體增長應該少於50MB
    }
    
    @Test("記憶體 - 虛擬化記憶體優化")
    func testVirtualizationMemoryOptimization() {
        let manager = VirtualizationManager()
        let memoryOptimizer = MemoryOptimizer()
        
        let initialMemory = getMemoryUsage()
        
        // 創建大量視窗但只虛擬化顯示部分
        let windows = generateTestWindows(count: 2000)
        
        let config = VirtualizationManager.VirtualizationConfig(
            bufferSize: 50,
            itemHeight: 120,
            itemWidth: 160,
            enableVirtualization: true
        )
        manager.updateConfig(config)
        
        let _ = manager.updateVisibleRange(
            viewportSize: CGSize(width: 800, height: 600),
            scrollOffset: .zero,
            totalItems: windows.count,
            mode: .grid
        )
        
        let virtualizedItems = manager.getVirtualizedItems(from: windows)
        
        // 應用記憶體優化
        memoryOptimizer.optimizeMemoryUsage()
        
        let finalMemory = getMemoryUsage()
        let memoryIncrease = finalMemory - initialMemory
        
        #expect(virtualizedItems.count < windows.count) // 虛擬化應該減少實際項目數
        #expect(memoryIncrease < 20 * 1024 * 1024) // 記憶體增長應該少於20MB
    }
    
    // MARK: - 並發性能測試
    
    @Test("並發 - 多線程重疊檢測")
    func testConcurrentOverlapDetection() async {
        let windows = generateOverlappingWindows(count: 100)
        let bounds = CGRect(x: 0, y: 0, width: 1000, height: 800)
        
        let startTime = Date()
        
        // 並發執行多個重疊檢測任務
        await withTaskGroup(of: OverlapResolver.OverlapResult.self) { group in
            for _ in 0..<10 {
                group.addTask {
                    return OverlapResolver.resolveOverlaps(windows: windows, in: bounds)
                }
            }
            
            var results: [OverlapResolver.OverlapResult] = []
            for await result in group {
                results.append(result)
            }
            
            #expect(results.count == 10)
            for result in results {
                #expect(result.windowDisplayInfos.count == 100)
            }
        }
        
        let elapsedTime = Date().timeIntervalSince(startTime)
        
        #expect(elapsedTime < 1.0) // 並發執行應該在1秒內完成
    }
    
    // MARK: - 輔助方法
    
    private func getMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return info.resident_size
        } else {
            return 0
        }
    }
    
    // MARK: - 基準測試
    
    @Test("基準 - 重疊檢測算法複雜度")
    func testOverlapDetectionComplexity() {
        let testSizes = [10, 20, 50, 100]
        var results: [(size: Int, time: TimeInterval)] = []
        
        for size in testSizes {
            let windows = generateOverlappingWindows(count: size)
            let bounds = CGRect(x: 0, y: 0, width: 1000, height: 800)
            
            let startTime = Date()
            let _ = OverlapResolver.resolveOverlaps(windows: windows, in: bounds)
            let elapsedTime = Date().timeIntervalSince(startTime)
            
            results.append((size: size, time: elapsedTime))
        }
        
        // 檢查算法複雜度是否合理（不應該是指數級增長）
        for i in 1..<results.count {
            let prevResult = results[i-1]
            let currentResult = results[i]
            
            let sizeRatio = Double(currentResult.size) / Double(prevResult.size)
            let timeRatio = currentResult.time / prevResult.time
            
            // 時間增長不應該超過大小增長的平方
            #expect(timeRatio < sizeRatio * sizeRatio * 2)
        }
    }
    
    @Test("基準 - 不同預覽模式性能比較")
    func testPreviewModePerformanceComparison() {
        let windows = generateTestWindows(count: 100)
        let bounds = CGRect(x: 0, y: 0, width: 1000, height: 800)
        
        var modePerformance: [PreviewMode: TimeInterval] = [:]
        
        for mode in PreviewMode.allCases {
            let startTime = Date()
            
            // 模擬不同模式的處理
            switch mode {
            case .scaled:
                let _ = OverlapResolver.resolveOverlaps(windows: windows, in: bounds)
            case .grid, .list:
                // 模擬網格/列表佈局計算
                let _ = windows.enumerated().map { index, window in
                    WindowDisplayInfo(
                        window: window,
                        displayFrame: CGRect(
                            x: CGFloat(index % 10) * 100,
                            y: CGFloat(index / 10) * 100,
                            width: 80,
                            height: 60
                        )
                    )
                }
            case .miniMap:
                let _ = ScreenBounds.detectScreenBounds(from: windows)
            }
            
            let elapsedTime = Date().timeIntervalSince(startTime)
            modePerformance[mode] = elapsedTime
        }
        
        // 所有模式都應該在合理時間內完成
        for (mode, time) in modePerformance {
            #expect(time < 0.5, "Mode \(mode) took too long: \(time)s")
        }
    }
}