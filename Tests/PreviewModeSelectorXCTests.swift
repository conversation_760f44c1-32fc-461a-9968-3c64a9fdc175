import Testing
import Swift<PERSON>
@testable import Workspace

@Suite("PreviewModeSelector Tests")
struct PreviewModeSelectorTests {
    
    @Test("Mode Selection")
    func testModeSelection() {
        var selectedMode: PreviewMode = .scaled
        var configuration = PreviewConfiguration.default
        
        #expect(selectedMode == .scaled)
        #expect(configuration.mode == .scaled)
        
        selectedMode = .grid
        configuration.mode = .grid
        
        #expect(selectedMode == .grid)
        #expect(configuration.mode == .grid)
    }
    
    @Test("Mode Persistence")
    func testModePersistence() {
        let testMode = PreviewMode.miniMap
        
        UserDefaults.standard.set(testMode.rawValue, forKey: "selectedPreviewMode")
        
        let savedModeRawValue = UserDefaults.standard.string(forKey: "selectedPreviewMode")
        let savedMode = PreviewMode(rawValue: savedModeRawValue ?? "")
        
        #expect(savedMode == testMode, "Mode persistence failed")
    }
    
    @Test("Preview Mode Properties")
    func testPreviewModeProperties() {
        let testCases: [(PreviewMode, String, String)] = [
            (.scaled, "縮放預覽", "rectangle.3.group"),
            (.grid, "網格預覽", "grid"),
            (.list, "列表預覽", "list.bullet"),
            (.miniMap, "小地圖", "map")
        ]
        
        for (mode, expectedName, expectedIcon) in testCases {
            #expect(mode.displayName == expectedName, "\(mode) display name is incorrect")
            #expect(mode.icon == expectedIcon, "\(mode) icon is incorrect")
            #expect(!mode.description.isEmpty, "\(mode) description should not be empty")
        }
    }
    
    @Test("Accessibility Requirements")
    func testAccessibilityRequirements() {
        for mode in PreviewMode.allCases {
            #expect(!mode.displayName.isEmpty, "\(mode) should have an accessibility label")
            #expect(!mode.description.isEmpty, "\(mode) should have an accessibility description")
        }
    }
    
    @Test("All Modes Switching")
    func testAllModesSwitching() {
        var selectedMode: PreviewMode = .scaled
        var configuration = PreviewConfiguration.default
        
        for mode in PreviewMode.allCases {
            selectedMode = mode
            configuration.mode = mode
            
            #expect(selectedMode == mode, "Failed to switch mode to \(mode.displayName)")
            #expect(configuration.mode == mode, "Configuration mode was not updated correctly")
        }
    }
    
    @Test("Persistence Restoration")
    func testPersistenceRestoration() {
        let savedMode = PreviewMode.list
        UserDefaults.standard.set(savedMode.rawValue, forKey: "selectedPreviewMode")
        
        let restoredModeRawValue = UserDefaults.standard.string(forKey: "selectedPreviewMode")
        let restoredMode = PreviewMode(rawValue: restoredModeRawValue ?? PreviewMode.scaled.rawValue)
        
        #expect(restoredMode == savedMode, "Persistence restoration failed")
    }
    
    @Test("Invalid Persistence Data")
    func testInvalidPersistenceData() {
        UserDefaults.standard.set("invalid_mode", forKey: "selectedPreviewMode")
        
        let restoredModeRawValue = UserDefaults.standard.string(forKey: "selectedPreviewMode")
        let restoredMode = PreviewMode(rawValue: restoredModeRawValue ?? PreviewMode.scaled.rawValue)
        
        #expect(restoredMode == .scaled, "Invalid data handling failed")
    }
    
    @Test("Preview Mode Identifiable")
    func testPreviewModeIdentifiable() {
        for mode in PreviewMode.allCases {
            #expect(mode.id == mode.rawValue, "\(mode) ID should be equal to its rawValue")
        }
    }
    
    @Test("Preview Mode CaseIterable")
    func testPreviewModeCaseIterable() {
        let allCases = PreviewMode.allCases
        
        #expect(allCases.contains(.scaled), "Should contain .scaled mode")
        #expect(allCases.contains(.grid), "Should contain .grid mode")
        #expect(allCases.contains(.list), "Should contain .list mode")
        #expect(allCases.contains(.miniMap), "Should contain .miniMap mode")
        
        #expect(allCases.count == 4, "There should be 4 preview modes")
    }
}