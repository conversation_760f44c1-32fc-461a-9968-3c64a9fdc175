import XCTest
import SwiftUI
@testable import Workspace

/**
 * Space 整合工作流程測試
 *
 * 測試完整的 Space 分組工作流程，包括所有組件的整合和狀態同步
 *
 * ## 測試範圍
 * - 完整的 Space 分組工作流程
 * - 組件間的狀態同步
 * - UI 更新和響應
 * - 錯誤處理和邊界情況
 * - 效能和穩定性
 *
 * @since 1.0.0
 * <AUTHOR> Integration Team
 */
class SpaceIntegrationWorkflowTests: XCTestCase {
    
    // MARK: - Properties
    
    var spaceProfileManager: SpaceProfileManager!
    var spaceDetector: SpaceDetector!
    var profileManager: ProfileManager!
    var hammerspoonManager: HammerspoonManager!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        
        // 初始化管理器
        spaceProfileManager = SpaceProfileManager.shared
        spaceDetector = SpaceDetector.shared
        profileManager = ProfileManager.shared
        hammerspoonManager = HammerspoonManager.shared
        
        // 清理測試環境
        clearTestEnvironment()
        
        // 設定測試環境
        setupTestEnvironment()
    }
    
    override func tearDown() {
        // 清理測試環境
        clearTestEnvironment()
        super.tearDown()
    }
    
    // MARK: - 完整工作流程測試
    
    /**
     * 測試完整的 Space 分組工作流程
     *
     * 驗證從初始化到設定檔管理的完整流程
     */
    func testCompleteSpaceGroupingWorkflow() {
        print("🧪 測試完整的 Space 分組工作流程")
        
        // Phase 1: 初始化和 Space 偵測
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        spaceDetector.setCurrentSpace(1)
        
        // 驗證初始化
        XCTAssertEqual(spaceDetector.availableSpaces.count, 3, "應該有 3 個可用 Spaces")
        XCTAssertEqual(spaceDetector.currentSpaceID, 1, "當前 Space 應該是 1")
        
        // Phase 2: 建立和儲存設定檔
        let profiles = [
            createTestProfile(name: "工作設定檔", spaceID: 1),
            createTestProfile(name: "娛樂設定檔", spaceID: 2),
            createTestProfile(name: "開發設定檔", spaceID: 1),
            createTestProfile(name: "設計設定檔", spaceID: 3)
        ]
        
        for profile in profiles {
            if let spaceID = profile.spaceID {
                spaceProfileManager.saveProfileToSpace(profile, spaceID: spaceID)
            }
        }
        
        // 驗證設定檔儲存
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: 1), 2, "Space 1 應該有 2 個設定檔")
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: 2), 1, "Space 2 應該有 1 個設定檔")
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: 3), 1, "Space 3 應該有 1 個設定檔")
        
        // Phase 3: Space 切換和狀態同步
        spaceDetector.setCurrentSpace(2)
        
        let currentSpaceProfiles = spaceProfileManager.getProfilesForCurrentSpace()
        XCTAssertEqual(currentSpaceProfiles.count, 1, "當前 Space 應該有 1 個設定檔")
        XCTAssertEqual(currentSpaceProfiles[0].name, "娛樂設定檔", "應該顯示正確的設定檔")
        
        // Phase 4: 設定檔操作
        let profileToMove = profiles[0] // 工作設定檔
        spaceProfileManager.moveProfileBetweenSpaces(profileToMove, from: 1, to: 3)
        
        // 驗證移動結果
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: 1), 1, "Space 1 應該剩下 1 個設定檔")
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: 3), 2, "Space 3 應該有 2 個設定檔")
        
        // Phase 5: 資料持久化
        spaceProfileManager.reloadMappings()
        
        // 驗證資料持久化
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: 1), 1, "重新載入後 Space 1 應該有 1 個設定檔")
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: 2), 1, "重新載入後 Space 2 應該有 1 個設定檔")
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: 3), 2, "重新載入後 Space 3 應該有 2 個設定檔")
        
        print("✅ 完整工作流程測試通過")
    }
    
    /**
     * 測試 Space 切換時的狀態同步
     *
     * 驗證所有組件在 Space 切換時的狀態同步
     */
    func testSpaceSwitchingStateSynchronization() {
        print("🧪 測試 Space 切換狀態同步")
        
        // 設定測試環境
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        
        // 建立測試設定檔
        let profile1 = createTestProfile(name: "Space1設定檔", spaceID: 1)
        let profile2 = createTestProfile(name: "Space2設定檔", spaceID: 2)
        let profile3 = createTestProfile(name: "Space3設定檔", spaceID: 3)
        
        spaceProfileManager.saveProfileToSpace(profile1, spaceID: 1)
        spaceProfileManager.saveProfileToSpace(profile2, spaceID: 2)
        spaceProfileManager.saveProfileToSpace(profile3, spaceID: 3)
        
        // 測試 Space 切換序列
        let spaceSequence = [1, 2, 3, 1, 2]
        
        for targetSpace in spaceSequence {
            // 切換到目標 Space
            spaceDetector.setCurrentSpace(targetSpace)
            
            // 驗證當前 Space 狀態
            XCTAssertEqual(spaceDetector.currentSpaceID, targetSpace, "當前 Space ID 應該正確")
            
            // 驗證當前 Space 的設定檔
            let currentProfiles = spaceProfileManager.getProfilesForCurrentSpace()
            XCTAssertEqual(currentProfiles.count, 1, "當前 Space 應該有 1 個設定檔")
            XCTAssertEqual(currentProfiles[0].spaceID, targetSpace, "設定檔應該屬於當前 Space")
            
            // 驗證 Space 資訊
            let currentSpaceInfo = spaceDetector.availableSpaces.first { $0.id == targetSpace }
            XCTAssertNotNil(currentSpaceInfo, "應該能找到當前 Space 資訊")
            XCTAssertEqual(currentSpaceInfo?.id, targetSpace, "Space 資訊應該正確")
        }
        
        print("✅ Space 切換狀態同步測試通過")
    }
    
    /**
     * 測試 UI 組件整合
     *
     * 驗證 UI 組件間的正確整合和互動
     */
    func testUIComponentIntegration() {
        print("🧪 測試 UI 組件整合")
        
        // 設定測試環境
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        spaceDetector.setCurrentSpace(1)
        
        // 測試 SpaceTabView 創建和狀態
        var selectedSpaceID: Int? = 1
        let spaceTabBinding = Binding<Int?>(
            get: { selectedSpaceID },
            set: { selectedSpaceID = $0 }
        )
        
        let spaceTabView = SpaceTabView(selectedSpaceID: spaceTabBinding)
        XCTAssertNotNil(spaceTabView, "SpaceTabView 應該能正確創建")
        
        // 測試 SpaceAwareProfileView 創建
        let spaceAwareView = SpaceAwareProfileView()
        XCTAssertNotNil(spaceAwareView, "SpaceAwareProfileView 應該能正確創建")
        
        // 測試狀態綁定
        selectedSpaceID = 2
        XCTAssertEqual(selectedSpaceID, 2, "狀態綁定應該正常工作")
        
        selectedSpaceID = 3
        XCTAssertEqual(selectedSpaceID, 3, "狀態綁定應該正常工作")
        
        // 測試邊界情況
        selectedSpaceID = nil
        XCTAssertNil(selectedSpaceID, "應該能處理 nil 狀態")
        
        print("✅ UI 組件整合測試通過")
    }
    
    /**
     * 測試錯誤處理和恢復
     *
     * 驗證系統在各種錯誤情況下的處理能力
     */
    func testErrorHandlingAndRecovery() {
        print("🧪 測試錯誤處理和恢復")
        
        // 測試無可用 Spaces 的情況
        spaceDetector.setTestSpaces([])
        XCTAssertTrue(spaceDetector.availableSpaces.isEmpty, "應該沒有可用 Spaces")
        
        let emptySpaceProfiles = spaceProfileManager.getProfilesForCurrentSpace()
        XCTAssertTrue(emptySpaceProfiles.isEmpty, "沒有 Spaces 時應該沒有設定檔")
        
        // 測試無效 Space ID 的處理
        let invalidProfile = createTestProfile(name: "無效設定檔", spaceID: 999)
        spaceProfileManager.saveProfileToSpace(invalidProfile, spaceID: 999)
        
        let invalidSpaceProfiles = spaceProfileManager.getProfilesForSpace(999)
        // 由於 Space 999 不存在，應該無法儲存
        XCTAssertTrue(invalidSpaceProfiles.isEmpty, "無效 Space 不應該有設定檔")
        
        // 測試 Space 消失後的恢復
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        
        let profile = createTestProfile(name: "測試設定檔", spaceID: 1)
        spaceProfileManager.saveProfileToSpace(profile, spaceID: 1)
        XCTAssertEqual(spaceProfileManager.getProfileCount(for: 1), 1, "應該成功儲存設定檔")
        
        // 移除 Space 1
        let reducedSpaces = Array(testSpaces.dropFirst())
        spaceDetector.setTestSpaces(reducedSpaces)
        
        // 系統應該能處理 Space 消失的情況
        XCTAssertFalse(spaceDetector.availableSpaces.contains { $0.id == 1 }, "Space 1 應該不再可用")
        
        print("✅ 錯誤處理和恢復測試通過")
    }
    
    /**
     * 測試效能和穩定性
     *
     * 驗證系統在高負載下的效能和穩定性
     */
    func testPerformanceAndStability() {
        print("🧪 測試效能和穩定性")
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // 設定測試環境
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        
        // 建立大量設定檔
        let profileCount = 100
        for i in 1...profileCount {
            let spaceID = (i % 3) + 1 // 分配到 Space 1, 2, 3
            let profile = createTestProfile(name: "效能測試設定檔\(i)", spaceID: spaceID)
            spaceProfileManager.saveProfileToSpace(profile, spaceID: spaceID)
        }
        
        // 驗證設定檔數量
        let totalProfiles = spaceProfileManager.getProfileCount(for: 1) +
                           spaceProfileManager.getProfileCount(for: 2) +
                           spaceProfileManager.getProfileCount(for: 3)
        XCTAssertEqual(totalProfiles, profileCount, "應該成功建立所有設定檔")
        
        // 測試大量 Space 切換
        for _ in 1...50 {
            let randomSpace = Int.random(in: 1...3)
            spaceDetector.setCurrentSpace(randomSpace)
            
            let profiles = spaceProfileManager.getProfilesForCurrentSpace()
            XCTAssertFalse(profiles.isEmpty, "每個 Space 都應該有設定檔")
        }
        
        // 測試資料重新載入
        spaceProfileManager.reloadMappings()
        
        let reloadedTotalProfiles = spaceProfileManager.getProfileCount(for: 1) +
                                   spaceProfileManager.getProfileCount(for: 2) +
                                   spaceProfileManager.getProfileCount(for: 3)
        XCTAssertEqual(reloadedTotalProfiles, profileCount, "重新載入後設定檔數量應該保持一致")
        
        let endTime = CFAbsoluteTimeGetCurrent()
        let duration = endTime - startTime
        
        XCTAssertLessThan(duration, 5.0, "效能測試應該在 5 秒內完成")
        
        print("✅ 效能和穩定性測試通過 (耗時: \(String(format: "%.3f", duration))s)")
    }
    
    /**
     * 測試並發安全性
     *
     * 驗證系統在並發操作下的安全性
     */
    func testConcurrencySafety() {
        print("🧪 測試並發安全性")
        
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        
        let expectation = XCTestExpectation(description: "並發操作完成")
        let operationCount = 50
        var completedOperations = 0
        
        // 並發執行多個操作
        for i in 1...operationCount {
            DispatchQueue.global(qos: .userInitiated).async {
                let spaceID = (i % 3) + 1
                let profile = self.createTestProfile(name: "並發測試\(i)", spaceID: spaceID)
                
                self.spaceProfileManager.saveProfileToSpace(profile, spaceID: spaceID)
                
                DispatchQueue.main.async {
                    completedOperations += 1
                    if completedOperations == operationCount {
                        expectation.fulfill()
                    }
                }
            }
        }
        
        wait(for: [expectation], timeout: 10.0)
        
        // 驗證並發操作結果
        let totalProfiles = spaceProfileManager.getProfileCount(for: 1) +
                           spaceProfileManager.getProfileCount(for: 2) +
                           spaceProfileManager.getProfileCount(for: 3)
        XCTAssertEqual(totalProfiles, operationCount, "並發操作應該成功完成")
        
        print("✅ 並發安全性測試通過")
    }
    
    // MARK: - 輔助方法
    
    /**
     * 清理測試環境
     */
    private func clearTestEnvironment() {
        spaceProfileManager.clearAllMappings()
        spaceDetector.resetTestState()
        profileManager.clearAllProfiles()
    }
    
    /**
     * 設定測試環境
     */
    private func setupTestEnvironment() {
        // 設定基本的測試環境
        let testSpaces = createTestSpaces()
        spaceDetector.setTestSpaces(testSpaces)
        spaceDetector.setCurrentSpace(1)
    }
    
    /**
     * 建立測試 Spaces
     */
    private func createTestSpaces() -> [SpaceInfo] {
        return [
            SpaceInfo(id: 1, name: "Space 1", isActive: true, displayName: "工作區 1"),
            SpaceInfo(id: 2, name: "Space 2", isActive: false, displayName: "工作區 2"),
            SpaceInfo(id: 3, name: "Space 3", isActive: false, displayName: "工作區 3")
        ]
    }
    
    /**
     * 建立測試設定檔
     */
    private func createTestProfile(name: String, spaceID: Int?) -> Profile {
        let testWindows = [
            WindowLayout(
                id: UUID().uuidString,
                app: "測試應用程式",
                title: "測試視窗",
                bundleID: "com.test.app",
                frame: WindowFrame(x: 100, y: 100, w: 800, h: 600)
            )
        ]
        
        return Profile(
            name: name,
            windows: testWindows,
            spaceID: spaceID,
            isSpaceSpecific: spaceID != nil,
            createdAt: Date(),
            modifiedAt: Date()
        )
    }
}

// MARK: - 測試擴展

extension ProfileManager {
    /// 清理所有設定檔（測試用）
    func clearAllProfiles() {
        DispatchQueue.main.async {
            self.profiles.removeAll()
        }
    }
}

// MARK: - 測試執行器

/**
 * 執行完整的 Space 整合測試
 */
func runSpaceIntegrationTests() -> Bool {
    print("🚀 開始執行 Space 整合工作流程測試...")
    print("=" * 60)
    
    let testSuite = SpaceIntegrationWorkflowTests()
    testSuite.setUp()
    
    let tests: [(String, () -> Void)] = [
        ("完整 Space 分組工作流程", testSuite.testCompleteSpaceGroupingWorkflow),
        ("Space 切換狀態同步", testSuite.testSpaceSwitchingStateSynchronization),
        ("UI 組件整合", testSuite.testUIComponentIntegration),
        ("錯誤處理和恢復", testSuite.testErrorHandlingAndRecovery),
        ("效能和穩定性", testSuite.testPerformanceAndStability),
        ("並發安全性", testSuite.testConcurrencySafety)
    ]
    
    var passedTests = 0
    let totalTests = tests.count
    
    for (testName, testFunction) in tests {
        print("\n📋 執行測試: \(testName)")
        do {
            testFunction()
            passedTests += 1
            print("✅ \(testName) 通過")
        } catch {
            print("❌ \(testName) 失敗: \(error)")
        }
    }
    
    testSuite.tearDown()
    
    print("\n" + "=" * 60)
    print("📊 測試結果: \(passedTests)/\(totalTests) 通過")
    
    let allPassed = passedTests == totalTests
    print(allPassed ? "🎉 所有整合測試通過！" : "⚠️  部分整合測試失敗")
    
    return allPassed
}