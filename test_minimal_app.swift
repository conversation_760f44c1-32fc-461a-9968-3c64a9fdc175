import SwiftUI
import AppKit

// 最小化的測試應用程式，用於隔離崩潰問題

@main
struct MinimalTestApp: App {
    var body: some Scene {
        Settings {
            Text("Minimal Test App")
        }
    }
}

class MinimalAppDelegate: NSObject, NSApplicationDelegate {
    private var statusItem: NSStatusItem?
    
    func applicationDidFinishLaunching(_ notification: Notification) {
        print("MinimalTestApp: 開始啟動")
        
        // 隱藏 Dock 圖示
        NSApp.setActivationPolicy(.accessory)
        
        // 設定選單列
        setupMenuBar()
        
        print("MinimalTestApp: 啟動完成")
    }
    
    private func setupMenuBar() {
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.variableLength)
        
        if let button = statusItem?.button {
            button.title = "Test"
            button.action = #selector(showMessage)
            button.target = self
        }
    }
    
    @objc private func showMessage() {
        let alert = NSAlert()
        alert.messageText = "測試應用程式正常運行"
        alert.runModal()
    }
}
