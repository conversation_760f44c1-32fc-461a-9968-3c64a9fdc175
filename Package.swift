// swift-tools-version: 5.10
import PackageDescription

let package = Package(
    name: "Workspace",
    platforms: [
        .macOS(.v13)
    ],
    products: [
        .executable(name: "Workspace", targets: ["Workspace"])
    ],
    targets: [
        .executableTarget(
            name: "Workspace",
            path: "Workspace",
            resources: [
                .process("Resources")
            ]
        ),
        .testTarget(
            name: "WorkspaceTests",
            dependencies: ["Workspace"],
            path: "Tests"
        )
    ]
)
