import Foundation
import Combine

/// 管理 Space 和 Profile 關聯的主要管理器
class SpaceProfileManager: ObservableObject {
    static let shared = SpaceProfileManager()
    
    @Published var spaceProfileMapping: [Int: [Profile]] = [:]
    @Published var currentSpace: SpaceInfo?
    
    private let profileManager: ProfileManager
    private let spaceDetector: SpaceDetector
    private let lazyLoader: LazyProfileLoader
    private let mappingFileURL: URL
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        self.profileManager = ProfileManager.shared
        self.spaceDetector = SpaceDetector.shared
        self.lazyLoader = LazyProfileLoader.shared

        // 設定映射檔案路徑
        let homeURL = FileManager.default.homeDirectoryForCurrentUser
        let layoutsPath = homeURL.appendingPathComponent(".hammerspoon/layouts")
        self.mappingFileURL = layoutsPath.appendingPathComponent("space_profile_mapping.json")

        setupObservers()

        // 延遲初始化，避免在視圖更新期間修改 @Published 屬性
        DispatchQueue.main.async { [weak self] in
            self?.loadSpaceProfileMapping()
            self?.updateCurrentSpace()
        }
    }
    
    // MARK: - Public Methods
    
    /// 獲取指定 Space 的所有設定檔
    func getProfilesForSpace(_ spaceID: Int) -> [Profile] {
        return spaceProfileMapping[spaceID] ?? []
    }
    
    /// 獲取當前 Space 的所有設定檔
    func getProfilesForCurrentSpace() -> [Profile] {
        guard let currentSpaceID = spaceDetector.currentSpaceID else {
            return getProfilesForSpace(spaceDetector.getDefaultSpaceID())
        }
        return getProfilesForSpace(currentSpaceID)
    }
    
    /// 懶載入指定 Space 的設定檔元資料
    func getProfileMetadataForSpace(_ spaceID: Int) -> [ProfileMetadata] {
        return lazyLoader.getAllProfileMetadata().filter { $0.spaceID == spaceID }
    }
    
    /// 懶載入指定設定檔
    func loadProfile(_ profileName: String) -> AnyPublisher<Profile?, Never> {
        return lazyLoader.loadProfile(profileName)
    }
    
    /// 預載入指定 Space 的設定檔
    func preloadSpaceProfiles(_ spaceID: Int) {
        lazyLoader.preloadSpaceMetadata(spaceID)
    }
    
    /// 將設定檔儲存到指定 Space
    func saveProfileToSpace(_ profile: Profile, spaceID: Int) {
        // 驗證 Space 是否可存取
        guard spaceDetector.isSpaceAccessible(spaceID) else {
            print("Space \(spaceID) 無法存取，無法儲存設定檔")
            return
        }
        
        // 更新設定檔的 Space 資訊
        var updatedProfile = profile
        updatedProfile.spaceID = spaceID
        updatedProfile.isSpaceSpecific = true
        updatedProfile.modifiedAt = Date()
        
        // 儲存到 ProfileManager
        profileManager.saveProfile(updatedProfile)
        
        // 更新映射
        addProfileToMapping(updatedProfile, spaceID: spaceID)
        saveSpaceProfileMapping()
    }
    
    /// 在 Spaces 之間移動設定檔
    func moveProfileBetweenSpaces(_ profile: Profile, from sourceSpaceID: Int, to targetSpaceID: Int) {
        // 驗證目標 Space 是否可存取
        guard spaceDetector.isSpaceAccessible(targetSpaceID) else {
            print("目標 Space \(targetSpaceID) 無法存取，無法移動設定檔")
            return
        }
        
        // 從來源 Space 移除
        removeProfileFromMapping(profile, spaceID: sourceSpaceID)
        
        // 更新設定檔的 Space 資訊
        var updatedProfile = profile
        updatedProfile.spaceID = targetSpaceID
        updatedProfile.modifiedAt = Date()
        
        // 儲存更新的設定檔
        profileManager.saveProfile(updatedProfile)
        
        // 添加到目標 Space
        addProfileToMapping(updatedProfile, spaceID: targetSpaceID)
        saveSpaceProfileMapping()
    }
    
    /// 從指定 Space 刪除設定檔
    func deleteProfileFromSpace(_ profile: Profile, spaceID: Int) {
        // 從映射中移除
        removeProfileFromMapping(profile, spaceID: spaceID)
        
        // 從 ProfileManager 刪除
        profileManager.deleteProfile(profile)
        
        saveSpaceProfileMapping()
    }
    
    /// 重新載入所有映射資料
    func reloadMappings() {
        loadSpaceProfileMapping()
        rebuildMappingFromProfiles()
    }
    
    // MARK: - Private Methods
    
    /// 設定觀察者
    private func setupObservers() {
        // 監聽 ProfileManager 的變更
        profileManager.$profiles
            .sink { [weak self] _ in
                self?.rebuildMappingFromProfiles()
            }
            .store(in: &cancellables)
        
        // 監聽 SpaceDetector 的變更
        spaceDetector.$currentSpaceID
            .sink { [weak self] _ in
                self?.updateCurrentSpace()
            }
            .store(in: &cancellables)
        
        spaceDetector.$availableSpaces
            .sink { [weak self] _ in
                self?.updateCurrentSpace()
            }
            .store(in: &cancellables)
    }
    
    /// 更新當前 Space 資訊
    private func updateCurrentSpace() {
        let newCurrentSpace: SpaceInfo?
        if let currentSpaceID = spaceDetector.currentSpaceID {
            newCurrentSpace = spaceDetector.availableSpaces.first { $0.id == currentSpaceID }
        } else {
            newCurrentSpace = nil
        }

        // 使用 asyncAfter 避免在視圖更新期間修改 @Published 屬性
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
            self.currentSpace = newCurrentSpace
        }
    }
    
    /// 從設定檔重建映射
    private func rebuildMappingFromProfiles() {
        var newMapping: [Int: [Profile]] = [:]
        
        for profile in profileManager.profiles {
            if let spaceID = profile.spaceID {
                if newMapping[spaceID] == nil {
                    newMapping[spaceID] = []
                }
                newMapping[spaceID]?.append(profile)
            } else if profile.isSpaceSpecific {
                // 處理舊格式的 Space-specific 設定檔，分配到預設 Space
                let defaultSpaceID = spaceDetector.getDefaultSpaceID()
                if newMapping[defaultSpaceID] == nil {
                    newMapping[defaultSpaceID] = []
                }
                newMapping[defaultSpaceID]?.append(profile)
            }
        }
        
        // 按修改時間排序每個 Space 的設定檔
        for spaceID in newMapping.keys {
            newMapping[spaceID]?.sort { $0.modifiedAt > $1.modifiedAt }
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
            self.spaceProfileMapping = newMapping
        }
    }
    
    /// 將設定檔添加到映射
    private func addProfileToMapping(_ profile: Profile, spaceID: Int) {
        if spaceProfileMapping[spaceID] == nil {
            spaceProfileMapping[spaceID] = []
        }
        
        // 移除可能存在的舊版本
        spaceProfileMapping[spaceID]?.removeAll { $0.id == profile.id }
        
        // 添加新版本
        spaceProfileMapping[spaceID]?.append(profile)
        
        // 重新排序
        spaceProfileMapping[spaceID]?.sort { $0.modifiedAt > $1.modifiedAt }
    }
    
    /// 從映射中移除設定檔
    private func removeProfileFromMapping(_ profile: Profile, spaceID: Int) {
        spaceProfileMapping[spaceID]?.removeAll { $0.id == profile.id }
        
        // 如果 Space 沒有設定檔了，移除該 Space 的條目
        if spaceProfileMapping[spaceID]?.isEmpty == true {
            spaceProfileMapping.removeValue(forKey: spaceID)
        }
    }
}

// MARK: - Storage Methods

extension SpaceProfileManager {
    /// 載入 Space-Profile 映射
    private func loadSpaceProfileMapping() {
        guard FileManager.default.fileExists(atPath: mappingFileURL.path) else {
            // 如果映射檔案不存在，從現有設定檔重建
            rebuildMappingFromProfiles()
            return
        }
        
        do {
            let data = try Data(contentsOf: mappingFileURL)
            let mappingData = try JSONDecoder().decode(SpaceProfileMappingData.self, from: data)
            
            // 驗證載入的資料
            var validMapping: [Int: [Profile]] = [:]
            
            for (spaceIDString, profileNames) in mappingData.spaceProfileMapping {
                guard let spaceID = Int(spaceIDString),
                      spaceDetector.isSpaceAccessible(spaceID) else {
                    continue
                }
                
                let profiles = profileNames.compactMap { profileName in
                    profileManager.profiles.first { $0.name == profileName }
                }
                
                if !profiles.isEmpty {
                    validMapping[spaceID] = profiles.sorted { $0.modifiedAt > $1.modifiedAt }
                }
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
                self.spaceProfileMapping = validMapping
            }
            
        } catch {
            print("載入 Space-Profile 映射失敗: \(error)")
            // 發生錯誤時從設定檔重建映射
            rebuildMappingFromProfiles()
        }
    }
    
    /// 儲存 Space-Profile 映射
    private func saveSpaceProfileMapping() {
        // 轉換為可序列化的格式
        var mappingDict: [String: [String]] = [:]
        
        for (spaceID, profiles) in spaceProfileMapping {
            let profileNames = profiles.map { $0.name }
            mappingDict[String(spaceID)] = profileNames
        }
        
        let mappingData = SpaceProfileMappingData(
            spaceProfileMapping: mappingDict,
            defaultSpace: spaceDetector.getDefaultSpaceID(),
            lastUpdated: Date()
        )
        
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(mappingData)
            try data.write(to: mappingFileURL)
        } catch {
            print("儲存 Space-Profile 映射失敗: \(error)")
        }
    }
}



// MARK: - Utility Extensions

extension SpaceProfileManager {
    /// 獲取所有有設定檔的 Space IDs
    var spacesWithProfiles: [Int] {
        return Array(spaceProfileMapping.keys).sorted()
    }
    
    /// 獲取指定 Space 的設定檔數量
    func getProfileCount(for spaceID: Int) -> Int {
        return spaceProfileMapping[spaceID]?.count ?? 0
    }
    
    /// 檢查指定 Space 是否有設定檔
    func hasProfiles(in spaceID: Int) -> Bool {
        return getProfileCount(for: spaceID) > 0
    }
    
    /// 獲取設定檔所屬的 Space ID
    func getSpaceID(for profile: Profile) -> Int? {
        for (spaceID, profiles) in spaceProfileMapping {
            if profiles.contains(where: { $0.id == profile.id }) {
                return spaceID
            }
        }
        return profile.spaceID
    }
    
    /// 檢查設定檔是否屬於指定 Space
    func isProfileInSpace(_ profile: Profile, spaceID: Int) -> Bool {
        return spaceProfileMapping[spaceID]?.contains(where: { $0.id == profile.id }) == true
    }
}