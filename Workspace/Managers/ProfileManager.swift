import Foundation
import Combine

class ProfileManager: ObservableObject {
    static let shared = ProfileManager()
    
    @Published var profiles: [Profile] = []
    private let layoutsPath: URL
    private let spaceDetector = SpaceDetector.shared
    
    private init() {
        let homeURL = FileManager.default.homeDirectoryForCurrentUser
        layoutsPath = homeURL.appendingPathComponent(".hammerspoon/layouts")

        createLayoutsDirectoryIfNeeded()

        // 延遲載入 profiles，避免在視圖更新期間修改 @Published 屬性
        DispatchQueue.main.async { [weak self] in
            self?.loadProfiles()
            self?.performMigrationIfNeeded()
        }
    }
    
    private func createLayoutsDirectoryIfNeeded() {
        if !FileManager.default.fileExists(atPath: layoutsPath.path) {
            try? FileManager.default.createDirectory(at: layoutsPath, withIntermediateDirectories: true)
        }
    }
    
    func loadProfiles() {
        do {
            let files = try FileManager.default.contentsOfDirectory(at: layoutsPath, includingPropertiesForKeys: nil)
            let jsonFiles = files.filter { $0.pathExtension == "json" }

            profiles = jsonFiles.compactMap { url in
                loadProfile(from: url, loadWindows: false) // 預設不載入 windows
            }.sorted { $0.modifiedAt > $1.modifiedAt }
            
            // 驗證載入的設定檔的 Space 資訊
            validateSpaceInformation()
        } catch {
            print("載入 Profiles 失敗: \(error)")
        }
    }

    private func loadProfile(from url: URL, loadWindows: Bool) -> Profile? {
        do {
            let data = try Data(contentsOf: url)
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601

            if loadWindows {
                // 載入完整設定檔，包含 windows
                return try? decoder.decode(Profile.self, from: data)
            } else {
                // 只載入元數據，不載入 windows
                var profile = try decoder.decode(Profile.self, from: data)
                profile.windows = nil
                return profile
            }
        } catch {
            print("載入 Profile \(url.lastPathComponent) 失敗: \(error)")
            return nil
        }
    }

    func loadWindowsForProfile(_ profile: Profile) -> [WindowLayout]? {
        let url = layoutsPath.appendingPathComponent(profile.jsonFileName)
        guard let loadedProfile = loadProfile(from: url, loadWindows: true) else {
            return nil
        }
        return loadedProfile.windows
    }
    
    func saveProfile(_ profile: Profile) {
        var profileToSave = profile
        
        // 如果設定檔沒有 Space ID，但標記為 Space 專用，則使用當前 Space
        if profileToSave.isSpaceSpecific && profileToSave.spaceID == nil {
            profileToSave.spaceID = spaceDetector.getCurrentSpace() ?? spaceDetector.getDefaultSpaceID()
        }
        
        // 驗證 Space ID 的有效性
        if let spaceID = profileToSave.spaceID {
            if !spaceDetector.isSpaceAccessible(spaceID) {
                print("警告: Space \(spaceID) 無法存取，將使用預設 Space")
                profileToSave.spaceID = spaceDetector.getDefaultSpaceID()
            }
        }
        
        let url = layoutsPath.appendingPathComponent(profileToSave.jsonFileName)
        
        do {
            // 使用新的完整 Profile JSON 格式
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(profileToSave)
            try data.write(to: url)
            
            // 優化：直接更新陣列，而不是重新載入所有
            if let index = profiles.firstIndex(where: { $0.id == profile.id }) {
                profiles[index] = profileToSave
            } else {
                profiles.append(profileToSave)
            }
            profiles.sort { $0.modifiedAt > $1.modifiedAt }
        } catch {
            print("儲存 Profile 失敗: \(error)")
        }
    }
    
    func deleteProfile(_ profile: Profile) {
        let url = layoutsPath.appendingPathComponent(profile.jsonFileName)
        
        do {
            try FileManager.default.removeItem(at: url)
            
            // 優化：直接從陣列中移除
            profiles.removeAll { $0.id == profile.id }
        } catch {
            print("刪除 Profile 失敗: \(error)")
        }
    }
    
    func renameProfile(_ profile: Profile, to newName: String) {
        let oldURL = layoutsPath.appendingPathComponent(profile.jsonFileName)
        
        // 建立新的 Profile 實例以獲得正確的檔案名稱
        var newProfile = profile
        newProfile.name = newName
        let newURL = layoutsPath.appendingPathComponent(newProfile.jsonFileName)

        do {
            try FileManager.default.moveItem(at: oldURL, to: newURL)
            
            // 優化：直接更新陣列中的項目
            if let index = profiles.firstIndex(where: { $0.id == profile.id }) {
                profiles[index].name = newName
            }
        } catch {
            print("重命名 Profile 失敗: \(error)")
        }
    }
    
    // MARK: - Space-aware Filtering Methods
    
    /// 獲取屬於指定 Space 的設定檔
    func getProfilesForSpace(_ spaceID: Int) -> [Profile] {
        return profiles.filter { profile in
            profile.belongsToSpace(spaceID)
        }
    }
    
    /// 獲取屬於當前 Space 的設定檔
    func getProfilesForCurrentSpace() -> [Profile] {
        guard let currentSpaceID = spaceDetector.currentSpaceID else {
            return getProfilesForSpace(spaceDetector.getDefaultSpaceID())
        }
        return getProfilesForSpace(currentSpaceID)
    }
    
    /// 獲取所有 Space 感知的設定檔
    func getSpaceAwareProfiles() -> [Profile] {
        return profiles.filter { $0.isSpaceAware }
    }
    
    /// 獲取所有非 Space 感知的設定檔（全域設定檔）
    func getGlobalProfiles() -> [Profile] {
        return profiles.filter { !$0.isSpaceAware }
    }
    
    /// 獲取按 Space 分組的設定檔
    func getProfilesGroupedBySpace() -> [Int: [Profile]] {
        var groupedProfiles: [Int: [Profile]] = [:]
        
        // 初始化支援的 Spaces
        for spaceID in 1...spaceDetector.maxSupportedSpaces {
            groupedProfiles[spaceID] = []
        }
        
        // 將設定檔分組到對應的 Space
        for profile in profiles {
            if let spaceID = profile.spaceID {
                groupedProfiles[spaceID, default: []].append(profile)
            }
        }
        
        return groupedProfiles
    }
    
    /// 檢查指定 Space 是否有設定檔
    func hasProfilesForSpace(_ spaceID: Int) -> Bool {
        return !getProfilesForSpace(spaceID).isEmpty
    }
    
    // MARK: - Space Information Validation
    
    /// 驗證所有設定檔的 Space 資訊
    private func validateSpaceInformation() {
        var hasChanges = false
        
        for (index, profile) in profiles.enumerated() {
            var updatedProfile = profile
            var needsUpdate = false
            
            // 檢查 Space ID 的有效性
            if let spaceID = profile.spaceID {
                if !spaceDetector.isSpaceAccessible(spaceID) {
                    print("警告: Profile '\(profile.name)' 的 Space \(spaceID) 無法存取")
                    // 可以選擇將其移動到預設 Space 或保持原狀
                    // 這裡我們選擇保持原狀，但記錄警告
                }
            }
            
            // 檢查 isSpaceSpecific 和 spaceID 的一致性
            if profile.isSpaceSpecific && profile.spaceID == nil {
                // 如果標記為 Space 專用但沒有 Space ID，嘗試從檔案名稱推斷
                if let inferredSpaceID = inferSpaceIDFromFileName(profile.jsonFileName) {
                    updatedProfile.spaceID = inferredSpaceID
                    needsUpdate = true
                    print("為 Profile '\(profile.name)' 推斷 Space ID: \(inferredSpaceID)")
                }
            }
            
            if needsUpdate {
                profiles[index] = updatedProfile
                hasChanges = true
            }
        }
        
        // 如果有變更，觸發 UI 更新
        if hasChanges {
            DispatchQueue.main.async {
                self.objectWillChange.send()
            }
        }
    }
    
    /// 從檔案名稱推斷 Space ID
    private func inferSpaceIDFromFileName(_ fileName: String) -> Int? {
        // 檢查檔案名稱是否包含 _space 模式
        if fileName.contains("_space") {
            let components = fileName.components(separatedBy: "_space")
            if components.count >= 2 {
                let spaceComponent = components[1].replacingOccurrences(of: ".json", with: "")
                if !spaceComponent.isEmpty {
                    return Int(spaceComponent)
                }
            }
        }
        return nil
    }
    
    // MARK: - Migration Support
    
    /// 執行資料遷移（如果需要）
    private func performMigrationIfNeeded() {
        let migrationManager = ProfileMigrationManager.shared
        
        if migrationManager.needsMigration() {
            print("偵測到需要資料遷移，開始執行...")
            
            let result = migrationManager.performMigration()
            
            switch result {
            case .success:
                print("資料遷移成功完成")
            case .alreadyCompleted:
                print("資料遷移已完成")
            case .failed(let error):
                print("資料遷移失敗: \(error.localizedDescription)")
                // 可以選擇顯示錯誤訊息給使用者
            }
        }
    }
    
    /// 移動設定檔到不同的 Space
    func moveProfileToSpace(_ profile: Profile, targetSpaceID: Int) {
        guard spaceDetector.isSpaceAccessible(targetSpaceID) else {
            print("錯誤: 目標 Space \(targetSpaceID) 無法存取")
            return
        }
        
        // 刪除舊檔案
        let oldURL = layoutsPath.appendingPathComponent(profile.jsonFileName)
        
        // 建立新的設定檔實例
        var newProfile = profile
        newProfile.spaceID = targetSpaceID
        newProfile.isSpaceSpecific = true
        newProfile.modifiedAt = Date()
        
        let newURL = layoutsPath.appendingPathComponent(newProfile.jsonFileName)
        
        do {
            // 如果新檔案已存在，先刪除
            if FileManager.default.fileExists(atPath: newURL.path) {
                try FileManager.default.removeItem(at: newURL)
            }
            
            // 移動檔案
            try FileManager.default.moveItem(at: oldURL, to: newURL)
            
            // 重新載入設定檔
            loadProfiles()
            
            print("成功將 Profile '\(profile.name)' 移動到 Space \(targetSpaceID)")
        } catch {
            print("移動 Profile 到 Space 失敗: \(error)")
        }
    }
}