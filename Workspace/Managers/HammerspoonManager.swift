import Foundation
import AppKit
import Combine

class HammerspoonManager: ObservableObject {
    static let shared = HammerspoonManager()

    @Published var isInstalled = false
    @Published var isConfigured = false
    @Published var isRunning = false

    private let hammerspoonPath = "/Applications/Hammerspoon.app"
    private let configPath: URL
    private let layoutsPath: URL
    private let spaceDetector = SpaceDetector.shared

    private init() {
        let homeURL = FileManager.default.homeDirectoryForCurrentUser
        configPath = homeURL.appendingPathComponent(".hammerspoon/init.lua")
        layoutsPath = homeURL.appendingPathComponent(".hammerspoon/layouts")

        // 延遲初始化檢查，避免在視圖更新期間修改 @Published 屬性
        DispatchQueue.main.async { [weak self] in
            self?.checkInstallation()
            self?.checkConfiguration()
        }
    }

    // MARK: - Installation & Configuration

    func checkInstallation() {
        isInstalled = FileManager.default.fileExists(atPath: hammerspoonPath)

        if isInstalled {
            checkIfRunning()
        }
    }

    private func checkIfRunning() {
        let runningApps = NSWorkspace.shared.runningApplications
        isRunning = runningApps.contains { $0.bundleIdentifier == "org.hammerspoon.Hammerspoon" }
    }

    func checkConfiguration() {
        isConfigured = FileManager.default.fileExists(atPath: configPath.path)
    }

    func openHammerspoonWebsite() {
        if let url = URL(string: "https://www.hammerspoon.org/") {
            NSWorkspace.shared.open(url)
        }
    }

    func installConfiguration() {
        guard let configURL = Bundle.main.url(forResource: "init", withExtension: "lua") else {
            print("找不到配置檔案模板")
            return
        }

        do {
            // 創建 .hammerspoon 目錄
            let hammerspoonDir = configPath.deletingLastPathComponent()
            try FileManager.default.createDirectory(at: hammerspoonDir, withIntermediateDirectories: true)

            // 創建 layouts 目錄
            try FileManager.default.createDirectory(at: layoutsPath, withIntermediateDirectories: true)

            // 複製配置檔案
            let configContent = try String(contentsOf: configURL)
            try configContent.write(to: configPath, atomically: true, encoding: .utf8)

            isConfigured = true

            // 重新載入 Hammerspoon 配置
            reloadConfiguration()

        } catch {
            print("安裝配置檔案失敗: \(error)")
        }
    }

    // MARK: - Hammerspoon Communication

    private func executeLuaCode(_ code: String) {
        // 重新檢查 Hammerspoon 狀態
        checkIfRunning()
        
        print("執行 Lua 代碼: \(code)")
        print("Hammerspoon 狀態 - 已安裝: \(isInstalled), 正在運行: \(isRunning)")
        
        guard isInstalled else {
            print("❌ Hammerspoon 未安裝")
            NotificationManager.shared.showError(
                title: "Hammerspoon 未安裝",
                message: "請先安裝 Hammerspoon"
            )
            return
        }
        
        guard isRunning else {
            print("❌ Hammerspoon 未運行")
            NotificationManager.shared.showError(
                title: "Hammerspoon 未運行",
                message: "請啟動 Hammerspoon 應用程式"
            )
            return
        }

        // 使用 AppleScript 執行 Lua 代碼
        executeViaAppleScript(code)
    }
    
    private func executeViaAppleScript(_ luaCode: String) {
        let escapedCode = luaCode.replacingOccurrences(of: "\"", with: "\\\"")
        let appleScript = """
        tell application "Hammerspoon"
            execute lua code "\(escapedCode)"
        end tell
        """
        
        print("執行 AppleScript: \(appleScript)")
        
        let task = Process()
        task.launchPath = "/usr/bin/osascript"
        task.arguments = ["-e", appleScript]
        
        let pipe = Pipe()
        let errorPipe = Pipe()
        task.standardOutput = pipe
        task.standardError = errorPipe
        
        task.launch()
        task.waitUntilExit()
        
        let data = pipe.fileHandleForReading.readDataToEndOfFile()
        let errorData = errorPipe.fileHandleForReading.readDataToEndOfFile()
        let output = String(data: data, encoding: .utf8)?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let errorOutput = String(data: errorData, encoding: .utf8)?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        
        print("AppleScript 輸出: '\(output)'")
        print("AppleScript 錯誤: '\(errorOutput)'")
        print("終止狀態: \(task.terminationStatus)")
        
        // 檢查返回值
        if output.lowercased() == "false" {
            print("⚠️ Lua 函數返回 false")
        } else if output.lowercased() == "true" {
            print("✅ Lua 函數返回 true")
        } else if !output.isEmpty {
            print("📝 Lua 函數返回: \(output)")
        }
        
        if task.terminationStatus != 0 || !errorOutput.isEmpty {
            print("❌ AppleScript 執行失敗")
            NotificationManager.shared.showError(
                title: "通信失敗",
                message: "無法與 Hammerspoon 通信: \(errorOutput.isEmpty ? "未知錯誤" : errorOutput)"
            )
        }
    }
    
    private func executeViaAppleScriptWithReturn(_ luaCode: String) -> String {
        let escapedCode = luaCode.replacingOccurrences(of: "\"", with: "\\\"")
        let appleScript = """
        tell application "Hammerspoon"
            execute lua code "\(escapedCode)"
        end tell
        """
        
        print("執行 AppleScript (with return): \(appleScript)")
        
        let task = Process()
        task.launchPath = "/usr/bin/osascript"
        task.arguments = ["-e", appleScript]
        
        let pipe = Pipe()
        let errorPipe = Pipe()
        task.standardOutput = pipe
        task.standardError = errorPipe
        
        task.launch()
        task.waitUntilExit()
        
        let data = pipe.fileHandleForReading.readDataToEndOfFile()
        let errorData = errorPipe.fileHandleForReading.readDataToEndOfFile()
        let output = String(data: data, encoding: .utf8)?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let errorOutput = String(data: errorData, encoding: .utf8)?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        
        print("AppleScript 返回值: '\(output)'")
        if !errorOutput.isEmpty {
            print("AppleScript 錯誤: '\(errorOutput)'")
        }
        
        return output
    }

    func saveCurrentLayout(profileName: String) {
        print("🔄 開始儲存佈局: \(profileName)")
        
        // 獲取當前 Space 資訊
        let currentSpaceID = spaceDetector.getCurrentSpace()
        print("當前 Space ID: \(currentSpaceID ?? -1)")
        
        // 根據是否有 Space 資訊決定使用哪個 Lua 函數
        let luaCode: String
        if let spaceID = currentSpaceID {
            luaCode = "return saveWindowLayoutWithSpace('\(profileName)', \(spaceID))"
        } else {
            luaCode = "return saveWindowLayout('\(profileName)')"
        }
        
        // 先檢查 Hammerspoon 狀態
        checkIfRunning()
        
        guard isInstalled else {
            print("❌ Hammerspoon 未安裝")
            NotificationManager.shared.showError(
                title: "Hammerspoon 未安裝",
                message: "請先安裝 Hammerspoon"
            )
            return
        }
        
        guard isRunning else {
            print("❌ Hammerspoon 未運行")
            NotificationManager.shared.showError(
                title: "Hammerspoon 未運行",
                message: "請啟動 Hammerspoon 應用程式"
            )
            return
        }

        // 執行 Lua 代碼並獲取返回值
        let result = executeViaAppleScriptWithReturn(luaCode)
        print("Lua 執行結果: \(result)")
        
        // 檢查檔案是否實際被創建
        let layoutsPath = FileManager.default.homeDirectoryForCurrentUser
            .appendingPathComponent(".hammerspoon/layouts")
        let profilePath = layoutsPath.appendingPathComponent("\(profileName).json")
        
        // 延遲檢查檔案是否存在
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            let fileExists = FileManager.default.fileExists(atPath: profilePath.path)
            print("檔案存在檢查: \(fileExists) - \(profilePath.path)")
            
            if fileExists {
                let spaceInfo = currentSpaceID != nil ? " (Space \(currentSpaceID!))" : ""
                print("✅ 儲存成功")
                NotificationManager.shared.showSuccess(
                    title: "佈局已儲存",
                    message: "Profile「\(profileName)」已成功儲存\(spaceInfo)"
                )
                // 重新載入 profiles
                ProfileManager.shared.loadProfiles()
            } else {
                print("❌ 儲存失敗 - 檔案不存在")
                NotificationManager.shared.showError(
                    title: "儲存失敗",
                    message: "無法儲存 Profile「\(profileName)」，請檢查 Hammerspoon 是否正在運行"
                )
            }
        }
    }

    func restoreLayout(profileName: String) {
        restoreLayout(profileName: profileName, forceRestore: false)
    }
    
    func restoreLayout(profileName: String, forceRestore: Bool) {
        // 先檢查 profile 是否存在
        let layoutsPath = FileManager.default.homeDirectoryForCurrentUser
            .appendingPathComponent(".hammerspoon/layouts")
        let profilePath = layoutsPath.appendingPathComponent("\(profileName).json")
        
        guard FileManager.default.fileExists(atPath: profilePath.path) else {
            NotificationManager.shared.showError(
                title: "還原失敗",
                message: "找不到 Profile「\(profileName)」"
            )
            return
        }
        
        // 讀取 Profile 資料以檢查 Space 資訊
        let currentSpaceID = spaceDetector.getCurrentSpace()
        let profileSpaceID = getProfileSpaceID(profileName: profileName)
        
        print("當前 Space ID: \(currentSpaceID ?? -1)")
        print("Profile Space ID: \(profileSpaceID ?? -1)")
        
        // 檢查是否需要跨 Space 還原確認
        if !forceRestore && shouldShowCrossSpaceConfirmation(currentSpaceID: currentSpaceID, profileSpaceID: profileSpaceID) {
            showCrossSpaceRestoreConfirmation(
                profileName: profileName,
                currentSpaceID: currentSpaceID,
                profileSpaceID: profileSpaceID
            )
            return
        }
        
        // 驗證 Space 上下文
        if let profileSpaceID = profileSpaceID, let currentSpaceID = currentSpaceID {
            if profileSpaceID != currentSpaceID && !forceRestore {
                print("⚠️ Space 不匹配，但用戶已確認繼續")
            }
        }
        
        // 立即顯示開始還原的通知
        let spaceInfo = profileSpaceID != nil ? " (來自 Space \(profileSpaceID!))" : ""
        NotificationManager.shared.showInfo(
            title: "正在還原佈局",
            message: "正在還原 Profile「\(profileName)」\(spaceInfo)..."
        )

        let luaCode = "restoreWindowLayout('\(profileName)')"
        executeLuaCode(luaCode)

        // 延遲顯示成功通知（給 Hammerspoon 時間執行）
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            NotificationManager.shared.showSuccess(
                title: "佈局已還原",
                message: "Profile「\(profileName)」已成功還原"
            )
            NotificationManager.shared.playCompletionSound()
        }
    }

    func reloadConfiguration() {
        let luaCode = "hs.reload()"
        executeLuaCode(luaCode)
    }

    // MARK: - Space-Specific Layout Management

    func saveCurrentSpaceLayout(profileName: String) {
        print("🔄 開始儲存當前 Space 佈局: \(profileName)")

        let luaCode = "return saveCurrentSpaceLayout('\(profileName)')"

        // 先檢查 Hammerspoon 狀態
        checkIfRunning()

        guard isInstalled else {
            print("❌ Hammerspoon 未安裝")
            NotificationManager.shared.showError(
                title: "Hammerspoon 未安裝",
                message: "請先安裝 Hammerspoon"
            )
            return
        }

        guard isRunning else {
            print("❌ Hammerspoon 未運行")
            NotificationManager.shared.showError(
                title: "Hammerspoon 未運行",
                message: "請啟動 Hammerspoon 應用程式"
            )
            return
        }

        // 執行 Lua 代碼並獲取返回值
        let result = executeViaAppleScriptWithReturn(luaCode)
        print("Space 佈局儲存結果: \(result)")

        // 檢查檔案是否實際被創建
        let layoutsPath = FileManager.default.homeDirectoryForCurrentUser
            .appendingPathComponent(".hammerspoon/layouts")
        let profilePath = layoutsPath.appendingPathComponent("\(profileName)_space.json")

        // 延遲檢查檔案是否存在
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            let fileExists = FileManager.default.fileExists(atPath: profilePath.path)
            print("Space 佈局檔案存在檢查: \(fileExists) - \(profilePath.path)")

            if fileExists {
                print("✅ Space 佈局儲存成功")
                NotificationManager.shared.showSuccess(
                    title: "Space 佈局已儲存",
                    message: "Space Profile「\(profileName)」已成功儲存"
                )
                // 重新載入 profiles
                ProfileManager.shared.loadProfiles()
            } else {
                print("❌ Space 佈局儲存失敗 - 檔案不存在")
                NotificationManager.shared.showError(
                    title: "Space 佈局儲存失敗",
                    message: "無法儲存 Space Profile「\(profileName)」，請檢查 Hammerspoon 是否正在運行"
                )
            }
        }
    }

    func restoreCurrentSpaceLayout(profileName: String) {
        // 先檢查 space profile 是否存在
        let layoutsPath = FileManager.default.homeDirectoryForCurrentUser
            .appendingPathComponent(".hammerspoon/layouts")
        let profilePath = layoutsPath.appendingPathComponent("\(profileName)_space.json")

        guard FileManager.default.fileExists(atPath: profilePath.path) else {
            NotificationManager.shared.showError(
                title: "還原失敗",
                message: "找不到 Space Profile「\(profileName)」"
            )
            return
        }

        // 立即顯示開始還原的通知
        NotificationManager.shared.showInfo(
            title: "正在還原 Space 佈局",
            message: "正在還原 Space Profile「\(profileName)」..."
        )

        let luaCode = "restoreCurrentSpaceLayout('\(profileName)')"
        executeLuaCode(luaCode)

        // 延遲顯示成功通知（給 Hammerspoon 時間執行）
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            NotificationManager.shared.showSuccess(
                title: "Space 佈局已還原",
                message: "Space Profile「\(profileName)」已成功還原"
            )
            NotificationManager.shared.playCompletionSound()
        }
    }

    // MARK: - Settings Sync

    func updateAlertSettings(textSize: Double, duration: Double) {
        let luaCode = """
        alertStyle = { textSize = \(Int(textSize)), duration = \(duration) }
        """
        executeLuaCode(luaCode)
    }

    // MARK: - Window Highlighting

    func highlightWindow(appName: String, windowTitle: String, bundleID: String) {
        print("🔍 高亮視窗: \(appName) - \(windowTitle)")

        // 轉義特殊字符以避免 Lua 語法錯誤
        let escapedAppName = appName.replacingOccurrences(of: "'", with: "\\'")
        let escapedWindowTitle = windowTitle.replacingOccurrences(of: "'", with: "\\'")
        let escapedBundleID = bundleID.replacingOccurrences(of: "'", with: "\\'")

        // 簡化的高亮邏輯
        let luaCode = """
        local targetWindow = nil
        local app = hs.application.find('\(escapedBundleID)')
        if not app then app = hs.application.find('\(escapedAppName)') end
        if app then
            local windows = app:allWindows()
            for _, win in ipairs(windows) do
                if win:title() == '\(escapedWindowTitle)' or '\(escapedWindowTitle)' == '' then
                    targetWindow = win
                    break
                end
            end
        end
        if targetWindow then
            targetWindow:focus()
            targetWindow:application():activate()
            hs.alert.show('已高亮視窗: \(escapedAppName)')
        else
            hs.alert.show('找不到視窗: \(escapedAppName)')
        end
        """

        executeLuaCode(luaCode)
    }

    func clearWindowHighlight() {
        print("🔍 清除視窗高亮")
        let luaCode = "clearHighlight()"
        executeLuaCode(luaCode)
    }

    // MARK: - Space Context Verification
    
    /// 獲取 Profile 的 Space ID
    private func getProfileSpaceID(profileName: String) -> Int? {
        let layoutsPath = FileManager.default.homeDirectoryForCurrentUser
            .appendingPathComponent(".hammerspoon/layouts")
        let profilePath = layoutsPath.appendingPathComponent("\(profileName).json")
        
        guard let data = try? Data(contentsOf: profilePath),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            return nil
        }
        
        return json["spaceID"] as? Int
    }
    
    /// 判斷是否需要顯示跨 Space 還原確認
    private func shouldShowCrossSpaceConfirmation(currentSpaceID: Int?, profileSpaceID: Int?) -> Bool {
        // 如果任一方沒有 Space 資訊，不需要確認
        guard let currentSpaceID = currentSpaceID,
              let profileSpaceID = profileSpaceID else {
            return false
        }
        
        // 如果 Space 不同，需要確認
        return currentSpaceID != profileSpaceID
    }
    
    /// 顯示跨 Space 還原確認對話框
    private func showCrossSpaceRestoreConfirmation(profileName: String, currentSpaceID: Int?, profileSpaceID: Int?) {
        let currentSpaceName = currentSpaceID != nil ? "Space \(currentSpaceID!)" : "未知 Space"
        let profileSpaceName = profileSpaceID != nil ? "Space \(profileSpaceID!)" : "未知 Space"
        
        DispatchQueue.main.async {
            let alert = NSAlert()
            alert.messageText = "跨 Space 還原確認"
            alert.informativeText = """
            您正在嘗試從 \(profileSpaceName) 還原 Profile「\(profileName)」到當前的 \(currentSpaceName)。
            
            這可能會導致視窗佈局不如預期，因為不同 Space 中的應用程式和視窗可能不同。
            
            您確定要繼續嗎？
            """
            alert.addButton(withTitle: "繼續還原")
            alert.addButton(withTitle: "取消")
            alert.alertStyle = .warning
            
            let response = alert.runModal()
            if response == .alertFirstButtonReturn {
                // 用戶確認繼續，強制還原
                self.restoreLayout(profileName: profileName, forceRestore: true)
            }
        }
    }
    
    /// 驗證 Profile 是否屬於當前 Space
    func validateProfileSpaceContext(profileName: String) -> Bool {
        let currentSpaceID = spaceDetector.getCurrentSpace()
        let profileSpaceID = getProfileSpaceID(profileName: profileName)
        
        // 如果沒有 Space 資訊，視為有效
        guard let currentSpaceID = currentSpaceID,
              let profileSpaceID = profileSpaceID else {
            return true
        }
        
        return currentSpaceID == profileSpaceID
    }

    // MARK: - Keyboard Shortcuts

    func getKeyboardShortcuts() -> [KeyboardShortcut] {
        // 從 init.lua 解析快捷鍵配置
        return [
            // 傳統全域佈局
            KeyboardShortcut(profile: "General", saveKeys: "⌘⌥1", restoreKeys: "⌘⌥⇧1"),
            KeyboardShortcut(profile: "Coding", saveKeys: "⌘⌥2", restoreKeys: "⌘⌥⇧2"),
            KeyboardShortcut(profile: "Comms", saveKeys: "⌘⌥3", restoreKeys: "⌘⌥⇧3"),
            KeyboardShortcut(profile: "Profile4", saveKeys: "⌘⌥4", restoreKeys: "⌘⌥⇧4"),
            KeyboardShortcut(profile: "Profile5", saveKeys: "⌘⌥5", restoreKeys: "⌘⌥⇧5"),

            // Space-specific 佈局
            KeyboardShortcut(profile: "SpaceProfile6", saveKeys: "⌘⌥6", restoreKeys: "⌘⌥⇧6"),
            KeyboardShortcut(profile: "SpaceProfile7", saveKeys: "⌘⌥7", restoreKeys: "⌘⌥⇧7"),
            KeyboardShortcut(profile: "SpaceProfile8", saveKeys: "⌘⌥8", restoreKeys: "⌘⌥⇧8"),
            KeyboardShortcut(profile: "SpaceProfile9", saveKeys: "⌘⌥9", restoreKeys: "⌘⌥⇧9"),
            KeyboardShortcut(profile: "SpaceProfile0", saveKeys: "⌘⌥0", restoreKeys: "⌘⌥⇧0")
        ]
    }

    func getSpaceKeyboardShortcuts() -> [KeyboardShortcut] {
        // 專門返回 Space-specific 快捷鍵
        return [
            KeyboardShortcut(profile: "SpaceProfile6", saveKeys: "⌘⌥6", restoreKeys: "⌘⌥⇧6"),
            KeyboardShortcut(profile: "SpaceProfile7", saveKeys: "⌘⌥7", restoreKeys: "⌘⌥⇧7"),
            KeyboardShortcut(profile: "SpaceProfile8", saveKeys: "⌘⌥8", restoreKeys: "⌘⌥⇧8"),
            KeyboardShortcut(profile: "SpaceProfile9", saveKeys: "⌘⌥9", restoreKeys: "⌘⌥⇧9"),
            KeyboardShortcut(profile: "SpaceProfile0", saveKeys: "⌘⌥0", restoreKeys: "⌘⌥⇧0")
        ]
    }
}

// MARK: - Supporting Types

struct KeyboardShortcut {
    let profile: String
    let saveKeys: String
    let restoreKeys: String
}