import SwiftUI

extension Font {
    // 增大的系統字體
    static let largeTitle = Font.system(size: 40, weight: .bold)
    static let title = Font.system(size: 32, weight: .bold)
    static let title2 = Font.system(size: 26, weight: .bold)
    static let title3 = Font.system(size: 22, weight: .semibold)
    static let headline = Font.system(size: 20, weight: .semibold)
    static let subheadline = Font.system(size: 18, weight: .medium)
    static let body = Font.system(size: 16, weight: .regular)
    static let callout = Font.system(size: 15, weight: .regular)
    static let footnote = Font.system(size: 14, weight: .regular)
    static let caption = Font.system(size: 13, weight: .regular)
    static let caption2 = Font.system(size: 12, weight: .regular)
    
    // 自定義大字體
    static func customLarge(_ size: CGFloat, weight: Font.Weight = .regular) -> Font {
        return Font.system(size: size + 4, weight: weight)
    }
}
