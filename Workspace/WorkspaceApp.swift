import SwiftUI
import AppKit

@main
struct WorkspaceApp: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    var body: some Scene {
        Settings {
            EmptyView()
        }
    }
}

class AppDelegate: NSObject, NSApplicationDelegate {
    private var statusItem: NSStatusItem?
    private var popover: NSPopover?

    func applicationDidFinishLaunching(_ notification: Notification) {
        // 隱藏 Dock 圖示，只在選單列顯示
        NSApp.setActivationPolicy(.accessory)

        setupMenuBar()

        // 檢查是否首次啟動
        let hasCompletedSetup = UserDefaults.standard.bool(forKey: "hasCompletedSetup")
        if !hasCompletedSetup {
            showWelcomeWindow()
        } else {
            checkHammerspoonInstallation()
        }
    }

    private func setupMenuBar() {
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.variableLength)

        if let button = statusItem?.button {
            // 設定選單列圖示
            if let image = NSImage(systemSymbolName: "rectangle.3.group", accessibilityDescription: "Workspace") {
                image.size = NSSize(width: 16, height: 16)
                button.image = image
            }

            button.action = #selector(togglePopover)
            button.target = self

            // 設定右鍵選單
            let menu = NSMenu()
            menu.addItem(NSMenuItem(title: "關於 Workspace", action: #selector(showAbout), keyEquivalent: ""))
            menu.addItem(NSMenuItem.separator())
            menu.addItem(NSMenuItem(title: "檢查更新", action: #selector(checkForUpdates), keyEquivalent: ""))
            menu.addItem(NSMenuItem.separator())
            menu.addItem(NSMenuItem(title: "結束應用", action: #selector(quitApp), keyEquivalent: "q"))

            button.sendAction(on: [.leftMouseUp, .rightMouseUp])
        }

        // 延遲設定 Popover，避免在應用程式啟動期間立即初始化 SwiftUI 視圖
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.setupPopover()
        }
    }

    private func setupPopover() {
        popover = NSPopover()
        popover?.contentSize = NSSize(width: 320, height: 500)
        popover?.behavior = .transient
        popover?.contentViewController = NSHostingController(rootView: MainMenuView())
    }

    @objc private func togglePopover() {
        guard let button = statusItem?.button else { return }

        // 檢查是否為右鍵點擊
        if let event = NSApp.currentEvent, event.type == .rightMouseUp {
            showContextMenu()
            return
        }

        // 如果 popover 還沒有初始化，先初始化它
        if popover == nil {
            setupPopover()
        }

        if let popover = popover {
            if popover.isShown {
                popover.performClose(nil)
            } else {
                popover.show(relativeTo: button.bounds, of: button, preferredEdge: .minY)
                // 確保 popover 獲得焦點
                popover.contentViewController?.view.window?.makeKey()
            }
        }
    }

    private func showContextMenu() {
        guard statusItem?.button != nil else { return }

        let menu = NSMenu()
        menu.addItem(NSMenuItem(title: "關於 Workspace", action: #selector(showAbout), keyEquivalent: ""))
        menu.addItem(NSMenuItem.separator())
        menu.addItem(NSMenuItem(title: "檢查更新", action: #selector(checkForUpdates), keyEquivalent: ""))
        menu.addItem(NSMenuItem.separator())
        menu.addItem(NSMenuItem(title: "結束應用", action: #selector(quitApp), keyEquivalent: "q"))

        statusItem?.menu = menu
        statusItem?.button?.performClick(nil)
        statusItem?.menu = nil
    }

    @objc private func showAbout() {
        let alert = NSAlert()
        alert.messageText = "Workspace for macOS"
        alert.informativeText = """
        版本 1.0

        一個優雅的 macOS 視窗佈局管理工具，為專業人士和高級用戶提供直觀的圖形化介面來管理基於 Hammerspoon 的多個視窗佈局 Profile。

        © 2024 Workspace Team
        """
        alert.alertStyle = .informational
        alert.addButton(withTitle: "確定")
        alert.runModal()
    }

    @objc private func checkForUpdates() {
        let alert = NSAlert()
        alert.messageText = "檢查更新"
        alert.informativeText = "您正在使用最新版本的 Workspace。"
        alert.alertStyle = .informational
        alert.addButton(withTitle: "確定")
        alert.runModal()
    }

    @objc private func quitApp() {
        NSApplication.shared.terminate(nil)
    }

    private func showWelcomeWindow() {
        let welcomeWindow = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 400, height: 350),
            styleMask: [.titled, .closable],
            backing: .buffered,
            defer: false
        )

        welcomeWindow.title = "Workspace 設定"
        welcomeWindow.contentView = NSHostingView(rootView: WelcomeView())
        welcomeWindow.center()
        welcomeWindow.makeKeyAndOrderFront(nil)

        // 確保視窗在最前面
        NSApp.activate(ignoringOtherApps: true)
    }

    private func checkHammerspoonInstallation() {
        let hammerspoonManager = HammerspoonManager.shared
        hammerspoonManager.checkInstallation()

        if !hammerspoonManager.isInstalled {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                self.showHammerspoonNotInstalledAlert()
            }
        }
    }

    private func showHammerspoonNotInstalledAlert() {
        let alert = NSAlert()
        alert.messageText = "Hammerspoon 未安裝"
        alert.informativeText = "Workspace 需要 Hammerspoon 才能正常運作。請安裝 Hammerspoon 後重新啟動應用程式。"
        alert.alertStyle = .warning
        alert.addButton(withTitle: "下載 Hammerspoon")
        alert.addButton(withTitle: "稍後提醒")

        let response = alert.runModal()
        if response == .alertFirstButtonReturn {
            HammerspoonManager.shared.openHammerspoonWebsite()
        }
    }
}