
import Foundation
import SwiftUI

// MARK: - GridLayoutCalculator

class GridLayoutCalculator {
    struct GridConfiguration {
        let columns: [GridItem]
        let itemSize: CGSize
    }

    static func calculateLayout(
        for windowCount: Int,
        in canvasSize: CGSize,
        minItemSize: CGSize = CGSize(width: 120, height: 90),
        maxItemSize: CGSize = CGSize(width: 240, height: 180),
        spacing: CGFloat = 16
    ) -> GridConfiguration {
        guard windowCount > 0, canvasSize.width > 0, canvasSize.height > 0 else {
            return GridConfiguration(columns: [GridItem(.flexible())], itemSize: minItemSize)
        }

        let bestLayout = findBestLayout(
            windowCount: windowCount,
            canvasSize: canvasSize,
            minItemSize: minItemSize,
            maxItemSize: maxItemSize,
            spacing: spacing
        )

        let columns = Array(repeating: GridItem(.fixed(bestLayout.itemSize.width), spacing: spacing), count: bestLayout.columns)
        return GridConfiguration(columns: columns, itemSize: bestLayout.itemSize)
    }

    private static func findBestLayout(
        windowCount: Int,
        canvasSize: CGSize,
        minItemSize: CGSize,
        maxItemSize: CGSize,
        spacing: CGFloat
    ) -> (columns: Int, itemSize: CGSize) {
        var bestLayout: (columns: Int, itemSize: CGSize) = (1, minItemSize)
        var maxDensity: CGFloat = 0

        for columns in 1...windowCount {
            let availableWidth = canvasSize.width - (CGFloat(columns) + 1) * spacing
            let itemWidth = availableWidth / CGFloat(columns)

            if itemWidth < minItemSize.width { continue }
            if itemWidth > maxItemSize.width {
                let clampedWidth = maxItemSize.width
                let itemHeight = clampedWidth * (minItemSize.height / minItemSize.width)
                let density = calculateDensity(
                    itemSize: CGSize(width: clampedWidth, height: itemHeight),
                    columns: columns,
                    windowCount: windowCount,
                    canvasSize: canvasSize
                )
                if density > maxDensity {
                    maxDensity = density
                    bestLayout = (columns, CGSize(width: clampedWidth, height: itemHeight))
                }
                continue
            }

            let itemHeight = itemWidth * (minItemSize.height / minItemSize.width)
            if itemHeight < minItemSize.height { continue }
            if itemHeight > maxItemSize.height { continue }

            let rows = ceil(CGFloat(windowCount) / CGFloat(columns))
            let totalHeight = rows * itemHeight + (rows + 1) * spacing
            
            if totalHeight > canvasSize.height { // Requires scrolling
                 let density = calculateDensity(itemSize: CGSize(width: itemWidth, height: itemHeight), columns: columns, windowCount: windowCount, canvasSize: canvasSize)
                 if density > maxDensity {
                    maxDensity = density
                    bestLayout = (columns, CGSize(width: itemWidth, height: itemHeight))
                }
            } else { // Fits without scrolling
                let density = calculateDensity(itemSize: CGSize(width: itemWidth, height: itemHeight), columns: columns, windowCount: windowCount, canvasSize: canvasSize)
                if density > maxDensity {
                    maxDensity = density
                    bestLayout = (columns, CGSize(width: itemWidth, height: itemHeight))
                }
            }
        }
        return bestLayout
    }
    
    private static func calculateDensity(itemSize: CGSize, columns: Int, windowCount: Int, canvasSize: CGSize) -> CGFloat {
        let areaPerItem = itemSize.width * itemSize.height
        let totalItemArea = areaPerItem * CGFloat(windowCount)
        let density = totalItemArea / (canvasSize.width * canvasSize.height)
        return density
    }
}
