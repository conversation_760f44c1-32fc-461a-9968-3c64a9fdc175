import SwiftUI
import Combine

@MainActor
class PreviewStateManager: ObservableObject {
    @Published var currentMode: PreviewMode = .scaled
    @Published var selectedWindow: WindowLayout?
    @Published var windows: [WindowLayout] = []
    
    func setup(with windows: [WindowLayout], configuration: PreviewConfiguration) {
        self.windows = windows
    }
    
    func switchMode(to mode: PreviewMode, animated: Bool) {
        if animated {
            withAnimation(.easeInOut(duration: 0.3)) {
                currentMode = mode
            }
        } else {
            currentMode = mode
        }
    }
    
    func selectWindow(_ window: WindowLayout) {
        selectedWindow = window
    }
    
    func updateWindows(_ windows: [WindowLayout]) {
        self.windows = windows
    }
}