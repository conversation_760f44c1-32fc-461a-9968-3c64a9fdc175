import Foundation
import SwiftUI

// MARK: - MemoryOptimizer
/// 記憶體優化工具，實現延遲載入和記憶體管理
@MainActor
class MemoryOptimizer: ObservableObject {
    
    // MARK: - Configuration
    struct OptimizationConfig {
        let enableLazyLoading: Bool
        let maxCachedItems: Int
        let memoryWarningThreshold: Double // MB
        let cleanupInterval: TimeInterval
        
        static let `default` = OptimizationConfig(
            enableLazyLoading: true,
            maxCachedItems: 100,
            memoryWarningThreshold: 200.0,
            cleanupInterval: 30.0
        )
    }
    
    // MARK: - Lazy Loading Cache
    private var lazyLoadCache: [String: Any] = [:]
    private var loadingTasks: [String: Task<Any, Error>] = [:]
    private let cacheQueue = DispatchQueue(label: "memory.optimizer.cache", qos: .utility)
    
    // MARK: - Memory Monitoring
    @Published var currentMemoryUsage: Double = 0.0
    @Published var isMemoryPressureHigh: Bool = false
    
    private let config: OptimizationConfig
    private var cleanupTimer: Timer?
    private var memoryMonitorTimer: Timer?
    
    init(config: OptimizationConfig = .default) {
        self.config = config
        setupMemoryMonitoring()
        setupCleanupTimer()
    }
    
    deinit {
        cleanupTimer?.invalidate()
        memoryMonitorTimer?.invalidate()
        
        // 取消所有載入任務
        for (_, task) in loadingTasks {
            task.cancel()
        }
    }
    
    // MARK: - Lazy Loading
    
    /// 延遲載入項目
    func lazyLoad<T>(
        key: String,
        loader: @escaping () async throws -> T
    ) async throws -> T {
        // 檢查快取
        if let cachedItem = getCachedItem(key: key) as? T {
            return cachedItem
        }
        
        // 檢查是否已經在載入中
        if loadingTasks[key] != nil {
            // 等待現有任務完成，然後重新嘗試
            try await Task.sleep(nanoseconds: 10_000_000) // 10ms
            return try await self.lazyLoad(key: key, loader: loader)
        }
        
        // 創建新的載入任務
        let task = Task<T, Error> {
            let result = try await loader()
            
            // 快取結果
            await MainActor.run {
                self.setCachedItem(key: key, item: result)
                self.loadingTasks.removeValue(forKey: key)
            }
            
            return result
        }
        
        loadingTasks[key] = Task<Any, Error> {
            return try await task.value
        }
        
        do {
            let result = try await task.value
            return result
        } catch {
            loadingTasks.removeValue(forKey: key)
            throw error
        }
    }
    
    /// 預載入項目
    func preload<T>(
        key: String,
        loader: @escaping () async throws -> T
    ) {
        guard getCachedItem(key: key) == nil else { return }
        
        Task {
            do {
                _ = try await lazyLoad(key: key, loader: loader)
            } catch {
                // 預載入失敗不影響主流程
                print("預載入失敗: \(key) - \(error)")
            }
        }
    }
    
    // MARK: - Cache Management
    
    private func getCachedItem(key: String) -> Any? {
        return cacheQueue.sync {
            return lazyLoadCache[key]
        }
    }
    
    private func setCachedItem(key: String, item: Any) {
        cacheQueue.async { [weak self] in
            guard let self = self else { return }
            
            self.lazyLoadCache[key] = item
            
            // 檢查快取大小限制
            if self.lazyLoadCache.count > self.config.maxCachedItems {
                self.evictOldestItems()
            }
        }
    }
    
    private func evictOldestItems() {
        let targetSize = config.maxCachedItems * 3 / 4 // 清理到75%
        let itemsToRemove = lazyLoadCache.count - targetSize
        
        guard itemsToRemove > 0 else { return }
        
        // 簡單的FIFO清理策略
        let keysToRemove = Array(lazyLoadCache.keys.prefix(itemsToRemove))
        for key in keysToRemove {
            lazyLoadCache.removeValue(forKey: key)
        }
    }
    
    /// 清理快取
    func clearCache() {
        cacheQueue.async { [weak self] in
            self?.lazyLoadCache.removeAll()
        }
        
        // 取消所有載入任務
        for (_, task) in loadingTasks {
            task.cancel()
        }
        loadingTasks.removeAll()
    }
    
    /// 清理特定前綴的快取
    func clearCache(withPrefix prefix: String) {
        cacheQueue.async { [weak self] in
            guard let self = self else { return }
            
            let keysToRemove = self.lazyLoadCache.keys.filter { $0.hasPrefix(prefix) }
            for key in keysToRemove {
                self.lazyLoadCache.removeValue(forKey: key)
            }
        }
    }
    
    // MARK: - Memory Monitoring
    
    private func setupMemoryMonitoring() {
        memoryMonitorTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            self?.updateMemoryUsage()
        }
    }
    
    private func updateMemoryUsage() {
        let memoryUsage = getMemoryUsage()
        
        DispatchQueue.main.async {
            self.currentMemoryUsage = memoryUsage
            self.isMemoryPressureHigh = memoryUsage > self.config.memoryWarningThreshold
            
            // 如果記憶體壓力過高，執行清理
            if self.isMemoryPressureHigh {
                self.performEmergencyCleanup()
            }
        }
    }
    
    private func getMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / 1024.0 / 1024.0 // 轉換為MB
        }
        
        return 0.0
    }
    
    private func performEmergencyCleanup() {
        // 清理一半的快取
        cacheQueue.async { [weak self] in
            guard let self = self else { return }
            
            let targetSize = self.lazyLoadCache.count / 2
            let keysToRemove = Array(self.lazyLoadCache.keys.prefix(self.lazyLoadCache.count - targetSize))
            
            for key in keysToRemove {
                self.lazyLoadCache.removeValue(forKey: key)
            }
        }
        
        // 取消非關鍵載入任務
        let nonCriticalTasks = loadingTasks.filter { !$0.key.contains("critical") }
        for (key, task) in nonCriticalTasks {
            task.cancel()
            loadingTasks.removeValue(forKey: key)
        }
    }
    
    // MARK: - Cleanup Timer
    
    private func setupCleanupTimer() {
        cleanupTimer = Timer.scheduledTimer(withTimeInterval: config.cleanupInterval, repeats: true) { [weak self] _ in
            self?.performRoutineCleanup()
        }
    }
    
    private func performRoutineCleanup() {
        // 清理過期的載入任務
        let completedTasks = loadingTasks.filter { $0.value.isCancelled }
        for (key, _) in completedTasks {
            loadingTasks.removeValue(forKey: key)
        }
        
        // 如果快取過大，執行清理
        if lazyLoadCache.count > config.maxCachedItems {
            evictOldestItems()
        }
    }
    
    // MARK: - Public Methods
    
    /// 獲取快取統計信息
    func getCacheStats() -> CacheStats {
        return cacheQueue.sync {
            return CacheStats(
                cachedItemCount: lazyLoadCache.count,
                loadingTaskCount: loadingTasks.count,
                memoryUsage: currentMemoryUsage,
                isMemoryPressureHigh: isMemoryPressureHigh
            )
        }
    }
    
    /// 檢查是否應該延遲載入
    func shouldLazyLoad(itemCount: Int) -> Bool {
        return config.enableLazyLoading && (itemCount > 20 || isMemoryPressureHigh)
    }
}

// MARK: - CacheStats
struct CacheStats {
    let cachedItemCount: Int
    let loadingTaskCount: Int
    let memoryUsage: Double
    let isMemoryPressureHigh: Bool
    
    var description: String {
        return """
        快取統計:
        - 快取項目數: \(cachedItemCount)
        - 載入任務數: \(loadingTaskCount)
        - 記憶體使用: \(String(format: "%.1f", memoryUsage)) MB
        - 記憶體壓力: \(isMemoryPressureHigh ? "高" : "正常")
        """
    }
}

// MARK: - LazyLoadingView
/// 支援延遲載入的視圖包裝器
struct LazyLoadingView<Content: View, PlaceholderContent: View>: View {
    let loadKey: String
    let loader: () async throws -> Content
    let placeholder: () -> PlaceholderContent
    
    @StateObject private var memoryOptimizer = MemoryOptimizer()
    @State private var loadedContent: Content?
    @State private var isLoading: Bool = false
    @State private var loadError: Error?
    
    init(
        key: String,
        @ViewBuilder content: @escaping () async throws -> Content,
        @ViewBuilder placeholder: @escaping () -> PlaceholderContent
    ) {
        self.loadKey = key
        self.loader = content
        self.placeholder = placeholder
    }
    
    var body: some View {
        Group {
            if let content = loadedContent {
                content
            } else if isLoading {
                placeholder()
                    .overlay(
                        ProgressView()
                            .scaleEffect(0.8)
                    )
            } else if loadError != nil {
                placeholder()
                    .overlay(
                        VStack {
                            Image(systemName: "exclamationmark.triangle")
                                .foregroundColor(.orange)
                            Text("載入失敗")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    )
            } else {
                placeholder()
            }
        }
        .onAppear {
            loadContent()
        }
    }
    
    private func loadContent() {
        guard loadedContent == nil && !isLoading else { return }
        
        isLoading = true
        loadError = nil
        
        Task {
            do {
                let content = try await memoryOptimizer.lazyLoad(key: loadKey, loader: loader)
                
                await MainActor.run {
                    self.loadedContent = content
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.loadError = error
                    self.isLoading = false
                }
            }
        }
    }
}

// MARK: - Memory Optimization Extensions
extension View {
    /// 添加延遲載入支援
    func lazyLoad<PlaceholderContent: View>(
        key: String,
        @ViewBuilder placeholder: @escaping () -> PlaceholderContent
    ) -> some View {
        LazyLoadingView(
            key: key,
            content: { self },
            placeholder: placeholder
        )
    }
}