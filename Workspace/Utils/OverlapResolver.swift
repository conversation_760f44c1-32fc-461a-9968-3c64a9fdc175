import Foundation
import SwiftUI

/// OverlapResolver 工具類，負責檢測和處理視窗重疊問題
/// 提供智能的重疊檢測、層級計算和位置調整算法
struct OverlapResolver {
    
    // MARK: - 配置常數
    
    /// 重疊檢測的最小閾值（重疊面積百分比）
    private static let overlapThreshold: CGFloat = 0.1
    
    /// 最大透明度調整範圍
    private static let minOpacity: Double = 0.3
    private static let maxOpacity: Double = 1.0
    
    /// 位置調整的最大偏移量
    private static let maxPositionOffset: CGFloat = 20.0
    
    /// 層級計算的權重因子
    private static let zIndexMultiplier: Int = 10
    
    // MARK: - 主要公共方法
    
    /// 解析視窗重疊問題，返回處理後的視窗顯示信息
    /// - Parameters:
    ///   - windows: 原始視窗佈局數組
    ///   - bounds: 預覽區域邊界
    ///   - scaleFactor: 縮放因子
    /// - Returns: 處理後的視窗顯示信息數組
    static func resolveOverlaps(
        windows: [WindowLayout],
        in bounds: CGRect,
        scaleFactor: CGFloat = 1.0
    ) -> [WindowDisplayInfo] {
        
        guard !windows.isEmpty else { return [] }
        
        // 1. 創建初始顯示信息
        var displayInfos = createInitialDisplayInfos(windows: windows, bounds: bounds, scaleFactor: scaleFactor)
        
        // 2. 檢測重疊關係
        let overlapGroups = detectOverlapGroups(displayInfos: displayInfos)
        
        // 3. 計算重疊層級
        displayInfos = calculateOverlapLevels(displayInfos: displayInfos, overlapGroups: overlapGroups)
        
        // 4. 調整位置以減少重疊
        displayInfos = adjustPositionsForOverlap(displayInfos: displayInfos, bounds: bounds)
        
        // 5. 計算動態透明度
        displayInfos = calculateDynamicOpacity(displayInfos: displayInfos)
        
        // 6. 分配 Z-Index
        displayInfos = assignZIndices(displayInfos: displayInfos)
        
        return displayInfos
    }
    
    // MARK: - 私有輔助方法
    
    /// 創建初始的視窗顯示信息
    private static func createInitialDisplayInfos(
        windows: [WindowLayout],
        bounds: CGRect,
        scaleFactor: CGFloat
    ) -> [WindowDisplayInfo] {
        
        return windows.map { window in
            let displayFrame = calculateDisplayFrame(
                window: window,
                bounds: bounds,
                scaleFactor: scaleFactor
            )
            
            return WindowDisplayInfo(
                window: window,
                displayFrame: displayFrame,
                isOverlapping: false,
                overlapLevel: 0,
                isVisible: bounds.intersects(displayFrame),
                opacity: 1.0,
                zIndex: 0
            )
        }
    }
    
    /// 計算視窗在預覽區域中的顯示框架
    private static func calculateDisplayFrame(
        window: WindowLayout,
        bounds: CGRect,
        scaleFactor: CGFloat
    ) -> CGRect {
        
        let originalFrame = CGRect(
            x: window.frame.x,
            y: window.frame.y,
            width: window.frame.w,
            height: window.frame.h
        )
        
        // 應用縮放因子
        let scaledFrame = CGRect(
            x: originalFrame.origin.x * scaleFactor,
            y: originalFrame.origin.y * scaleFactor,
            width: originalFrame.width * scaleFactor,
            height: originalFrame.height * scaleFactor
        )
        
        // 確保框架在預覽邊界內
        return scaledFrame.intersection(bounds.insetBy(dx: -bounds.width, dy: -bounds.height))
    }
    
    /// 檢測重疊組群
    private static func detectOverlapGroups(displayInfos: [WindowDisplayInfo]) -> [[Int]] {
        var visited = Set<Int>()
        var groups: [[Int]] = []
        
        for i in 0..<displayInfos.count {
            if visited.contains(i) { continue }
            
            var currentGroup: [Int] = []
            var stack = [i]
            
            while !stack.isEmpty {
                let current = stack.removeLast()
                if visited.contains(current) { continue }
                
                visited.insert(current)
                currentGroup.append(current)
                
                // 查找與當前視窗重疊的其他視窗
                for j in 0..<displayInfos.count {
                    if !visited.contains(j) && hasSignificantOverlap(
                        displayInfos[current].displayFrame,
                        displayInfos[j].displayFrame
                    ) {
                        stack.append(j)
                    }
                }
            }
            
            if currentGroup.count > 1 {
                groups.append(currentGroup)
            }
        }
        
        return groups
    }
    
    /// 檢查兩個框架是否有顯著重疊
    private static func hasSignificantOverlap(_ frame1: CGRect, _ frame2: CGRect) -> Bool {
        let intersection = frame1.intersection(frame2)
        let intersectionArea = intersection.width * intersection.height
        
        let frame1Area = frame1.width * frame1.height
        let frame2Area = frame2.width * frame2.height
        let minArea = min(frame1Area, frame2Area)
        
        return minArea > 0 && (intersectionArea / minArea) > overlapThreshold
    }
    
    /// 計算重疊層級
    private static func calculateOverlapLevels(
        displayInfos: [WindowDisplayInfo],
        overlapGroups: [[Int]]
    ) -> [WindowDisplayInfo] {
        
        var updatedInfos = displayInfos
        
        for group in overlapGroups {
            // 按視窗面積排序，較大的視窗獲得較低的層級（更靠後）
            let sortedIndices = group.sorted { i, j in
                let area1 = displayInfos[i].displayFrame.width * displayInfos[i].displayFrame.height
                let area2 = displayInfos[j].displayFrame.width * displayInfos[j].displayFrame.height
                return area1 > area2
            }
            
            for (level, index) in sortedIndices.enumerated() {
                let originalInfo = displayInfos[index]
                updatedInfos[index] = WindowDisplayInfo(
                    window: originalInfo.window,
                    displayFrame: originalInfo.displayFrame,
                    isOverlapping: true,
                    overlapLevel: level,
                    isVisible: originalInfo.isVisible,
                    opacity: originalInfo.opacity,
                    zIndex: originalInfo.zIndex
                )
            }
        }
        
        return updatedInfos
    }
    
    /// 調整位置以減少重疊
    private static func adjustPositionsForOverlap(
        displayInfos: [WindowDisplayInfo],
        bounds: CGRect
    ) -> [WindowDisplayInfo] {
        
        var updatedInfos = displayInfos
        
        for i in 0..<updatedInfos.count {
            if !updatedInfos[i].isOverlapping { continue }
            
            let originalFrame = updatedInfos[i].displayFrame
            var bestFrame = originalFrame
            var minOverlapArea: CGFloat = CGFloat.greatestFiniteMagnitude
            
            // 嘗試不同的位置偏移
            let offsets: [CGPoint] = [
                CGPoint(x: maxPositionOffset, y: 0),
                CGPoint(x: -maxPositionOffset, y: 0),
                CGPoint(x: 0, y: maxPositionOffset),
                CGPoint(x: 0, y: -maxPositionOffset),
                CGPoint(x: maxPositionOffset/2, y: maxPositionOffset/2),
                CGPoint(x: -maxPositionOffset/2, y: -maxPositionOffset/2)
            ]
            
            for offset in offsets {
                let testFrame = CGRect(
                    x: originalFrame.origin.x + offset.x,
                    y: originalFrame.origin.y + offset.y,
                    width: originalFrame.width,
                    height: originalFrame.height
                )
                
                // 確保調整後的框架仍在邊界內
                guard bounds.contains(testFrame) else { continue }
                
                // 計算與其他視窗的總重疊面積
                let totalOverlap = calculateTotalOverlapArea(
                    testFrame: testFrame,
                    withOthers: updatedInfos,
                    excludingIndex: i
                )
                
                if totalOverlap < minOverlapArea {
                    minOverlapArea = totalOverlap
                    bestFrame = testFrame
                }
            }
            
            // 更新顯示框架
            let originalInfo = updatedInfos[i]
            updatedInfos[i] = WindowDisplayInfo(
                window: originalInfo.window,
                displayFrame: bestFrame,
                isOverlapping: originalInfo.isOverlapping,
                overlapLevel: originalInfo.overlapLevel,
                isVisible: bounds.intersects(bestFrame),
                opacity: originalInfo.opacity,
                zIndex: originalInfo.zIndex
            )
        }
        
        return updatedInfos
    }
    
    /// 計算測試框架與其他視窗的總重疊面積
    private static func calculateTotalOverlapArea(
        testFrame: CGRect,
        withOthers displayInfos: [WindowDisplayInfo],
        excludingIndex: Int
    ) -> CGFloat {
        
        var totalArea: CGFloat = 0
        
        for (index, info) in displayInfos.enumerated() {
            if index == excludingIndex { continue }
            
            let intersection = testFrame.intersection(info.displayFrame)
            totalArea += intersection.width * intersection.height
        }
        
        return totalArea
    }
    
    /// 計算動態透明度
    private static func calculateDynamicOpacity(displayInfos: [WindowDisplayInfo]) -> [WindowDisplayInfo] {
        
        return displayInfos.map { info in
            let opacity: Double
            
            if info.isOverlapping {
                // 根據重疊層級計算透明度
                let levelFactor = Double(info.overlapLevel) / 5.0 // 假設最多5層重疊
                let adjustedFactor = min(levelFactor, 1.0)
                opacity = maxOpacity - (maxOpacity - minOpacity) * adjustedFactor
            } else {
                opacity = maxOpacity
            }
            
            return WindowDisplayInfo(
                window: info.window,
                displayFrame: info.displayFrame,
                isOverlapping: info.isOverlapping,
                overlapLevel: info.overlapLevel,
                isVisible: info.isVisible,
                opacity: opacity,
                zIndex: info.zIndex
            )
        }
    }
    
    /// 分配 Z-Index
    private static func assignZIndices(displayInfos: [WindowDisplayInfo]) -> [WindowDisplayInfo] {
        
        return displayInfos.map { info in
            let zIndex: Int
            
            if info.isOverlapping {
                // 重疊層級越高，Z-Index 越大（顯示在前面）
                zIndex = info.overlapLevel * zIndexMultiplier
            } else {
                zIndex = 0
            }
            
            return WindowDisplayInfo(
                window: info.window,
                displayFrame: info.displayFrame,
                isOverlapping: info.isOverlapping,
                overlapLevel: info.overlapLevel,
                isVisible: info.isVisible,
                opacity: info.opacity,
                zIndex: zIndex
            )
        }
    }
}

// MARK: - 擴展方法

extension OverlapResolver {
    
    /// 快速重疊檢測，用於性能敏感的場景
    /// - Parameters:
    ///   - windows: 視窗佈局數組
    ///   - bounds: 預覽區域邊界
    /// - Returns: 簡化的重疊檢測結果
    static func quickOverlapDetection(
        windows: [WindowLayout],
        in bounds: CGRect
    ) -> [Bool] {
        
        let frames = windows.map { window in
            CGRect(x: window.frame.x, y: window.frame.y, width: window.frame.w, height: window.frame.h)
        }
        
        return frames.enumerated().map { index, frame in
            for (otherIndex, otherFrame) in frames.enumerated() {
                if index != otherIndex && hasSignificantOverlap(frame, otherFrame) {
                    return true
                }
            }
            return false
        }
    }
    
    /// 計算重疊統計信息
    /// - Parameter displayInfos: 視窗顯示信息數組
    /// - Returns: 重疊統計結果
    static func calculateOverlapStatistics(displayInfos: [WindowDisplayInfo]) -> OverlapStatistics {
        let totalWindows = displayInfos.count
        let overlappingWindows = displayInfos.filter { $0.isOverlapping }.count
        let maxOverlapLevel = displayInfos.map { $0.overlapLevel }.max() ?? 0
        let averageOpacity = displayInfos.map { $0.opacity }.reduce(0, +) / Double(totalWindows)
        
        return OverlapStatistics(
            totalWindows: totalWindows,
            overlappingWindows: overlappingWindows,
            overlapPercentage: totalWindows > 0 ? Double(overlappingWindows) / Double(totalWindows) : 0,
            maxOverlapLevel: maxOverlapLevel,
            averageOpacity: averageOpacity
        )
    }
}

// MARK: - 輔助數據結構

/// 重疊統計信息
struct OverlapStatistics {
    let totalWindows: Int
    let overlappingWindows: Int
    let overlapPercentage: Double
    let maxOverlapLevel: Int
    let averageOpacity: Double
    
    var description: String {
        return """
        總視窗數: \(totalWindows)
        重疊視窗數: \(overlappingWindows)
        重疊百分比: \(String(format: "%.1f", overlapPercentage * 100))%
        最大重疊層級: \(maxOverlapLevel)
        平均透明度: \(String(format: "%.2f", averageOpacity))
        """
    }
}