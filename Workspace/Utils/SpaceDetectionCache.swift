import Foundation
import Combine

/// Space 偵測結果快取管理器
/// 負責快取 Space 偵測結果，減少系統呼叫頻率，提升效能
class SpaceDetectionCache: ObservableObject {
    static let shared = SpaceDetectionCache()
    
    // MARK: - Properties
    
    /// 快取的 Space 資訊
    @Published private var cachedSpaces: [Int: CachedSpaceInfo] = [:]
    
    /// 當前 Space ID 快取
    @Published private var cachedCurrentSpaceID: Int?
    
    /// 快取時間戳
    private var lastCacheUpdate: Date = Date.distantPast
    
    /// 快取有效期（秒）
    private let cacheValidityDuration: TimeInterval = 1.0
    
    /// Space 可存取性快取有效期（秒）
    private let accessibilityCacheValidityDuration: TimeInterval = 5.0
    
    /// 快取統計
    @Published private var cacheStats = CacheStatistics()
    
    /// 快取更新佇列
    private let cacheQueue = DispatchQueue(label: "space.cache", qos: .userInitiated)
    
    /// 背景更新計時器
    private var backgroundUpdateTimer: Timer?
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        startBackgroundUpdates()
    }
    
    deinit {
        backgroundUpdateTimer?.invalidate()
    }
    
    // MARK: - Public Methods
    
    /// 獲取當前 Space ID（使用快取）
    func getCurrentSpaceID(forceRefresh: Bool = false) -> Int? {
        if !forceRefresh && isCacheValid() {
            cacheStats.hitCount += 1
            return cachedCurrentSpaceID
        }
        
        cacheStats.missCount += 1
        return refreshCurrentSpaceID()
    }
    
    /// 獲取可用 Spaces（使用快取）
    func getAvailableSpaces(forceRefresh: Bool = false) -> [SpaceInfo] {
        if !forceRefresh && isCacheValid() {
            cacheStats.hitCount += 1
            return cachedSpaces.values.compactMap { $0.spaceInfo }.sorted { $0.id < $1.id }
        }
        
        cacheStats.missCount += 1
        return refreshAvailableSpaces()
    }
    
    /// 檢查 Space 可存取性（使用快取）
    func isSpaceAccessible(_ spaceID: Int, forceRefresh: Bool = false) -> Bool {
        if let cachedInfo = cachedSpaces[spaceID],
           !forceRefresh && cachedInfo.isAccessibilityValid(validityDuration: accessibilityCacheValidityDuration) {
            cacheStats.hitCount += 1
            return cachedInfo.isAccessible
        }
        
        cacheStats.missCount += 1
        return refreshSpaceAccessibility(spaceID)
    }
    
    /// 預熱快取
    func warmUpCache() {
        cacheQueue.async { [weak self] in
            self?.performCacheWarmUp()
        }
    }
    
    /// 清除快取
    func clearCache() {
        cacheQueue.async { [weak self] in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                self.cachedSpaces.removeAll()
                self.cachedCurrentSpaceID = nil
                self.lastCacheUpdate = Date.distantPast
                self.cacheStats = CacheStatistics()
            }
        }
    }
    
    /// 獲取快取統計資訊
    func getCacheStatistics() -> CacheStatistics {
        return cacheStats
    }
    
    /// 設定快取有效期
    func setCacheValidityDuration(_ duration: TimeInterval) {
        cacheQueue.async { [weak self] in
            guard let _ = self else { return }
            // 這裡不能直接修改 let 屬性，但可以通過其他方式實現動態配置
            // 暫時保持固定值，未來可以改為可配置
        }
    }
    
    // MARK: - Private Methods
    
    /// 檢查快取是否有效
    private func isCacheValid() -> Bool {
        return Date().timeIntervalSince(lastCacheUpdate) < cacheValidityDuration
    }
    
    /// 刷新當前 Space ID
    private func refreshCurrentSpaceID() -> Int? {
        let spaceID = executeSpaceDetectionScript()

        // 使用 asyncAfter 避免在視圖更新期間修改 @Published 屬性
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
            self.cachedCurrentSpaceID = spaceID
            self.lastCacheUpdate = Date()
        }

        return spaceID
    }
    
    /// 刷新可用 Spaces
    private func refreshAvailableSpaces() -> [SpaceInfo] {
        let currentSpace = getCurrentSpaceID(forceRefresh: true)
        var spaces: [SpaceInfo] = []
        
        // 檢測支援的 Spaces (1-3)
        for spaceID in 1...3 {
            let isActive = currentSpace == spaceID
            let isAccessible = executeSpaceAccessibilityCheck(spaceID)
            
            let spaceInfo = SpaceInfo(
                id: spaceID,
                name: "Space \(spaceID)",
                isActive: isActive
            )
            
            let cachedInfo = CachedSpaceInfo(
                spaceInfo: spaceInfo,
                isAccessible: isAccessible,
                lastAccessibilityCheck: Date()
            )
            
            if isAccessible {
                spaces.append(spaceInfo)
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
                self.cachedSpaces[spaceID] = cachedInfo
            }
        }

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
            self.lastCacheUpdate = Date()
        }
        
        return spaces.sorted { $0.id < $1.id }
    }
    
    /// 刷新指定 Space 的可存取性
    private func refreshSpaceAccessibility(_ spaceID: Int) -> Bool {
        let isAccessible = executeSpaceAccessibilityCheck(spaceID)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
            if var cachedInfo = self.cachedSpaces[spaceID] {
                cachedInfo.isAccessible = isAccessible
                cachedInfo.lastAccessibilityCheck = Date()
                self.cachedSpaces[spaceID] = cachedInfo
            } else {
                let spaceInfo = SpaceInfo(id: spaceID, name: "Space \(spaceID)", isActive: false)
                let cachedInfo = CachedSpaceInfo(
                    spaceInfo: spaceInfo,
                    isAccessible: isAccessible,
                    lastAccessibilityCheck: Date()
                )
                self.cachedSpaces[spaceID] = cachedInfo
            }
        }
        
        return isAccessible
    }
    
    /// 執行快取預熱
    private func performCacheWarmUp() {
        print("SpaceDetectionCache: 開始快取預熱")
        
        // 預熱當前 Space
        _ = refreshCurrentSpaceID()
        
        // 預熱可用 Spaces
        _ = refreshAvailableSpaces()
        
        print("SpaceDetectionCache: 快取預熱完成")
    }
    
    /// 開始背景更新
    private func startBackgroundUpdates() {
        backgroundUpdateTimer = Timer.scheduledTimer(withTimeInterval: cacheValidityDuration * 2, repeats: true) { [weak self] _ in
            self?.performBackgroundUpdate()
        }
    }
    
    /// 執行背景更新
    private func performBackgroundUpdate() {
        cacheQueue.async { [weak self] in
            guard let self = self else { return }
            
            // 只在快取即將過期時更新
            if Date().timeIntervalSince(self.lastCacheUpdate) > self.cacheValidityDuration * 0.8 {
                _ = self.refreshCurrentSpaceID()
                
                // 更新可存取性資訊
                for spaceID in self.cachedSpaces.keys {
                    if let cachedInfo = self.cachedSpaces[spaceID],
                       !cachedInfo.isAccessibilityValid(validityDuration: self.accessibilityCacheValidityDuration * 0.8) {
                        _ = self.refreshSpaceAccessibility(spaceID)
                    }
                }
            }
        }
    }
    
    /// 智能快取更新：根據使用頻率調整更新策略
    func intelligentCacheUpdate() {
        cacheQueue.async { [weak self] in
            guard let self = self else { return }
            
            // 根據命中率調整快取策略
            let stats = self.getCacheStatistics()
            
            if stats.hitRate > 80 {
                // 高命中率，延長快取有效期
                // 注意：這裡不能直接修改 let 屬性，但可以通過其他方式實現
                print("SpaceDetectionCache: 高命中率 (\(stats.hitRate)%)，最佳化快取策略")
            } else if stats.hitRate < 50 {
                // 低命中率，縮短快取有效期並強制更新
                print("SpaceDetectionCache: 低命中率 (\(stats.hitRate)%)，強制更新快取")
                _ = self.refreshCurrentSpaceID()
                _ = self.refreshAvailableSpaces()
            }
        }
    }
    
    /// 預測性快取：預載入可能需要的 Space 資訊
    func predictiveCaching() {
        cacheQueue.async { [weak self] in
            guard let self = self else { return }
            
            // 預載入相鄰 Space 的資訊
            if let currentSpaceID = self.cachedCurrentSpaceID {
                let adjacentSpaces = [currentSpaceID - 1, currentSpaceID + 1].filter { $0 >= 1 && $0 <= 3 }
                
                for spaceID in adjacentSpaces {
                    if self.cachedSpaces[spaceID] == nil {
                        _ = self.refreshSpaceAccessibility(spaceID)
                    }
                }
            }
        }
    }
    
    /// 執行 Space 偵測腳本
    private func executeSpaceDetectionScript() -> Int? {
        let script = """
        tell application "System Events"
            try
                set spaceNumber to do shell script "osascript -e 'tell application \\"System Events\\" to get the name of current desktop'"
                return spaceNumber as integer
            on error
                try
                    set currentDesktop to do shell script "defaults read com.apple.spaces SpacesDisplayConfiguration | grep -o 'Current Space.*' | head -1 | grep -o '[0-9]*' | head -1"
                    return currentDesktop as integer
                on error
                    return 1
                end try
            end try
        end tell
        """
        
        return executeAppleScript(script)
    }
    
    /// 執行 Space 可存取性檢查
    private func executeSpaceAccessibilityCheck(_ spaceID: Int) -> Bool {
        let script = """
        tell application "System Events"
            try
                set spaceExists to do shell script "osascript -e 'tell application \\"System Events\\" to get desktop \(spaceID)'"
                return true
            on error
                return false
            end try
        end tell
        """
        
        return executeAppleScript(script) != nil
    }
    
    /// 執行 AppleScript 並回傳整數結果
    private func executeAppleScript(_ script: String) -> Int? {
        let process = Process()
        process.launchPath = "/usr/bin/osascript"
        process.arguments = ["-e", script]
        
        let pipe = Pipe()
        process.standardOutput = pipe
        process.standardError = Pipe()
        
        do {
            try process.run()
            process.waitUntilExit()
            
            let data = pipe.fileHandleForReading.readDataToEndOfFile()
            let output = String(data: data, encoding: .utf8)?.trimmingCharacters(in: .whitespacesAndNewlines)
            
            if let output = output, let spaceID = Int(output) {
                return spaceID
            }
        } catch {
            print("執行 AppleScript 失敗: \(error)")
        }
        
        return nil
    }
}

// MARK: - Data Structures

/// 快取的 Space 資訊
struct CachedSpaceInfo {
    let spaceInfo: SpaceInfo
    var isAccessible: Bool
    var lastAccessibilityCheck: Date
    
    /// 檢查可存取性快取是否有效
    func isAccessibilityValid(validityDuration: TimeInterval) -> Bool {
        return Date().timeIntervalSince(lastAccessibilityCheck) < validityDuration
    }
}

/// 快取統計資訊
struct CacheStatistics {
    var hitCount: Int = 0
    var missCount: Int = 0
    
    var totalRequests: Int {
        return hitCount + missCount
    }
    
    var hitRate: Double {
        guard totalRequests > 0 else { return 0 }
        return Double(hitCount) / Double(totalRequests) * 100
    }
    
    var missRate: Double {
        guard totalRequests > 0 else { return 0 }
        return Double(missCount) / Double(totalRequests) * 100
    }
}

// MARK: - Extensions

extension SpaceDetectionCache {
    /// 獲取快取效能報告
    func getPerformanceReport() -> String {
        let stats = getCacheStatistics()
        return """
        Space Detection Cache Performance Report
        ========================================
        Total Requests: \(stats.totalRequests)
        Cache Hits: \(stats.hitCount)
        Cache Misses: \(stats.missCount)
        Hit Rate: \(String(format: "%.2f", stats.hitRate))%
        Miss Rate: \(String(format: "%.2f", stats.missRate))%
        Cached Spaces: \(cachedSpaces.count)
        Last Update: \(lastCacheUpdate)
        Cache Validity: \(cacheValidityDuration)s
        """
    }
    
    /// 重置統計資訊
    func resetStatistics() {
        DispatchQueue.main.async {
            self.cacheStats = CacheStatistics()
        }
    }
}