import Foundation
import Combine
import SwiftUI

/// 效能整合管理器
/// 協調所有效能最佳化組件，提供統一的效能管理介面
class PerformanceIntegrationManager: ObservableObject {
    static let shared = PerformanceIntegrationManager()
    
    // MARK: - Properties
    
    /// 效能最佳化狀態
    @Published var optimizationEnabled = true
    
    /// 整合效能指標
    @Published var integratedMetrics = IntegratedPerformanceMetrics()
    
    /// 最佳化建議
    @Published var optimizationRecommendations: [OptimizationRecommendation] = []
    
    /// 組件參考
    private let lazyLoader = LazyProfileLoader.shared
    private let spaceCache = SpaceDetectionCache.shared
    private let uiOptimizer = UIPerformanceOptimizer.shared
    private let performanceMonitor = PerformanceMonitor.shared
    
    /// 最佳化計時器
    private var optimizationTimer: Timer?
    
    /// 效能分析佇列
    private let analysisQueue = DispatchQueue(label: "performance.analysis", qos: .utility)
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        setupPerformanceIntegration()
        startPerformanceOptimization()
    }
    
    deinit {
        stopPerformanceOptimization()
    }
    
    // MARK: - Public Methods
    
    /// 開始效能最佳化
    func startPerformanceOptimization() {
        guard optimizationEnabled else { return }
        
        // 啟動效能監控
        performanceMonitor.startMonitoring()
        
        // 預熱快取
        spaceCache.warmUpCache()
        
        // 智能預載入
        lazyLoader.intelligentPreload()
        
        // 啟動定期最佳化
        optimizationTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            self?.performPeriodicOptimization()
        }
        
        print("PerformanceIntegrationManager: 效能最佳化已啟動")
    }
    
    /// 停止效能最佳化
    func stopPerformanceOptimization() {
        optimizationTimer?.invalidate()
        optimizationTimer = nil
        
        performanceMonitor.stopMonitoring()
        
        print("PerformanceIntegrationManager: 效能最佳化已停止")
    }
    
    /// 執行全面效能分析
    func performComprehensiveAnalysis() -> AnyPublisher<PerformanceAnalysisResult, Never> {
        return Future<PerformanceAnalysisResult, Never> { [weak self] promise in
            self?.analysisQueue.async {
                guard let self = self else {
                    promise(.success(PerformanceAnalysisResult.empty))
                    return
                }
                
                let result = self.analyzeOverallPerformance()
                promise(.success(result))
            }
        }
        .receive(on: DispatchQueue.main)
        .eraseToAnyPublisher()
    }
    
    /// 應用最佳化建議
    func applyOptimizationRecommendations(_ recommendations: [OptimizationRecommendation]) {
        for recommendation in recommendations {
            applyRecommendation(recommendation)
        }
        
        // 更新建議列表
        optimizationRecommendations.removeAll { recommendations.contains($0) }
    }
    
    /// 獲取效能摘要報告
    func getPerformanceSummaryReport() -> PerformanceSummaryReport {
        return PerformanceSummaryReport(
            timestamp: Date(),
            lazyLoadingStats: lazyLoader.getCacheStats(),
            spaceDetectionStats: spaceCache.getCacheStatistics(),
            uiPerformanceMetrics: uiOptimizer.performanceMetrics,
            systemMetrics: performanceMonitor.metrics,
            integratedMetrics: integratedMetrics,
            recommendations: optimizationRecommendations
        )
    }
    
    /// 重置所有效能資料
    func resetAllPerformanceData() {
        lazyLoader.clearCache()
        spaceCache.clearCache()
        uiOptimizer.clearRenderCache()
        performanceMonitor.resetMetrics()
        
        integratedMetrics = IntegratedPerformanceMetrics()
        optimizationRecommendations.removeAll()
        
        print("PerformanceIntegrationManager: 所有效能資料已重置")
    }
    
    // MARK: - Private Methods
    
    /// 設定效能整合
    private func setupPerformanceIntegration() {
        // 監聽各組件的效能變化
        setupPerformanceObservers()
        
        // 設定自動最佳化觸發器
        setupAutoOptimizationTriggers()
    }
    
    /// 設定效能觀察者
    private func setupPerformanceObservers() {
        // 監聽記憶體使用變化
        performanceMonitor.$metrics
            .map { $0.memoryUsage }
            .removeDuplicates()
            .sink { [weak self] memoryUsage in
                self?.handleMemoryUsageChange(memoryUsage)
            }
            .store(in: &cancellables)
        
        // 監聽 UI 效能變化
        uiOptimizer.$performanceMetrics
            .map { $0.averageResponseTime }
            .removeDuplicates()
            .sink { [weak self] responseTime in
                self?.handleResponseTimeChange(responseTime)
            }
            .store(in: &cancellables)
    }
    
    /// 設定自動最佳化觸發器
    private func setupAutoOptimizationTriggers() {
        // 當記憶體使用過高時自動清理
        // Note: NSApplication.didReceiveMemoryWarningNotification doesn't exist on macOS
        // Using a custom memory pressure monitoring approach instead
        Timer.scheduledTimer(withTimeInterval: 10.0, repeats: true) { [weak self] _ in
            self?.checkMemoryPressure()
        }
        
        // 當應用程式進入背景時執行清理
        NotificationCenter.default.publisher(for: NSApplication.didResignActiveNotification)
            .sink { [weak self] _ in
                self?.performBackgroundOptimization()
            }
            .store(in: &cancellables)
    }
    
    /// 執行定期最佳化
    private func performPeriodicOptimization() {
        analysisQueue.async { [weak self] in
            guard let self = self else { return }
            
            // 智能快取更新
            self.spaceCache.intelligentCacheUpdate()
            self.spaceCache.predictiveCaching()
            
            // 背景清理
            self.lazyLoader.backgroundCleanup()
            
            // UI 最佳化
            self.uiOptimizer.intelligentAnimationScheduling()
            
            // 更新整合指標
            DispatchQueue.main.async {
                self.updateIntegratedMetrics()
                self.generateOptimizationRecommendations()
            }
        }
    }
    
    /// 執行背景最佳化
    private func performBackgroundOptimization() {
        analysisQueue.async { [weak self] in
            guard let self = self else { return }
            
            // 清理不必要的快取
            self.lazyLoader.backgroundCleanup()
            self.uiOptimizer.clearRenderCache()
            
            // 重置統計資料
            self.spaceCache.resetStatistics()
            
            print("PerformanceIntegrationManager: 背景最佳化完成")
        }
    }
    
    /// 處理記憶體使用變化
    private func handleMemoryUsageChange(_ memoryUsage: Double) {
        if memoryUsage > 500 { // 超過 500MB
            addRecommendation(.reduceMemoryUsage)
            
            // 自動執行記憶體清理
            lazyLoader.backgroundCleanup()
            uiOptimizer.clearRenderCache()
        }
    }
    
    /// 處理響應時間變化
    private func handleResponseTimeChange(_ responseTime: TimeInterval) {
        if responseTime > 0.1 { // 超過 100ms
            addRecommendation(.optimizeUIPerformance)
            
            // 自動啟用效能模式
            uiOptimizer.intelligentAnimationScheduling()
        }
    }
    
    /// 處理記憶體警告
    private func handleMemoryWarning() {
        print("PerformanceIntegrationManager: 收到記憶體警告，執行緊急清理")
        
        // 緊急清理所有快取
        lazyLoader.clearCache()
        spaceCache.clearCache()
        uiOptimizer.clearRenderCache()
        
        addRecommendation(.emergencyMemoryCleanup)
    }
    
    /// 檢查記憶體壓力
    private func checkMemoryPressure() {
        let memoryUsage = performanceMonitor.metrics.memoryUsage
        if memoryUsage > 500 { // 超過 500MB 時觸發記憶體警告
            handleMemoryWarning()
        }
    }
    
    /// 分析整體效能
    private func analyzeOverallPerformance() -> PerformanceAnalysisResult {
        let lazyLoadingStats = lazyLoader.getCacheStats()
        let spaceDetectionStats = spaceCache.getCacheStatistics()
        let uiMetrics = uiOptimizer.performanceMetrics
        let systemMetrics = performanceMonitor.metrics
        
        // 計算整體效能分數
        let performanceScore = calculatePerformanceScore(
            lazyLoadingStats: lazyLoadingStats,
            spaceDetectionStats: spaceDetectionStats,
            uiMetrics: uiMetrics,
            systemMetrics: systemMetrics
        )
        
        // 識別效能瓶頸
        let bottlenecks = identifyPerformanceBottlenecks(
            lazyLoadingStats: lazyLoadingStats,
            spaceDetectionStats: spaceDetectionStats,
            uiMetrics: uiMetrics,
            systemMetrics: systemMetrics
        )
        
        return PerformanceAnalysisResult(
            performanceScore: performanceScore,
            bottlenecks: bottlenecks,
            recommendations: generateDetailedRecommendations(bottlenecks: bottlenecks),
            analysisTimestamp: Date()
        )
    }
    
    /// 計算效能分數
    private func calculatePerformanceScore(
        lazyLoadingStats: LazyLoadingCacheStats,
        spaceDetectionStats: CacheStatistics,
        uiMetrics: UIPerformanceMetrics,
        systemMetrics: SystemPerformanceMetrics
    ) -> Double {
        var score: Double = 100
        
        // 快取效能影響
        let cacheEfficiency = (spaceDetectionStats.hitRate + Double(lazyLoadingStats.loadedCount) / Double(lazyLoadingStats.maxCacheSize) * 100) / 2
        score = score * (cacheEfficiency / 100)
        
        // UI 響應時間影響
        if uiMetrics.averageResponseTime > 0.05 {
            score *= 0.9
        }
        if uiMetrics.averageResponseTime > 0.1 {
            score *= 0.8
        }
        
        // 記憶體使用影響
        if systemMetrics.memoryUsage > 300 {
            score *= 0.9
        }
        if systemMetrics.memoryUsage > 500 {
            score *= 0.7
        }
        
        return max(0, min(100, score))
    }
    
    /// 識別效能瓶頸
    private func identifyPerformanceBottlenecks(
        lazyLoadingStats: LazyLoadingCacheStats,
        spaceDetectionStats: CacheStatistics,
        uiMetrics: UIPerformanceMetrics,
        systemMetrics: SystemPerformanceMetrics
    ) -> [PerformanceBottleneck] {
        var bottlenecks: [PerformanceBottleneck] = []
        
        // 快取效能瓶頸
        if spaceDetectionStats.hitRate < 70 {
            bottlenecks.append(.lowCacheHitRate(spaceDetectionStats.hitRate))
        }
        
        // UI 效能瓶頸
        if uiMetrics.averageResponseTime > 0.1 {
            bottlenecks.append(.slowUIResponse(uiMetrics.averageResponseTime))
        }
        
        // 記憶體瓶頸
        if systemMetrics.memoryUsage > 500 {
            bottlenecks.append(.highMemoryUsage(systemMetrics.memoryUsage))
        }
        
        // 動畫效能瓶頸
        if uiMetrics.activeAnimations > 10 {
            bottlenecks.append(.tooManyActiveAnimations(uiMetrics.activeAnimations))
        }
        
        return bottlenecks
    }
    
    /// 生成詳細建議
    private func generateDetailedRecommendations(bottlenecks: [PerformanceBottleneck]) -> [OptimizationRecommendation] {
        var recommendations: [OptimizationRecommendation] = []
        
        for bottleneck in bottlenecks {
            switch bottleneck {
            case .lowCacheHitRate:
                recommendations.append(.improveCacheStrategy)
            case .slowUIResponse:
                recommendations.append(.optimizeUIPerformance)
            case .highMemoryUsage:
                recommendations.append(.reduceMemoryUsage)
            case .tooManyActiveAnimations:
                recommendations.append(.limitConcurrentAnimations)
            }
        }
        
        return recommendations
    }
    
    /// 更新整合指標
    private func updateIntegratedMetrics() {
        let lazyStats = lazyLoader.getCacheStats()
        let spaceStats = spaceCache.getCacheStatistics()
        let uiMetrics = uiOptimizer.performanceMetrics
        let systemMetrics = performanceMonitor.metrics
        
        integratedMetrics.overallCacheHitRate = (spaceStats.hitRate + uiMetrics.cacheHitRate) / 2
        integratedMetrics.averageResponseTime = uiMetrics.averageResponseTime
        integratedMetrics.memoryEfficiency = max(0, 100 - (systemMetrics.memoryUsage / 10))
        integratedMetrics.loadedProfilesCount = lazyStats.loadedCount
        integratedMetrics.lastUpdateTime = Date()
    }
    
    /// 生成最佳化建議
    private func generateOptimizationRecommendations() {
        var newRecommendations: [OptimizationRecommendation] = []
        
        // 基於整合指標生成建議
        if integratedMetrics.overallCacheHitRate < 70 {
            newRecommendations.append(.improveCacheStrategy)
        }
        
        if integratedMetrics.averageResponseTime > 0.1 {
            newRecommendations.append(.optimizeUIPerformance)
        }
        
        if integratedMetrics.memoryEfficiency < 70 {
            newRecommendations.append(.reduceMemoryUsage)
        }
        
        // 只添加新的建議
        for recommendation in newRecommendations {
            if !optimizationRecommendations.contains(recommendation) {
                optimizationRecommendations.append(recommendation)
            }
        }
    }
    
    /// 添加建議
    private func addRecommendation(_ recommendation: OptimizationRecommendation) {
        if !optimizationRecommendations.contains(recommendation) {
            optimizationRecommendations.append(recommendation)
        }
    }
    
    /// 應用建議
    private func applyRecommendation(_ recommendation: OptimizationRecommendation) {
        switch recommendation {
        case .improveCacheStrategy:
            spaceCache.warmUpCache()
            lazyLoader.intelligentPreload()
        case .optimizeUIPerformance:
            uiOptimizer.intelligentAnimationScheduling()
        case .reduceMemoryUsage:
            lazyLoader.backgroundCleanup()
            uiOptimizer.clearRenderCache()
        case .limitConcurrentAnimations:
            uiOptimizer.animationConfig.enableLowPowerMode()
        case .emergencyMemoryCleanup:
            resetAllPerformanceData()
        }
    }
}

// MARK: - Data Structures

/// 整合效能指標
struct IntegratedPerformanceMetrics {
    var overallCacheHitRate: Double = 0
    var averageResponseTime: TimeInterval = 0
    var memoryEfficiency: Double = 100
    var loadedProfilesCount: Int = 0
    var lastUpdateTime: Date = Date()
}

/// 最佳化建議
enum OptimizationRecommendation: CaseIterable, Equatable {
    case improveCacheStrategy
    case optimizeUIPerformance
    case reduceMemoryUsage
    case limitConcurrentAnimations
    case emergencyMemoryCleanup
    
    var description: String {
        switch self {
        case .improveCacheStrategy:
            return "改善快取策略以提高命中率"
        case .optimizeUIPerformance:
            return "最佳化 UI 效能以減少響應時間"
        case .reduceMemoryUsage:
            return "減少記憶體使用以提高效率"
        case .limitConcurrentAnimations:
            return "限制同時進行的動畫數量"
        case .emergencyMemoryCleanup:
            return "執行緊急記憶體清理"
        }
    }
}

/// 效能瓶頸
enum PerformanceBottleneck {
    case lowCacheHitRate(Double)
    case slowUIResponse(TimeInterval)
    case highMemoryUsage(Double)
    case tooManyActiveAnimations(Int)
    
    var description: String {
        switch self {
        case .lowCacheHitRate(let rate):
            return "快取命中率過低: \(String(format: "%.1f", rate))%"
        case .slowUIResponse(let time):
            return "UI 響應時間過長: \(String(format: "%.3f", time))s"
        case .highMemoryUsage(let usage):
            return "記憶體使用過高: \(String(format: "%.1f", usage))MB"
        case .tooManyActiveAnimations(let count):
            return "同時進行的動畫過多: \(count) 個"
        }
    }
}

/// 效能分析結果
struct PerformanceAnalysisResult {
    let performanceScore: Double
    let bottlenecks: [PerformanceBottleneck]
    let recommendations: [OptimizationRecommendation]
    let analysisTimestamp: Date
    
    static let empty = PerformanceAnalysisResult(
        performanceScore: 0,
        bottlenecks: [],
        recommendations: [],
        analysisTimestamp: Date()
    )
}

/// 效能摘要報告
struct PerformanceSummaryReport {
    let timestamp: Date
    let lazyLoadingStats: LazyLoadingCacheStats
    let spaceDetectionStats: CacheStatistics
    let uiPerformanceMetrics: UIPerformanceMetrics
    let systemMetrics: SystemPerformanceMetrics
    let integratedMetrics: IntegratedPerformanceMetrics
    let recommendations: [OptimizationRecommendation]
}