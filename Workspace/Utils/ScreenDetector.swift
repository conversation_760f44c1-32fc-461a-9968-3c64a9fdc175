import Foundation
import CoreGraphics

// MARK: - ScreenDetectionResult
struct ScreenDetectionResult {
    let screens: [ScreenBounds]
    let confidence: Double
    let detectionMethod: DetectionMethod
    
    enum DetectionMethod {
        case clustering
        case gapAnalysis
        case fallback
    }
}

// MARK: - ScreenDetector
struct ScreenDetector {
    
    /// 檢測螢幕配置
    /// - Parameter windows: 視窗佈局陣列
    /// - Returns: 螢幕檢測結果
    static func detectScreenConfiguration(from windows: [WindowLayout]) -> ScreenDetectionResult {
        guard !windows.isEmpty else {
            return ScreenDetectionResult(
                screens: [defaultScreen()],
                confidence: 0.0,
                detectionMethod: .fallback
            )
        }
        
        // 嘗試使用聚類方法檢測
        if let clusteringResult = detectUsingClustering(windows: windows) {
            return clusteringResult
        }
        
        // 嘗試使用間隙分析方法檢測
        if let gapResult = detectUsingGapAnalysis(windows: windows) {
            return gapResult
        }
        
        // 回退到簡單檢測
        return ScreenDetectionResult(
            screens: [createScreenFromWindows(windows)],
            confidence: 0.3,
            detectionMethod: .fallback
        )
    }
    
    // MARK: - Private Methods
    
    private static func detectUsingClustering(windows: [WindowLayout]) -> ScreenDetectionResult? {
        // 簡化的聚類算法實現
        // 這裡可以實現更複雜的 K-means 或其他聚類算法
        
        let windowCenters = windows.map { window in
            CGPoint(x: window.frame.x + window.frame.w/2, y: window.frame.y + window.frame.h/2)
        }
        
        // 如果視窗數量太少，無法進行有效聚類
        guard windowCenters.count >= 3 else { return nil }
        
        // 簡單的雙聚類檢測（假設最多兩個螢幕）
        let sortedByX = windowCenters.sorted { $0.x < $1.x }
        let midIndex = sortedByX.count / 2
        
        let leftGroup = Array(sortedByX[0..<midIndex])
        let rightGroup = Array(sortedByX[midIndex...])
        
        // 檢查是否存在明顯的間隙
        let leftMaxX = leftGroup.map { $0.x }.max() ?? 0
        let rightMinX = rightGroup.map { $0.x }.min() ?? 0
        let gap = rightMinX - leftMaxX
        
        if gap > 100 { // 如果間隙大於100像素，認為是兩個螢幕
            let leftScreen = createScreenFromPoints(leftGroup)
            let rightScreen = createScreenFromPoints(rightGroup)
            
            return ScreenDetectionResult(
                screens: [leftScreen, rightScreen],
                confidence: 0.8,
                detectionMethod: .clustering
            )
        }
        
        return nil
    }
    
    private static func detectUsingGapAnalysis(windows: [WindowLayout]) -> ScreenDetectionResult? {
        // 分析視窗之間的間隙來檢測螢幕邊界
        let windowFrames = windows.map { window in
            CGRect(x: window.frame.x, y: window.frame.y, width: window.frame.w, height: window.frame.h)
        }
        
        // 找到所有垂直間隙
        let verticalGaps = findVerticalGaps(in: windowFrames)
        
        // 如果存在大的垂直間隙，可能表示多個螢幕
        let significantGaps = verticalGaps.filter { $0.width > 50 }
        
        if !significantGaps.isEmpty {
            // 基於間隙分割螢幕
            let screens = createScreensFromGaps(windows: windows, gaps: significantGaps)
            
            return ScreenDetectionResult(
                screens: screens,
                confidence: 0.7,
                detectionMethod: .gapAnalysis
            )
        }
        
        return nil
    }
    
    private static func findVerticalGaps(in frames: [CGRect]) -> [CGRect] {
        // 簡化實現：找到視窗之間的垂直間隙
        var gaps: [CGRect] = []
        
        let sortedFrames = frames.sorted { $0.minX < $1.minX }
        
        for i in 0..<(sortedFrames.count - 1) {
            let current = sortedFrames[i]
            let next = sortedFrames[i + 1]
            
            if next.minX > current.maxX {
                let gap = CGRect(
                    x: current.maxX,
                    y: min(current.minY, next.minY),
                    width: next.minX - current.maxX,
                    height: max(current.maxY, next.maxY) - min(current.minY, next.minY)
                )
                gaps.append(gap)
            }
        }
        
        return gaps
    }
    
    private static func createScreensFromGaps(windows: [WindowLayout], gaps: [CGRect]) -> [ScreenBounds] {
        // 基於間隙創建螢幕邊界
        // 這是一個簡化實現
        
        if gaps.isEmpty {
            return [createScreenFromWindows(windows)]
        }
        
        let firstGap = gaps[0]
        let leftWindows = windows.filter { $0.frame.x < firstGap.minX }
        let rightWindows = windows.filter { $0.frame.x > firstGap.maxX }
        
        var screens: [ScreenBounds] = []
        
        if !leftWindows.isEmpty {
            screens.append(createScreenFromWindows(leftWindows))
        }
        
        if !rightWindows.isEmpty {
            screens.append(createScreenFromWindows(rightWindows))
        }
        
        return screens.isEmpty ? [createScreenFromWindows(windows)] : screens
    }
    
    private static func createScreenFromPoints(_ points: [CGPoint]) -> ScreenBounds {
        guard !points.isEmpty else { return defaultScreen() }
        
        let minX = points.map { $0.x }.min() ?? 0
        let maxX = points.map { $0.x }.max() ?? 1920
        let minY = points.map { $0.y }.min() ?? 0
        let maxY = points.map { $0.y }.max() ?? 1080
        
        // 添加一些邊距
        let margin: CGFloat = 100
        let bounds = CGRect(
            x: minX - margin,
            y: minY - margin,
            width: (maxX - minX) + 2 * margin,
            height: (maxY - minY) + 2 * margin
        )
        
        return ScreenBounds(
            bounds: bounds,
            isPrimary: minX < 100, // 假設左側螢幕是主螢幕
            displayName: "檢測到的螢幕",
            scaleFactor: 1.0
        )
    }
    
    private static func createScreenFromWindows(_ windows: [WindowLayout]) -> ScreenBounds {
        guard !windows.isEmpty else { return defaultScreen() }
        
        let frames = windows.map { window in
            CGRect(x: window.frame.x, y: window.frame.y, width: window.frame.w, height: window.frame.h)
        }
        
        let minX = frames.map { $0.minX }.min() ?? 0
        let maxX = frames.map { $0.maxX }.max() ?? 1920
        let minY = frames.map { $0.minY }.min() ?? 0
        let maxY = frames.map { $0.maxY }.max() ?? 1080
        
        let bounds = CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)
        
        return ScreenBounds(
            bounds: bounds,
            isPrimary: true,
            displayName: "檢測到的螢幕區域",
            scaleFactor: 1.0
        )
    }
    
    private static func defaultScreen() -> ScreenBounds {
        return ScreenBounds(
            bounds: CGRect(x: 0, y: 0, width: 1920, height: 1080),
            isPrimary: true,
            displayName: "預設螢幕",
            scaleFactor: 1.0
        )
    }
}