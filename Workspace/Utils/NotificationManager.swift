import UserNotifications
import AppKit

class NotificationManager {
    static let shared = NotificationManager()
    private var hasPermission = false
    
    private init() {
        // 延遲初始化通知權限檢查，避免在應用程式啟動期間調用 UNUserNotificationCenter
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.checkAndRequestPermission()
        }
    }

    private func checkAndRequestPermission() {
        // 安全地獲取通知中心，避免崩潰
        guard let notificationCenter = getNotificationCenterSafely() else {
            print("無法獲取 UNUserNotificationCenter，跳過通知權限檢查")
            return
        }

        notificationCenter.getNotificationSettings { settings in
            DispatchQueue.main.async {
                switch settings.authorizationStatus {
                case .authorized, .provisional:
                    self.hasPermission = true
                case .denied:
                    self.hasPermission = false
                    print("通知權限被拒絕，請到系統偏好設定 > 通知與專注模式中啟用")
                case .notDetermined:
                    self.requestPermission()
                @unknown default:
                    self.hasPermission = false
                }
            }
        }
    }

    private func getNotificationCenterSafely() -> UNUserNotificationCenter? {
        // 使用 try-catch 安全地獲取通知中心
        do {
            let center = UNUserNotificationCenter.current()
            return center
        } catch {
            print("獲取 UNUserNotificationCenter 失敗: \(error)")
            return nil
        }
    }

    private func requestPermission() {
        guard let notificationCenter = getNotificationCenterSafely() else {
            print("無法獲取 UNUserNotificationCenter，跳過權限請求")
            return
        }

        notificationCenter.requestAuthorization(options: [.alert, .sound]) { granted, error in
            DispatchQueue.main.async {
                self.hasPermission = granted
                if let error = error {
                    print("通知權限請求失敗: \(error)")
                } else if !granted {
                    print("用戶拒絕了通知權限")
                }
            }
        }
    }
    
    func showSuccess(title: String, message: String) {
        // 總是在控制台顯示訊息
        print("✅ \(title): \(message)")
        
        if hasPermission {
            let content = UNMutableNotificationContent()
            content.title = title
            content.body = message
            content.sound = .default
            
            let request = UNNotificationRequest(identifier: UUID().uuidString, content: content, trigger: nil)

            guard let notificationCenter = getNotificationCenterSafely() else {
                print("無法獲取 UNUserNotificationCenter，跳過通知發送")
                return
            }

            notificationCenter.add(request) { error in
                if let error = error {
                    print("發送通知失敗: \(error)")
                }
                // 總是播放聲音
                DispatchQueue.main.async {
                    self.playCompletionSound()
                }
            }
        } else {
            // 沒有權限時，只播放聲音
            playCompletionSound()
        }
    }

    func showInfo(title: String, message: String) {
        // 總是在控制台顯示訊息
        print("ℹ️ \(title): \(message)")

        if hasPermission {
            let content = UNMutableNotificationContent()
            content.title = title
            content.body = message
            content.sound = .default

            let request = UNNotificationRequest(identifier: UUID().uuidString, content: content, trigger: nil)

            guard let notificationCenter = getNotificationCenterSafely() else {
                print("無法獲取 UNUserNotificationCenter，跳過通知發送")
                return
            }

            notificationCenter.add(request) { error in
                if let error = error {
                    print("發送通知失敗: \(error)")
                }
            }
        }
    }

    func showError(title: String, message: String) {
        // 總是在控制台顯示訊息
        print("❌ \(title): \(message)")
        
        if hasPermission {
            let content = UNMutableNotificationContent()
            content.title = title
            content.body = message
            content.sound = .defaultCritical
            
            let request = UNNotificationRequest(identifier: UUID().uuidString, content: content, trigger: nil)

            guard let notificationCenter = getNotificationCenterSafely() else {
                print("無法獲取 UNUserNotificationCenter，跳過通知發送")
                return
            }

            notificationCenter.add(request) { error in
                if let error = error {
                    print("發送通知失敗: \(error)")
                }
                // 總是播放聲音
                DispatchQueue.main.async {
                    NSSound.beep()
                }
            }
        } else {
            // 沒有權限時，只播放聲音
            NSSound.beep()
        }
    }
    
    func playCompletionSound() {
        NSSound.beep()
    }
    
    // 重新檢查通知權限（可以在設定頁面調用）
    func recheckPermission() {
        checkAndRequestPermission()
    }
    
    // 開啟系統通知設定
    func openNotificationSettings() {
        if let url = URL(string: "x-apple.systempreferences:com.apple.preference.notifications") {
            NSWorkspace.shared.open(url)
        }
    }
}