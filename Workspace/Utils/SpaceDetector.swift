import Foundation
import Combine

/// 表示 macOS Space 資訊的資料結構
struct SpaceInfo: Identifiable, Codable, Equatable {
    let id: Int
    let name: String
    let isActive: Bool
    let displayName: String
    
    init(id: Int, name: String? = nil, isActive: Bool = false) {
        self.id = id
        self.name = name ?? "Space \(id)"
        self.isActive = isActive
        self.displayName = name ?? "工作區 \(id)"
    }
}

/// 負責偵測和管理 macOS Spaces 資訊的核心組件
class SpaceDetector: ObservableObject {
    static let shared = SpaceDetector()
    
    @Published var currentSpaceID: Int?
    @Published var availableSpaces: [SpaceInfo] = []
    @Published var maxSupportedSpaces: Int = 3
    
    private var cancellables = Set<AnyCancellable>()
    private let refreshInterval: TimeInterval = 2.0
    
    /// Space 偵測快取
    private let detectionCache = SpaceDetectionCache.shared
    
    private init() {
        // 預熱快取
        detectionCache.warmUpCache()
        startPeriodicRefresh()

        // 延遲初始化，避免在視圖更新期間修改 @Published 屬性
        DispatchQueue.main.async { [weak self] in
            self?.refreshSpaceInfo()
        }
    }
    
    /// 獲取當前 Space ID
    func getCurrentSpace() -> Int? {
        guard let spaceID = detectionCache.getCurrentSpaceID() else {
            return nil
        }
        
        // 驗證 Space ID 是否在支援範圍內
        if spaceID >= 1 && spaceID <= maxSupportedSpaces {
            return spaceID
        }
        
        return nil
    }
    
    /// 獲取所有可用的 Spaces
    func getAvailableSpaces() -> [SpaceInfo] {
        return detectionCache.getAvailableSpaces()
    }
    
    /// 檢查指定的 Space 是否可存取
    func isSpaceAccessible(_ spaceID: Int) -> Bool {
        // 檢查 Space ID 是否在有效範圍內
        guard spaceID >= 1 && spaceID <= maxSupportedSpaces else {
            return false
        }
        
        // 使用快取檢查可存取性
        return detectionCache.isSpaceAccessible(spaceID)
    }
    
    /// 刷新 Space 資訊
    func refreshSpaceInfo() {
        DispatchQueue.global(qos: .background).async { [weak self] in
            guard let self = self else { return }
            
            let currentSpace = self.getCurrentSpace()
            let availableSpaces = self.getAvailableSpaces()
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
                self.currentSpaceID = currentSpace
                self.availableSpaces = availableSpaces
            }
        }
    }
    
    /// 強制刷新 Space 資訊（繞過快取）
    func forceRefreshSpaceInfo() {
        DispatchQueue.global(qos: .background).async { [weak self] in
            guard let self = self else { return }
            
            let currentSpace = self.detectionCache.getCurrentSpaceID(forceRefresh: true)
            let availableSpaces = self.detectionCache.getAvailableSpaces(forceRefresh: true)
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) {
                self.currentSpaceID = currentSpace
                self.availableSpaces = availableSpaces
            }
        }
    }
    
    // MARK: - Private Methods
    
    /// 開始定期刷新 Space 資訊
    private func startPeriodicRefresh() {
        // 延遲啟動定時器，避免在初始化期間立即觸發
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            guard let self = self else { return }

            Timer.publish(every: self.refreshInterval, on: .main, in: .common)
                .autoconnect()
                .sink { [weak self] _ in
                    self?.refreshSpaceInfo()
                }
                .store(in: &self.cancellables)
        }
    }
    
    /// 執行 Space 偵測腳本
    private func executeSpaceDetectionScript() -> Int? {
        let script = """
        tell application "System Events"
            try
                set spaceNumber to do shell script "osascript -e 'tell application \\"System Events\\" to get the name of current desktop'"
                return spaceNumber as integer
            on error
                -- 如果無法直接獲取，嘗試替代方法
                try
                    set currentDesktop to do shell script "defaults read com.apple.spaces SpacesDisplayConfiguration | grep -o 'Current Space.*' | head -1 | grep -o '[0-9]*' | head -1"
                    return currentDesktop as integer
                on error
                    -- 預設回傳 1
                    return 1
                end try
            end try
        end tell
        """
        
        return executeAppleScript(script)
    }
    
    /// 執行 Space 可存取性檢查
    private func executeSpaceAccessibilityCheck(_ spaceID: Int) -> Bool {
        let script = """
        tell application "System Events"
            try
                -- 嘗試獲取指定 Space 的資訊
                set spaceExists to do shell script "osascript -e 'tell application \\"System Events\\" to get desktop \(spaceID)'"
                return true
            on error
                return false
            end try
        end tell
        """
        
        return executeAppleScript(script) != nil
    }
    
    /// 執行 AppleScript 並回傳整數結果
    private func executeAppleScript(_ script: String) -> Int? {
        let process = Process()
        process.launchPath = "/usr/bin/osascript"
        process.arguments = ["-e", script]
        
        let pipe = Pipe()
        process.standardOutput = pipe
        process.standardError = Pipe()
        
        do {
            try process.run()
            process.waitUntilExit()
            
            let data = pipe.fileHandleForReading.readDataToEndOfFile()
            let output = String(data: data, encoding: .utf8)?.trimmingCharacters(in: .whitespacesAndNewlines)
            
            if let output = output, let spaceID = Int(output) {
                return spaceID
            }
        } catch {
            print("執行 AppleScript 失敗: \(error)")
        }
        
        return nil
    }
}

// MARK: - Extensions

extension SpaceDetector {
    /// 獲取指定 Space 的顯示名稱
    func getDisplayName(for spaceID: Int) -> String {
        return availableSpaces.first { $0.id == spaceID }?.displayName ?? "工作區 \(spaceID)"
    }
    
    /// 檢查是否為當前 Space
    func isCurrentSpace(_ spaceID: Int) -> Bool {
        return currentSpaceID == spaceID
    }
    
    /// 獲取預設 Space ID
    func getDefaultSpaceID() -> Int {
        return 1
    }
}