import SwiftUI
import Combine

/// UI 效能最佳化工具
/// 提供動畫最佳化、響應時間改善和渲染效能提升功能
class UIPerformanceOptimizer: ObservableObject {
    static let shared = UIPerformanceOptimizer()
    
    // MARK: - Properties
    
    /// 動畫配置
    @Published var animationConfig = AnimationConfig()
    
    /// 效能監控
    @Published var performanceMetrics = UIPerformanceMetrics()
    
    /// 渲染最佳化設定
    @Published var renderingConfig = RenderingConfig()
    
    /// 動畫狀態管理
    private var animationStates: [String: AnimationState] = [:]
    
    /// 效能監控計時器
    private var performanceTimer: Timer?
    
    /// 渲染快取
    private var renderCache: [String: AnyView] = [:]
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        setupPerformanceMonitoring()
        optimizeForCurrentDevice()
    }
    
    deinit {
        performanceTimer?.invalidate()
    }
    
    // MARK: - Animation Optimization
    
    /// 獲取最佳化的動畫
    func optimizedAnimation(for context: AnimationContext) -> Animation {
        switch context {
        case .spaceTransition:
            return animationConfig.spaceTransitionAnimation
        case .profileListUpdate:
            return animationConfig.profileListAnimation
        case .tabSwitch:
            return animationConfig.tabSwitchAnimation
        case .contentLoad:
            return animationConfig.contentLoadAnimation
        case .hover:
            return animationConfig.hoverAnimation
        }
    }
    
    /// 開始動畫狀態追蹤
    func startAnimation(_ identifier: String, context: AnimationContext) {
        let state = AnimationState(
            identifier: identifier,
            context: context,
            startTime: Date(),
            isActive: true
        )
        
        animationStates[identifier] = state
        performanceMetrics.activeAnimations += 1
    }
    
    /// 結束動畫狀態追蹤
    func endAnimation(_ identifier: String) {
        guard var state = animationStates[identifier] else { return }
        
        state.isActive = false
        state.endTime = Date()
        
        let duration = state.endTime?.timeIntervalSince(state.startTime) ?? 0
        performanceMetrics.averageAnimationDuration = 
            (performanceMetrics.averageAnimationDuration + duration) / 2
        
        animationStates.removeValue(forKey: identifier)
        performanceMetrics.activeAnimations = max(0, performanceMetrics.activeAnimations - 1)
    }
    
    /// 批次動畫最佳化
    func batchAnimations<T>(_ animations: [() -> T]) -> [T] {
        var results: [T] = []
        
        // 暫時禁用動畫以提升批次操作效能
        let originalAnimationsEnabled = animationConfig.animationsEnabled
        animationConfig.animationsEnabled = false
        
        for animation in animations {
            results.append(animation())
        }
        
        // 恢復動畫設定
        animationConfig.animationsEnabled = originalAnimationsEnabled
        
        return results
    }
    
    // MARK: - Rendering Optimization
    
    /// 獲取快取的視圖
    func getCachedView(for key: String) -> AnyView? {
        return renderCache[key]
    }
    
    /// 快取視圖
    func cacheView<T: View>(_ view: T, for key: String) {
        if renderCache.count >= renderingConfig.maxCacheSize {
            // 清理最舊的快取項目
            let oldestKey = renderCache.keys.first
            if let key = oldestKey {
                renderCache.removeValue(forKey: key)
            }
        }
        
        renderCache[key] = AnyView(view)
    }
    
    /// 清除渲染快取
    func clearRenderCache() {
        renderCache.removeAll()
        performanceMetrics.cacheHits = 0
        performanceMetrics.cacheMisses = 0
    }
    
    /// 預渲染視圖
    func preRenderViews(for keys: [String], viewBuilder: @escaping (String) -> AnyView) {
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            for key in keys {
                if self?.renderCache[key] == nil {
                    let view = viewBuilder(key)
                    DispatchQueue.main.async {
                        self?.renderCache[key] = view
                    }
                }
            }
        }
    }
    
    /// 智能動畫調度：根據系統負載調整動畫品質
    func intelligentAnimationScheduling() {
        let systemLoad = getCurrentSystemLoad()
        
        if systemLoad > 0.8 {
            // 高負載時降低動畫品質
            animationConfig.enableLowPowerMode()
            renderingConfig.enablePerformanceMode()
        } else if systemLoad < 0.3 {
            // 低負載時恢復正常動畫品質
            animationConfig = AnimationConfig() // 重置為預設值
            renderingConfig.performanceModeEnabled = false
        }
    }
    
    /// 自適應渲染：根據內容複雜度調整渲染策略
    func adaptiveRendering(for contentComplexity: ContentComplexity) -> RenderingStrategy {
        switch contentComplexity {
        case .simple:
            return .immediate
        case .moderate:
            return renderingConfig.performanceModeEnabled ? .cached : .immediate
        case .complex:
            return .cached
        case .veryComplex:
            return .lazy
        }
    }
    
    /// 獲取當前系統負載
    private func getCurrentSystemLoad() -> Double {
        // 簡化的系統負載計算
        let memoryPressure = performanceMetrics.memoryUsage / 1000.0 // 假設 1GB 為基準
        let animationLoad = Double(performanceMetrics.activeAnimations) / 10.0 // 假設 10 個動畫為高負載
        
        return min(1.0, (memoryPressure + animationLoad) / 2.0)
    }
    
    // MARK: - Performance Monitoring
    
    /// 記錄 UI 操作時間
    func measureUIOperation<T>(_ operation: () -> T, operationType: String) -> T {
        let startTime = CFAbsoluteTimeGetCurrent()
        let result = operation()
        let endTime = CFAbsoluteTimeGetCurrent()
        
        let duration = endTime - startTime
        recordOperationTime(duration, for: operationType)
        
        return result
    }
    
    /// 記錄操作時間
    private func recordOperationTime(_ duration: TimeInterval, for operationType: String) {
        performanceMetrics.operationTimes[operationType] = duration
        
        // 更新平均響應時間
        let currentAverage = performanceMetrics.averageResponseTime
        performanceMetrics.averageResponseTime = (currentAverage + duration) / 2
        
        // 檢查是否需要調整效能設定
        if duration > renderingConfig.responseTimeThreshold {
            adjustPerformanceSettings()
        }
    }
    
    /// 調整效能設定
    private func adjustPerformanceSettings() {
        // 如果響應時間過長，降低動畫品質
        if performanceMetrics.averageResponseTime > 0.1 {
            animationConfig.reduceAnimationQuality()
            renderingConfig.enablePerformanceMode()
        }
    }
    
    // MARK: - Device Optimization
    
    /// 根據當前設備最佳化設定
    private func optimizeForCurrentDevice() {
        let processInfo = ProcessInfo.processInfo
        let physicalMemory = processInfo.physicalMemory
        
        // 根據記憶體大小調整快取設定
        if physicalMemory < 8 * 1024 * 1024 * 1024 { // 小於 8GB
            renderingConfig.maxCacheSize = 20
            animationConfig.enableLowPowerMode()
        } else if physicalMemory < 16 * 1024 * 1024 * 1024 { // 小於 16GB
            renderingConfig.maxCacheSize = 50
        } else {
            renderingConfig.maxCacheSize = 100
        }
        
        // 檢查是否為低功耗模式
        if processInfo.isLowPowerModeEnabled {
            animationConfig.enableLowPowerMode()
            renderingConfig.enablePerformanceMode()
        }
    }
    
    /// 設定效能監控
    private func setupPerformanceMonitoring() {
        performanceTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updatePerformanceMetrics()
        }
    }
    
    /// 更新效能指標
    private func updatePerformanceMetrics() {
        // 更新記憶體使用情況
        var memoryInfo = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &memoryInfo) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            performanceMetrics.memoryUsage = Double(memoryInfo.resident_size) / (1024 * 1024) // MB
        }
        
        // 更新快取統計
        performanceMetrics.cacheSize = renderCache.count
        
        // 檢查是否需要清理快取
        if performanceMetrics.memoryUsage > 500 { // 超過 500MB
            clearRenderCache()
        }
    }
}

// MARK: - Data Structures

/// 動畫配置
struct AnimationConfig {
    var animationsEnabled: Bool = true
    var spaceTransitionAnimation: Animation = .easeInOut(duration: 0.3)
    var profileListAnimation: Animation = .easeOut(duration: 0.2)
    var tabSwitchAnimation: Animation = .easeInOut(duration: 0.25)
    var contentLoadAnimation: Animation = .easeOut(duration: 0.15)
    var hoverAnimation: Animation = .easeInOut(duration: 0.1)
    
    mutating func enableLowPowerMode() {
        spaceTransitionAnimation = .easeInOut(duration: 0.2)
        profileListAnimation = .easeOut(duration: 0.1)
        tabSwitchAnimation = .easeInOut(duration: 0.15)
        contentLoadAnimation = .easeOut(duration: 0.1)
        hoverAnimation = .easeInOut(duration: 0.05)
    }
    
    mutating func reduceAnimationQuality() {
        spaceTransitionAnimation = .linear(duration: 0.2)
        profileListAnimation = .linear(duration: 0.1)
        tabSwitchAnimation = .linear(duration: 0.15)
        contentLoadAnimation = .linear(duration: 0.1)
        hoverAnimation = .linear(duration: 0.05)
    }
}

/// 渲染配置
struct RenderingConfig {
    var maxCacheSize: Int = 50
    var responseTimeThreshold: TimeInterval = 0.05
    var enableViewCaching: Bool = true
    var enablePreRendering: Bool = true
    var performanceModeEnabled: Bool = false
    
    mutating func enablePerformanceMode() {
        performanceModeEnabled = true
        maxCacheSize = min(maxCacheSize, 20)
        enablePreRendering = false
    }
}

/// UI 效能指標
struct UIPerformanceMetrics {
    var averageResponseTime: TimeInterval = 0
    var averageAnimationDuration: TimeInterval = 0
    var activeAnimations: Int = 0
    var memoryUsage: Double = 0 // MB
    var cacheSize: Int = 0
    var cacheHits: Int = 0
    var cacheMisses: Int = 0
    var operationTimes: [String: TimeInterval] = [:]
    
    var cacheHitRate: Double {
        let total = cacheHits + cacheMisses
        return total > 0 ? Double(cacheHits) / Double(total) * 100 : 0
    }
}

/// 動畫上下文
enum AnimationContext {
    case spaceTransition
    case profileListUpdate
    case tabSwitch
    case contentLoad
    case hover
}

/// 動畫狀態
struct AnimationState {
    let identifier: String
    let context: AnimationContext
    let startTime: Date
    var endTime: Date?
    var isActive: Bool
}

/// 內容複雜度
enum ContentComplexity {
    case simple      // 簡單內容（文字、基本圖形）
    case moderate    // 中等複雜度（列表、基本動畫）
    case complex     // 複雜內容（複雜佈局、多層動畫）
    case veryComplex // 非常複雜（大量資料、複雜互動）
}

/// 渲染策略
enum RenderingStrategy {
    case immediate   // 立即渲染
    case cached      // 使用快取
    case lazy        // 懶載入渲染
}

// MARK: - View Extensions

extension View {
    /// 應用效能最佳化的動畫
    func optimizedAnimation(for context: AnimationContext) -> some View {
        let optimizer = UIPerformanceOptimizer.shared
        return self.animation(optimizer.optimizedAnimation(for: context), value: UUID())
    }
    
    /// 測量視圖渲染時間
    func measureRenderTime(_ operationType: String) -> some View {
        let optimizer = UIPerformanceOptimizer.shared
        return optimizer.measureUIOperation({
            return self
        }, operationType: operationType)
    }
    
    /// 啟用視圖快取
    func cached(key: String) -> some View {
        let optimizer = UIPerformanceOptimizer.shared
        
        if let cachedView = optimizer.getCachedView(for: key) {
            optimizer.performanceMetrics.cacheHits += 1
            return cachedView
        } else {
            optimizer.performanceMetrics.cacheMisses += 1
            let view = AnyView(self)
            optimizer.cacheView(view, for: key)
            return view
        }
    }
    
    /// 最佳化的懸停效果
    func optimizedHover() -> some View {
        self.scaleEffect(1.0)
            .optimizedAnimation(for: .hover)
            .onHover { isHovering in
                withAnimation(UIPerformanceOptimizer.shared.optimizedAnimation(for: .hover)) {
                    // 懸停效果邏輯
                }
            }
    }
}