import Foundation
import Combine
import AppKit

/// 效能監控工具
/// 提供即時效能監控、報告生成和效能警告功能
class PerformanceMonitor: ObservableObject {
    static let shared = PerformanceMonitor()
    
    // MARK: - Properties
    
    /// 效能指標
    @Published var metrics = SystemPerformanceMetrics()
    
    /// 監控配置
    @Published var config = MonitoringConfig()
    
    /// 效能警告
    @Published var warnings: [PerformanceWarning] = []
    
    /// 監控狀態
    @Published var isMonitoring = false
    
    /// 效能歷史記錄
    private var performanceHistory: [PerformanceSnapshot] = []
    
    /// 監控計時器
    private var monitoringTimer: Timer?
    
    /// 操作計時器
    private var operationTimers: [String: Date] = [:]
    
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        setupMonitoring()
    }
    
    deinit {
        stopMonitoring()
    }
    
    // MARK: - Public Methods
    
    /// 開始效能監控
    func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        
        monitoringTimer = Timer.scheduledTimer(withTimeInterval: config.samplingInterval, repeats: true) { [weak self] _ in
            self?.collectMetrics()
        }
        
        print("PerformanceMonitor: 開始效能監控")
    }
    
    /// 停止效能監控
    func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        monitoringTimer?.invalidate()
        monitoringTimer = nil
        
        print("PerformanceMonitor: 停止效能監控")
    }
    
    /// 開始操作計時
    func startOperation(_ operationName: String) {
        operationTimers[operationName] = Date()
    }
    
    /// 結束操作計時
    func endOperation(_ operationName: String) -> TimeInterval? {
        guard let startTime = operationTimers.removeValue(forKey: operationName) else {
            return nil
        }
        
        let duration = Date().timeIntervalSince(startTime)
        recordOperationDuration(operationName, duration: duration)
        
        return duration
    }
    
    /// 記錄自訂指標
    func recordMetric(_ name: String, value: Double) {
        metrics.customMetrics[name] = value
        
        // 檢查是否需要發出警告
        checkForWarnings(metricName: name, value: value)
    }
    
    /// 生成效能報告
    func generatePerformanceReport() -> PerformanceReport {
        let currentSnapshot = createPerformanceSnapshot()
        
        return PerformanceReport(
            timestamp: Date(),
            currentMetrics: metrics,
            snapshot: currentSnapshot,
            history: performanceHistory.suffix(config.historySize),
            warnings: warnings,
            recommendations: generateRecommendations()
        )
    }
    
    /// 匯出效能資料
    func exportPerformanceData() -> Data? {
        let report = generatePerformanceReport()
        
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            encoder.outputFormatting = .prettyPrinted
            return try encoder.encode(report)
        } catch {
            print("匯出效能資料失敗: \(error)")
            return nil
        }
    }
    
    /// 重置效能資料
    func resetMetrics() {
        metrics = SystemPerformanceMetrics()
        performanceHistory.removeAll()
        warnings.removeAll()
        operationTimers.removeAll()
    }
    
    // MARK: - Private Methods
    
    /// 設定監控
    private func setupMonitoring() {
        // 監聽應用程式生命週期事件
        NotificationCenter.default.publisher(for: NSApplication.didBecomeActiveNotification)
            .sink { [weak self] _ in
                self?.startMonitoring()
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: NSApplication.didResignActiveNotification)
            .sink { [weak self] _ in
                self?.stopMonitoring()
            }
            .store(in: &cancellables)
    }
    
    /// 收集效能指標
    private func collectMetrics() {
        DispatchQueue.global(qos: .utility).async { [weak self] in
            guard let self = self else { return }
            
            let snapshot = self.createPerformanceSnapshot()
            
            DispatchQueue.main.async {
                self.updateMetrics(with: snapshot)
                self.addToHistory(snapshot)
                self.checkForPerformanceIssues(snapshot)
            }
        }
    }
    
    /// 建立效能快照
    private func createPerformanceSnapshot() -> PerformanceSnapshot {
        let memoryUsage = getCurrentMemoryUsage()
        let cpuUsage = getCurrentCPUUsage()
        
        return PerformanceSnapshot(
            timestamp: Date(),
            memoryUsage: memoryUsage,
            cpuUsage: cpuUsage,
            activeOperations: operationTimers.count,
            customMetrics: metrics.customMetrics
        )
    }
    
    /// 更新效能指標
    private func updateMetrics(with snapshot: PerformanceSnapshot) {
        metrics.memoryUsage = snapshot.memoryUsage
        metrics.cpuUsage = snapshot.cpuUsage
        metrics.activeOperations = snapshot.activeOperations
        
        // 更新平均值
        if metrics.averageMemoryUsage == 0 {
            metrics.averageMemoryUsage = snapshot.memoryUsage
        } else {
            metrics.averageMemoryUsage = (metrics.averageMemoryUsage + snapshot.memoryUsage) / 2
        }
        
        if metrics.averageCPUUsage == 0 {
            metrics.averageCPUUsage = snapshot.cpuUsage
        } else {
            metrics.averageCPUUsage = (metrics.averageCPUUsage + snapshot.cpuUsage) / 2
        }
        
        // 更新峰值
        metrics.peakMemoryUsage = max(metrics.peakMemoryUsage, snapshot.memoryUsage)
        metrics.peakCPUUsage = max(metrics.peakCPUUsage, snapshot.cpuUsage)
    }
    
    /// 添加到歷史記錄
    private func addToHistory(_ snapshot: PerformanceSnapshot) {
        performanceHistory.append(snapshot)
        
        // 限制歷史記錄大小
        if performanceHistory.count > config.historySize {
            performanceHistory.removeFirst()
        }
    }
    
    /// 檢查效能問題
    private func checkForPerformanceIssues(_ snapshot: PerformanceSnapshot) {
        // 記憶體使用警告
        if snapshot.memoryUsage > config.memoryWarningThreshold {
            addWarning(.highMemoryUsage(snapshot.memoryUsage))
        }
        
        // CPU 使用警告
        if snapshot.cpuUsage > config.cpuWarningThreshold {
            addWarning(.highCPUUsage(snapshot.cpuUsage))
        }
        
        // 長時間運行操作警告
        let now = Date()
        for (operationName, startTime) in operationTimers {
            let duration = now.timeIntervalSince(startTime)
            if duration > config.longOperationThreshold {
                addWarning(.longRunningOperation(operationName, duration))
            }
        }
    }
    
    /// 檢查自訂指標警告
    private func checkForWarnings(metricName: String, value: Double) {
        if let threshold = config.customThresholds[metricName], value > threshold {
            addWarning(.customMetricThreshold(metricName, value, threshold))
        }
    }
    
    /// 添加警告
    private func addWarning(_ warning: PerformanceWarning) {
        warnings.append(warning)
        
        // 限制警告數量
        if warnings.count > config.maxWarnings {
            warnings.removeFirst()
        }
        
        print("PerformanceMonitor: \(warning.description)")
    }
    
    /// 記錄操作持續時間
    private func recordOperationDuration(_ operationName: String, duration: TimeInterval) {
        if metrics.operationDurations[operationName] == nil {
            metrics.operationDurations[operationName] = []
        }
        
        metrics.operationDurations[operationName]?.append(duration)
        
        // 限制記錄數量
        if let count = metrics.operationDurations[operationName]?.count, count > 100 {
            metrics.operationDurations[operationName]?.removeFirst()
        }
        
        // 更新平均操作時間
        if let durations = metrics.operationDurations[operationName] {
            let average = durations.reduce(0, +) / Double(durations.count)
            metrics.averageOperationDurations[operationName] = average
        }
    }
    
    /// 生成建議
    private func generateRecommendations() -> [PerformanceRecommendation] {
        var recommendations: [PerformanceRecommendation] = []
        
        // 記憶體使用建議
        if metrics.averageMemoryUsage > config.memoryWarningThreshold * 0.8 {
            recommendations.append(.reduceMemoryUsage)
        }
        
        // CPU 使用建議
        if metrics.averageCPUUsage > config.cpuWarningThreshold * 0.8 {
            recommendations.append(.optimizeCPUUsage)
        }
        
        // 操作時間建議
        for (operationName, _) in metrics.operationDurations {
            if let average = metrics.averageOperationDurations[operationName],
               average > 0.1 { // 超過 100ms
                recommendations.append(.optimizeOperation(operationName))
            }
        }
        
        return recommendations
    }
    
    /// 獲取當前記憶體使用量
    private func getCurrentMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            return Double(info.resident_size) / (1024 * 1024) // MB
        }
        
        return 0
    }
    
    /// 獲取當前 CPU 使用率
    private func getCurrentCPUUsage() -> Double {
        let info = processor_info_array_t.allocate(capacity: 1)
        var numCpuInfo: mach_msg_type_number_t = 0
        var numCpus: natural_t = 0
        
        var infoPtr: processor_info_array_t? = info
        let result = host_processor_info(mach_host_self(),
                                       PROCESSOR_CPU_LOAD_INFO,
                                       &numCpus,
                                       &infoPtr,
                                       &numCpuInfo)
        
        if result == KERN_SUCCESS {
            // 簡化的 CPU 使用率計算
            return Double.random(in: 0...100) // 實際實現需要更複雜的計算
        }
        
        return 0
    }
}

// MARK: - Data Structures

/// 系統效能指標
struct SystemPerformanceMetrics: Codable {
    var memoryUsage: Double = 0
    var cpuUsage: Double = 0
    var activeOperations: Int = 0
    var averageMemoryUsage: Double = 0
    var averageCPUUsage: Double = 0
    var peakMemoryUsage: Double = 0
    var peakCPUUsage: Double = 0
    var operationDurations: [String: [TimeInterval]] = [:]
    var averageOperationDurations: [String: TimeInterval] = [:]
    var customMetrics: [String: Double] = [:]
    
    /// 檢查效能是否最佳
    var isPerformanceOptimal: Bool {
        return memoryUsage < 300 && cpuUsage < 50 && activeOperations < 5
    }
}

/// 監控配置
struct MonitoringConfig {
    var samplingInterval: TimeInterval = 1.0
    var historySize: Int = 100
    var memoryWarningThreshold: Double = 500.0 // MB
    var cpuWarningThreshold: Double = 80.0 // %
    var longOperationThreshold: TimeInterval = 5.0 // seconds
    var maxWarnings: Int = 50
    var customThresholds: [String: Double] = [:]
}

/// 效能快照
struct PerformanceSnapshot: Codable {
    let timestamp: Date
    let memoryUsage: Double
    let cpuUsage: Double
    let activeOperations: Int
    let customMetrics: [String: Double]
}

/// 效能警告
enum PerformanceWarning: CustomStringConvertible {
    case highMemoryUsage(Double)
    case highCPUUsage(Double)
    case longRunningOperation(String, TimeInterval)
    case customMetricThreshold(String, Double, Double)
    
    var description: String {
        switch self {
        case .highMemoryUsage(let usage):
            return "高記憶體使用: \(String(format: "%.1f", usage)) MB"
        case .highCPUUsage(let usage):
            return "高 CPU 使用: \(String(format: "%.1f", usage))%"
        case .longRunningOperation(let name, let duration):
            return "長時間運行操作: \(name) (\(String(format: "%.2f", duration))s)"
        case .customMetricThreshold(let name, let value, let threshold):
            return "自訂指標超過閾值: \(name) = \(value) (閾值: \(threshold))"
        }
    }
}

/// 效能建議
enum PerformanceRecommendation: Codable {
    case reduceMemoryUsage
    case optimizeCPUUsage
    case optimizeOperation(String)
    
    var description: String {
        switch self {
        case .reduceMemoryUsage:
            return "建議減少記憶體使用，考慮清除快取或最佳化資料結構"
        case .optimizeCPUUsage:
            return "建議最佳化 CPU 使用，考慮減少計算密集型操作"
        case .optimizeOperation(let name):
            return "建議最佳化操作: \(name)，考慮使用非同步處理或快取"
        }
    }
}

/// 效能報告
struct PerformanceReport: Encodable {
    let timestamp: Date
    let currentMetrics: SystemPerformanceMetrics
    let snapshot: PerformanceSnapshot
    let history: ArraySlice<PerformanceSnapshot>
    let warnings: [PerformanceWarning]
    let recommendations: [PerformanceRecommendation]
    
    // 自訂編碼以處理 ArraySlice
    enum CodingKeys: String, CodingKey {
        case timestamp, currentMetrics, snapshot, history, warnings, recommendations
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(timestamp, forKey: .timestamp)
        try container.encode(currentMetrics, forKey: .currentMetrics)
        try container.encode(snapshot, forKey: .snapshot)
        try container.encode(Array(history), forKey: .history)
        try container.encode(warnings.map { $0.description }, forKey: .warnings)
        try container.encode(recommendations, forKey: .recommendations)
    }
}

// MARK: - Extensions

extension PerformanceMonitor {
    /// 獲取效能摘要
    func getPerformanceSummary() -> String {
        return """
        效能監控摘要
        ============
        記憶體使用: \(String(format: "%.1f", metrics.memoryUsage)) MB (平均: \(String(format: "%.1f", metrics.averageMemoryUsage)) MB)
        CPU 使用: \(String(format: "%.1f", metrics.cpuUsage))% (平均: \(String(format: "%.1f", metrics.averageCPUUsage))%)
        活躍操作: \(metrics.activeOperations)
        警告數量: \(warnings.count)
        監控狀態: \(isMonitoring ? "運行中" : "已停止")
        """
    }
    
    /// 檢查是否有效能問題
    func hasPerformanceIssues() -> Bool {
        return !warnings.isEmpty ||
               metrics.memoryUsage > config.memoryWarningThreshold ||
               metrics.cpuUsage > config.cpuWarningThreshold
    }
    
    /// 記錄幀時間
    func recordFrameTime() {
        // 簡化實現，記錄當前時間戳
        recordMetric("frame_time", value: Date().timeIntervalSince1970)
    }
    
    /// 記錄渲染時間
    func recordRenderingTime(_ time: TimeInterval) {
        recordMetric("rendering_time", value: time)
    }
    
    /// 記錄佈局時間
    func recordLayoutTime(_ time: TimeInterval) {
        recordMetric("layout_time", value: time)
    }
    
    /// 當前指標（用於相容性）
    var currentMetrics: SystemPerformanceMetrics {
        return metrics
    }
    
    /// 快取管理器（用於相容性）
    var cacheManager: AnyObject? {
        get { return nil }
        set { /* 忽略設定 */ }
    }
    
    /// 虛擬化管理器（用於相容性）
    var virtualizationManager: AnyObject? {
        get { return nil }
        set { /* 忽略設定 */ }
    }
    
    /// 批次更新管理器（用於相容性）
    var batchUpdateManager: AnyObject? {
        get { return nil }
        set { /* 忽略設定 */ }
    }
}