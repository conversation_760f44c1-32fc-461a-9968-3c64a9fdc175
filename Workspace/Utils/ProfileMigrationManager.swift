import Foundation

/// 管理設定檔資料遷移和向後相容性的工具類
class ProfileMigrationManager {
    static let shared = ProfileMigrationManager()
    
    private let layoutsPath: URL
    private let migrationStatusFile: URL
    private let backupPath: URL
    
    private init() {
        let homeURL = FileManager.default.homeDirectoryForCurrentUser
        layoutsPath = homeURL.appendingPathComponent(".hammerspoon/layouts")
        migrationStatusFile = layoutsPath.appendingPathComponent("migration_status.json")
        backupPath = layoutsPath.appendingPathComponent("backup")
        
        createDirectoriesIfNeeded()
    }
    
    // MARK: - Public Methods
    
    /// 執行完整的資料遷移流程
    func performMigration() -> MigrationResult {
        print("開始執行設定檔資料遷移...")
        
        // 檢查是否已經遷移過
        if isMigrationCompleted() {
            print("資料遷移已完成，跳過遷移流程")
            return .alreadyCompleted
        }
        
        // 建立備份
        let backupResult = createBackup()
        if case .failure(let error) = backupResult {
            print("建立備份失敗: \(error)")
            return .failed(error)
        }
        
        // 執行遷移
        do {
            let migrationSteps = [
                migrateLegacyProfiles,
                createSpaceMapping,
                validateMigratedData,
                cleanupLegacyFiles
            ]
            
            for step in migrationSteps {
                try step()
            }
            
            // 標記遷移完成
            markMigrationCompleted()
            
            print("資料遷移成功完成")
            return .success
            
        } catch {
            print("資料遷移失敗: \(error)")
            
            // 嘗試還原備份
            if case .success = restoreFromBackup() {
                print("已還原備份資料")
                return .failed(MigrationError.migrationFailedButRestored(error))
            } else {
                print("還原備份失敗")
                return .failed(MigrationError.migrationAndRestoreFailed(error))
            }
        }
    }
    
    /// 檢查是否需要遷移
    func needsMigration() -> Bool {
        return !isMigrationCompleted() && hasLegacyProfiles()
    }
    
    /// 獲取遷移狀態資訊
    func getMigrationStatus() -> MigrationStatus {
        if isMigrationCompleted() {
            return loadMigrationStatus() ?? MigrationStatus.completed()
        } else if hasLegacyProfiles() {
            return MigrationStatus.pending()
        } else {
            return MigrationStatus.notNeeded()
        }
    }
    
    // MARK: - Private Migration Steps
    
    /// 遷移舊版設定檔到新格式
    private func migrateLegacyProfiles() throws {
        print("開始遷移舊版設定檔...")
        
        let files = try FileManager.default.contentsOfDirectory(at: layoutsPath, includingPropertiesForKeys: nil)
        let jsonFiles = files.filter { $0.pathExtension == "json" && !isSystemFile($0) }
        
        var migratedCount = 0
        var errors: [String] = []
        
        for file in jsonFiles {
            do {
                if try migrateSingleProfile(from: file) {
                    migratedCount += 1
                }
            } catch {
                errors.append("遷移 \(file.lastPathComponent) 失敗: \(error)")
            }
        }
        
        print("成功遷移 \(migratedCount) 個設定檔")
        
        if !errors.isEmpty {
            print("遷移過程中發生的錯誤:")
            errors.forEach { print("  - \($0)") }
        }
    }
    
    /// 遷移單個設定檔
    private func migrateSingleProfile(from fileURL: URL) throws -> Bool {
        let data = try Data(contentsOf: fileURL)
        let fileName = fileURL.deletingPathExtension().lastPathComponent
        
        // 檢查是否已經是新格式
        if let _ = try? JSONDecoder().decode(Profile.self, from: data) {
            print("跳過已是新格式的設定檔: \(fileName)")
            return false
        }
        
        // 解析舊格式
        let profile = try parseLegacyProfile(data: data, fileName: fileName, fileURL: fileURL)
        
        // 儲存為新格式
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        let newData = try encoder.encode(profile)
        
        // 建立新檔案名稱
        let newFileName = profile.jsonFileName
        let newFileURL = layoutsPath.appendingPathComponent(newFileName)
        
        // 如果檔案名稱不同，需要重新命名
        if fileURL.lastPathComponent != newFileName {
            // 先寫入新檔案
            try newData.write(to: newFileURL)
            // 刪除舊檔案
            try FileManager.default.removeItem(at: fileURL)
            print("遷移並重新命名: \(fileName) -> \(newFileName)")
        } else {
            // 直接覆寫
            try newData.write(to: fileURL)
            print("遷移設定檔: \(fileName)")
        }
        
        return true
    }
    
    /// 解析舊格式設定檔
    private func parseLegacyProfile(data: Data, fileName: String, fileURL: URL) throws -> Profile {
        var name = fileName
        var spaceID: Int? = nil
        var isSpaceSpecific = false
        
        // 檢查是否為 Space-specific 檔案
        if fileName.hasSuffix("_space") || fileName.contains("_space") {
            isSpaceSpecific = true
            
            // 解析 Space ID
            if fileName.contains("_space") {
                let components = fileName.components(separatedBy: "_space")
                if components.count >= 2 {
                    name = components[0]
                    let spaceComponent = components[1]
                    if !spaceComponent.isEmpty {
                        spaceID = Int(spaceComponent)
                    }
                }
            }
        }
        
        let windows: [WindowLayout]
        
        if isSpaceSpecific {
            // 嘗試解析 Space-specific 格式
            if let spaceData = try? JSONDecoder().decode(SpaceLayoutData.self, from: data) {
                windows = spaceData.windows
                // 如果 SpaceLayoutData 包含 spaceID，使用它
                if spaceID == nil {
                    spaceID = spaceData.spaceID
                }
            } else {
                // 回退到普通格式
                windows = try JSONDecoder().decode([WindowLayout].self, from: data)
            }
        } else {
            // 解析普通格式（直接是 WindowLayout 陣列）
            windows = try JSONDecoder().decode([WindowLayout].self, from: data)
        }
        
        // 建立 Profile 實例
        var profile = Profile(name: name, windows: windows, isSpaceSpecific: isSpaceSpecific, spaceID: spaceID)
        
        // 設定檔案時間
        let attributes = try FileManager.default.attributesOfItem(atPath: fileURL.path)
        if let creationDate = attributes[.creationDate] as? Date {
            profile.createdAt = creationDate
        }
        if let modificationDate = attributes[.modificationDate] as? Date {
            profile.modifiedAt = modificationDate
        }
        
        return profile
    }
    
    /// 建立 Space 映射檔案
    private func createSpaceMapping() throws {
        print("建立 Space 映射檔案...")
        
        let mappingFileURL = layoutsPath.appendingPathComponent("space_profile_mapping.json")
        
        // 如果映射檔案已存在，跳過
        if FileManager.default.fileExists(atPath: mappingFileURL.path) {
            print("Space 映射檔案已存在，跳過建立")
            return
        }
        
        // 創建一個基本的映射檔案，SpaceProfileManager 初始化後會重建
        let basicMapping = SpaceProfileMappingData(
            spaceProfileMapping: [:],
            defaultSpace: 1,
            lastUpdated: Date()
        )
        
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(basicMapping)
            try data.write(to: mappingFileURL)
            print("Space 映射檔案建立完成")
        } catch {
            print("建立 Space 映射檔案失敗: \(error)")
            throw error
        }
    }
    
    /// 驗證遷移後的資料
    private func validateMigratedData() throws {
        print("驗證遷移後的資料...")
        
        // 重新載入設定檔
        ProfileManager.shared.loadProfiles()
        let profiles = ProfileManager.shared.profiles
        
        var validationErrors: [String] = []
        
        // 檢查每個設定檔
        for profile in profiles {
            // 檢查必要欄位
            if profile.name.isEmpty {
                validationErrors.append("設定檔名稱為空")
            }
            
            // 檢查 Space 資訊一致性
            if profile.isSpaceSpecific && profile.spaceID == nil {
                validationErrors.append("設定檔 '\(profile.name)' 標記為 Space 專用但缺少 Space ID")
            }
            
            // 檢查視窗資料
            if profile.windows?.isEmpty ?? true {
                print("警告: 設定檔 '\(profile.name)' 沒有視窗資料")
            }
        }
        
        // 檢查映射檔案
        let mappingFileURL = layoutsPath.appendingPathComponent("space_profile_mapping.json")
        if !FileManager.default.fileExists(atPath: mappingFileURL.path) {
            validationErrors.append("Space 映射檔案不存在")
        }
        
        if !validationErrors.isEmpty {
            let errorMessage = "資料驗證失敗:\n" + validationErrors.joined(separator: "\n")
            throw MigrationError.validationFailed(errorMessage)
        }
        
        print("資料驗證通過，共驗證 \(profiles.count) 個設定檔")
    }
    
    /// 清理舊版檔案（可選）
    private func cleanupLegacyFiles() throws {
        print("清理舊版檔案...")
        
        // 這裡可以選擇性地清理一些不再需要的舊檔案
        // 為了安全起見，我們暫時不刪除任何檔案
        
        print("舊版檔案清理完成")
    }
    
    // MARK: - Backup and Restore
    
    /// 建立資料備份
    private func createBackup() -> Result<Void, Error> {
        do {
            // 建立備份目錄
            if FileManager.default.fileExists(atPath: backupPath.path) {
                try FileManager.default.removeItem(at: backupPath)
            }
            try FileManager.default.createDirectory(at: backupPath, withIntermediateDirectories: true)
            
            // 複製所有 JSON 檔案
            let files = try FileManager.default.contentsOfDirectory(at: layoutsPath, includingPropertiesForKeys: nil)
            let jsonFiles = files.filter { $0.pathExtension == "json" }
            
            for file in jsonFiles {
                let backupFile = backupPath.appendingPathComponent(file.lastPathComponent)
                try FileManager.default.copyItem(at: file, to: backupFile)
            }
            
            // 建立備份資訊檔案
            let backupInfo = BackupInfo(
                createdAt: Date(),
                originalFileCount: jsonFiles.count,
                backupPath: backupPath.path
            )
            
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let backupInfoData = try encoder.encode(backupInfo)
            let backupInfoFile = backupPath.appendingPathComponent("backup_info.json")
            try backupInfoData.write(to: backupInfoFile)
            
            print("成功建立備份，共備份 \(jsonFiles.count) 個檔案")
            return .success(())
            
        } catch {
            return .failure(error)
        }
    }
    
    /// 從備份還原資料
    private func restoreFromBackup() -> Result<Void, Error> {
        do {
            guard FileManager.default.fileExists(atPath: backupPath.path) else {
                return .failure(MigrationError.backupNotFound)
            }
            
            // 讀取備份資訊
            let backupInfoFile = backupPath.appendingPathComponent("backup_info.json")
            let backupInfoData = try Data(contentsOf: backupInfoFile)
            let _ = try JSONDecoder().decode(BackupInfo.self, from: backupInfoData)
            
            // 清理當前檔案
            let currentFiles = try FileManager.default.contentsOfDirectory(at: layoutsPath, includingPropertiesForKeys: nil)
            let jsonFiles = currentFiles.filter { $0.pathExtension == "json" && !isSystemFile($0) }
            
            for file in jsonFiles {
                try FileManager.default.removeItem(at: file)
            }
            
            // 還原備份檔案
            let backupFiles = try FileManager.default.contentsOfDirectory(at: backupPath, includingPropertiesForKeys: nil)
            let backupJsonFiles = backupFiles.filter { $0.pathExtension == "json" && $0.lastPathComponent != "backup_info.json" }
            
            for backupFile in backupJsonFiles {
                let restoreFile = layoutsPath.appendingPathComponent(backupFile.lastPathComponent)
                try FileManager.default.copyItem(at: backupFile, to: restoreFile)
            }
            
            print("成功還原備份，共還原 \(backupJsonFiles.count) 個檔案")
            return .success(())
            
        } catch {
            return .failure(error)
        }
    }
    
    // MARK: - Migration Status Management
    
    /// 檢查遷移是否已完成
    private func isMigrationCompleted() -> Bool {
        guard FileManager.default.fileExists(atPath: migrationStatusFile.path) else {
            return false
        }
        
        do {
            let data = try Data(contentsOf: migrationStatusFile)
            let status = try JSONDecoder().decode(MigrationStatus.self, from: data)
            return status.isCompleted
        } catch {
            print("讀取遷移狀態失敗: \(error)")
            return false
        }
    }
    
    /// 載入遷移狀態
    private func loadMigrationStatus() -> MigrationStatus? {
        guard FileManager.default.fileExists(atPath: migrationStatusFile.path) else {
            return nil
        }
        
        do {
            let data = try Data(contentsOf: migrationStatusFile)
            return try JSONDecoder().decode(MigrationStatus.self, from: data)
        } catch {
            print("載入遷移狀態失敗: \(error)")
            return nil
        }
    }
    
    /// 標記遷移完成
    private func markMigrationCompleted() {
        let status = MigrationStatus.completed()
        
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(status)
            try data.write(to: migrationStatusFile)
        } catch {
            print("儲存遷移狀態失敗: \(error)")
        }
    }
    
    /// 檢查是否有舊版設定檔
    private func hasLegacyProfiles() -> Bool {
        do {
            let files = try FileManager.default.contentsOfDirectory(at: layoutsPath, includingPropertiesForKeys: nil)
            let jsonFiles = files.filter { $0.pathExtension == "json" && !isSystemFile($0) }
            
            for file in jsonFiles {
                let data = try Data(contentsOf: file)
                // 如果無法解析為新格式，則認為是舊版
                if (try? JSONDecoder().decode(Profile.self, from: data)) == nil {
                    return true
                }
            }
            
            return false
        } catch {
            print("檢查舊版設定檔失敗: \(error)")
            return false
        }
    }
    
    /// 檢查是否為系統檔案
    private func isSystemFile(_ url: URL) -> Bool {
        let fileName = url.lastPathComponent
        return fileName == "migration_status.json" || 
               fileName == "space_profile_mapping.json" ||
               fileName.hasPrefix("backup_")
    }
    
    /// 建立必要的目錄
    private func createDirectoriesIfNeeded() {
        if !FileManager.default.fileExists(atPath: layoutsPath.path) {
            try? FileManager.default.createDirectory(at: layoutsPath, withIntermediateDirectories: true)
        }
    }
}

// MARK: - Data Structures

/// 遷移結果
enum MigrationResult {
    case success
    case alreadyCompleted
    case failed(Error)
}

/// 遷移錯誤
enum MigrationError: LocalizedError {
    case validationFailed(String)
    case backupNotFound
    case migrationFailedButRestored(Error)
    case migrationAndRestoreFailed(Error)
    
    var errorDescription: String? {
        switch self {
        case .validationFailed(let message):
            return "資料驗證失敗: \(message)"
        case .backupNotFound:
            return "找不到備份檔案"
        case .migrationFailedButRestored(let error):
            return "遷移失敗但已還原備份: \(error.localizedDescription)"
        case .migrationAndRestoreFailed(let error):
            return "遷移失敗且還原備份也失敗: \(error.localizedDescription)"
        }
    }
}

/// 遷移狀態
struct MigrationStatus: Codable {
    let isCompleted: Bool
    let completedAt: Date?
    let version: String
    let migratedProfileCount: Int?
    let notes: String?
    
    static func completed() -> MigrationStatus {
        return MigrationStatus(
            isCompleted: true,
            completedAt: Date(),
            version: "1.0",
            migratedProfileCount: nil,
            notes: "自動遷移完成"
        )
    }
    
    static func pending() -> MigrationStatus {
        return MigrationStatus(
            isCompleted: false,
            completedAt: nil,
            version: "1.0",
            migratedProfileCount: nil,
            notes: "等待遷移"
        )
    }
    
    static func notNeeded() -> MigrationStatus {
        return MigrationStatus(
            isCompleted: true,
            completedAt: Date(),
            version: "1.0",
            migratedProfileCount: 0,
            notes: "無需遷移"
        )
    }
}

/// 備份資訊
private struct BackupInfo: Codable {
    let createdAt: Date
    let originalFileCount: Int
    let backupPath: String
}