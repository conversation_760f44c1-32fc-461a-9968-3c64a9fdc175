import SwiftUI
import Combine

@MainActor
class PreviewConfigurationManager: ObservableObject {
    static let shared = PreviewConfigurationManager()
    
    @Published var currentConfiguration = PreviewConfiguration.default
    @Published var lastSelectedWindowId: String?
    
    private init() {}
    
    func updateConfiguration(_ configuration: PreviewConfiguration) {
        currentConfiguration = configuration
    }
    
    func updatePreviewMode(_ mode: PreviewMode) {
        // Configuration updated based on mode
    }
    
    func optimizeConfiguration(for windowCount: Int) {
        if windowCount > 100 {
            currentConfiguration.enableAnimations = false
            currentConfiguration.maxWindowsPerView = 50
        }
    }
}