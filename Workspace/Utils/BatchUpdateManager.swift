import Foundation
import SwiftUI
import Combine

// MARK: - BatchUpdateManager
/// 管理批次狀態更新，減少不必要的重新渲染
@MainActor
class BatchUpdateManager: ObservableObject {
    
    // MARK: - Update Types
    enum UpdateType: String, CaseIterable {
        case windowSelection = "windowSelection"
        case windowLayout = "windowLayout"
        case previewMode = "previewMode"
        case configuration = "configuration"
        case visibility = "visibility"
        
        var priority: Int {
            switch self {
            case .previewMode: return 0 // 最高優先級
            case .configuration: return 1
            case .windowSelection: return 2
            case .visibility: return 3
            case .windowLayout: return 4 // 最低優先級
            }
        }
    }
    
    // MARK: - Batch Update
    struct BatchUpdate {
        let id = UUID()
        let type: UpdateType
        let timestamp: Date
        let data: Any
        let completion: (() -> Void)?
        
        init(type: UpdateType, data: Any, completion: (() -> Void)? = nil) {
            self.type = type
            self.data = data
            self.timestamp = Date()
            self.completion = completion
        }
    }
    
    // MARK: - Configuration
    struct BatchConfig {
        let batchInterval: TimeInterval // 批次處理間隔
        let maxBatchSize: Int // 最大批次大小
        let enableCoalescing: Bool // 是否啟用合併
        let priorityThreshold: TimeInterval // 高優先級更新的閾值
        
        static let `default` = BatchConfig(
            batchInterval: 0.016, // ~60fps
            maxBatchSize: 50,
            enableCoalescing: true,
            priorityThreshold: 0.1 // 100ms
        )
    }
    
    // MARK: - Properties
    private var pendingUpdates: [BatchUpdate] = []
    private var processingUpdates: [BatchUpdate] = []
    private let config: BatchConfig
    private let updateQueue = DispatchQueue(label: "batch.update", qos: .userInitiated)
    private var batchTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Published Properties
    @Published var isProcessing: Bool = false
    @Published var batchMetrics = BatchMetrics()
    
    // MARK: - Callbacks
    var onWindowSelectionUpdate: (([String]) -> Void)?
    var onWindowLayoutUpdate: (([WindowLayout]) -> Void)?
    var onPreviewModeUpdate: ((PreviewMode) -> Void)?
    var onConfigurationUpdate: ((PreviewConfiguration) -> Void)?
    var onVisibilityUpdate: ((Bool) -> Void)?
    
    init(config: BatchConfig = .default) {
        self.config = config
        setupBatchTimer()
    }
    
    deinit {
        batchTimer?.invalidate()
    }
    
    // MARK: - Public Methods
    
    /// 添加更新到批次隊列
    func addUpdate(type: UpdateType, data: Any, completion: (() -> Void)? = nil) {
        updateQueue.async { [weak self] in
            guard let self = self else { return }
            
            let update = BatchUpdate(type: type, data: data, completion: completion)
            
            // 如果啟用合併，檢查是否可以合併相同類型的更新
            if self.config.enableCoalescing {
                self.coalesceUpdate(update)
            } else {
                self.pendingUpdates.append(update)
            }
            
            // 檢查是否需要立即處理高優先級更新
            if self.shouldProcessImmediately(update) {
                DispatchQueue.main.async {
                    self.processBatch()
                }
            }
            
            DispatchQueue.main.async {
                self.batchMetrics.totalUpdatesQueued += 1
            }
        }
    }
    
    /// 立即處理所有待處理的更新
    func flushUpdates() {
        updateQueue.async { [weak self] in
            DispatchQueue.main.async {
                self?.processBatch()
            }
        }
    }
    
    /// 清除所有待處理的更新
    func clearPendingUpdates() {
        updateQueue.async { [weak self] in
            guard let self = self else { return }
            
            let clearedCount = self.pendingUpdates.count
            self.pendingUpdates.removeAll()
            
            DispatchQueue.main.async {
                self.batchMetrics.updatesCleared += clearedCount
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func setupBatchTimer() {
        batchTimer = Timer.scheduledTimer(withTimeInterval: config.batchInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.processBatch()
            }
        }
    }
    
    private func coalesceUpdate(_ newUpdate: BatchUpdate) {
        // 查找相同類型的現有更新
        if let existingIndex = pendingUpdates.firstIndex(where: { $0.type == newUpdate.type }) {
            let existingUpdate = pendingUpdates[existingIndex]
            
            // 根據更新類型決定如何合併
            let coalescedUpdate = coalesceUpdates(existing: existingUpdate, new: newUpdate)
            pendingUpdates[existingIndex] = coalescedUpdate
            
            batchMetrics.updatesCoalesced += 1
        } else {
            pendingUpdates.append(newUpdate)
        }
    }
    
    private func coalesceUpdates(existing: BatchUpdate, new: BatchUpdate) -> BatchUpdate {
        switch new.type {
        case .windowSelection:
            // 視窗選擇更新：使用最新的選擇狀態
            return new
            
        case .windowLayout:
            // 視窗佈局更新：使用最新的佈局數據
            return new
            
        case .previewMode:
            // 預覽模式更新：使用最新的模式
            return new
            
        case .configuration:
            // 配置更新：合併配置變更
            if let _ = existing.data as? PreviewConfiguration,
               let _ = new.data as? PreviewConfiguration {
                // 創建合併的配置（這裡簡化為使用新配置）
                return new
            }
            return new
            
        case .visibility:
            // 可見性更新：使用最新的可見性狀態
            return new
        }
    }
    
    private func shouldProcessImmediately(_ update: BatchUpdate) -> Bool {
        // 高優先級更新或者批次大小超過限制時立即處理
        return update.type.priority <= 1 || pendingUpdates.count >= config.maxBatchSize
    }
    
    @MainActor
    private func processBatch() {
        guard !pendingUpdates.isEmpty && !isProcessing else { return }
        
        isProcessing = true
        let startTime = Date()
        
        updateQueue.async { [weak self] in
            guard let self = self else { return }
            
            // 移動待處理的更新到處理隊列
            self.processingUpdates = self.pendingUpdates
            self.pendingUpdates.removeAll()
            
            // 按優先級排序
            self.processingUpdates.sort { $0.type.priority < $1.type.priority }
            
            DispatchQueue.main.async {
                self.executeUpdates()
                
                let processingTime = Date().timeIntervalSince(startTime)
                self.batchMetrics.lastBatchProcessingTime = processingTime
                self.batchMetrics.totalBatchesProcessed += 1
                self.batchMetrics.averageProcessingTime = (self.batchMetrics.averageProcessingTime * Double(self.batchMetrics.totalBatchesProcessed - 1) + processingTime) / Double(self.batchMetrics.totalBatchesProcessed)
                
                self.isProcessing = false
                self.processingUpdates.removeAll()
            }
        }
    }
    
    private func executeUpdates() {
        var executedUpdates: [UpdateType: Any] = [:]
        var completions: [() -> Void] = []
        
        // 收集所有更新，相同類型的只保留最後一個
        for update in processingUpdates {
            executedUpdates[update.type] = update.data
            if let completion = update.completion {
                completions.append(completion)
            }
        }
        
        // 執行更新回調
        for (type, data) in executedUpdates {
            switch type {
            case .windowSelection:
                if let selectedWindows = data as? [String] {
                    onWindowSelectionUpdate?(selectedWindows)
                }
                
            case .windowLayout:
                if let windows = data as? [WindowLayout] {
                    onWindowLayoutUpdate?(windows)
                }
                
            case .previewMode:
                if let mode = data as? PreviewMode {
                    onPreviewModeUpdate?(mode)
                }
                
            case .configuration:
                if let config = data as? PreviewConfiguration {
                    onConfigurationUpdate?(config)
                }
                
            case .visibility:
                if let isVisible = data as? Bool {
                    onVisibilityUpdate?(isVisible)
                }
            }
        }
        
        // 執行完成回調
        for completion in completions {
            completion()
        }
        
        batchMetrics.updatesExecuted += executedUpdates.count
    }
    
    // MARK: - Performance Monitoring
    
    /// 獲取性能指標
    func getPerformanceMetrics() -> BatchMetrics {
        return batchMetrics
    }
    
    /// 檢查是否需要調整批次配置
    func shouldAdjustBatchConfig() -> Bool {
        return batchMetrics.averageProcessingTime > config.batchInterval * 2.0
    }
    
    /// 自動調整批次配置
    func adjustBatchConfig() {
        guard shouldAdjustBatchConfig() else { return }
        
        // 如果處理時間過長，減少批次大小
        let newBatchSize = max(10, config.maxBatchSize / 2)
        let _ = BatchConfig(
            batchInterval: config.batchInterval,
            maxBatchSize: newBatchSize,
            enableCoalescing: config.enableCoalescing,
            priorityThreshold: config.priorityThreshold
        )
        
        // 這裡可以更新配置，但為了簡化，我們只記錄調整
        batchMetrics.configAdjustments += 1
    }
}

// MARK: - BatchMetrics
struct BatchMetrics {
    var totalUpdatesQueued: Int = 0
    var updatesExecuted: Int = 0
    var updatesCoalesced: Int = 0
    var updatesCleared: Int = 0
    var totalBatchesProcessed: Int = 0
    var lastBatchProcessingTime: TimeInterval = 0
    var averageProcessingTime: TimeInterval = 0
    var configAdjustments: Int = 0
    
    var coalescingEfficiency: Double {
        let totalProcessed = updatesExecuted + updatesCoalesced
        return totalProcessed > 0 ? Double(updatesCoalesced) / Double(totalProcessed) : 0
    }
    
    var processingEfficiency: Double {
        return totalUpdatesQueued > 0 ? Double(updatesExecuted) / Double(totalUpdatesQueued) : 0
    }
    
    var description: String {
        return """
        批次處理指標:
        - 總排隊更新: \(totalUpdatesQueued)
        - 已執行更新: \(updatesExecuted)
        - 合併更新: \(updatesCoalesced)
        - 清理更新: \(updatesCleared)
        - 處理批次數: \(totalBatchesProcessed)
        - 平均處理時間: \(String(format: "%.3f", averageProcessingTime))s
        - 合併效率: \(String(format: "%.1f", coalescingEfficiency * 100))%
        - 處理效率: \(String(format: "%.1f", processingEfficiency * 100))%
        """
    }
}

// MARK: - Batch Update Extensions
extension BatchUpdateManager {
    
    /// 便捷方法：更新視窗選擇
    func updateWindowSelection(_ selectedWindows: [String], completion: (() -> Void)? = nil) {
        addUpdate(type: .windowSelection, data: selectedWindows, completion: completion)
    }
    
    /// 便捷方法：更新視窗佈局
    func updateWindowLayout(_ windows: [WindowLayout], completion: (() -> Void)? = nil) {
        addUpdate(type: .windowLayout, data: windows, completion: completion)
    }
    
    /// 便捷方法：更新預覽模式
    func updatePreviewMode(_ mode: PreviewMode, completion: (() -> Void)? = nil) {
        addUpdate(type: .previewMode, data: mode, completion: completion)
    }
    
    /// 便捷方法：更新配置
    func updateConfiguration(_ config: PreviewConfiguration, completion: (() -> Void)? = nil) {
        addUpdate(type: .configuration, data: config, completion: completion)
    }
    
    /// 便捷方法：更新可見性
    func updateVisibility(_ isVisible: Bool, completion: (() -> Void)? = nil) {
        addUpdate(type: .visibility, data: isVisible, completion: completion)
    }
}

// MARK: - Debounced Publisher
extension BatchUpdateManager {
    
    /// 創建防抖動的發布者
    func debouncedPublisher<T>(for keyPath: KeyPath<BatchUpdateManager, T>, interval: TimeInterval = 0.3) -> AnyPublisher<T, Never> where T: Equatable {
        return $isProcessing
            .debounce(for: .seconds(interval), scheduler: RunLoop.main)
            .removeDuplicates()
            .map { _ in self[keyPath: keyPath] }
            .eraseToAnyPublisher()
    }
    
    /// 創建節流的發布者
    func throttledPublisher<T>(for keyPath: KeyPath<BatchUpdateManager, T>, interval: TimeInterval = 0.1) -> AnyPublisher<T, Never> where T: Equatable {
        return $isProcessing
            .throttle(for: .seconds(interval), scheduler: RunLoop.main, latest: true)
            .removeDuplicates()
            .map { _ in self[keyPath: keyPath] }
            .eraseToAnyPublisher()
    }
}