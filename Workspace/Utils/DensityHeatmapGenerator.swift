import Foundation
import SwiftUI

// MARK: - DensityRegion 密度區域
/// 密度區域數據結構，用於表示熱力圖中的一個區域
struct DensityRegion: Identifiable {
    let id = UUID()
    let bounds: CGRect
    let windowCount: Int
    let density: Double
}

// MARK: - DensityHeatmapGenerator 密度熱力圖生成器
/// 負責生成視窗密度熱力圖的工具類
class DensityHeatmapGenerator {
    
    // MARK: - 配置結構
    struct HeatmapConfiguration {
        let gridSize: CGFloat
        let smoothingRadius: CGFloat
        let intensityMultiplier: Double
        let colorScheme: ColorScheme
        let showLabels: Bool
        let minDensityThreshold: Double
        
        enum ColorScheme {
            case thermal    // 熱力圖配色（藍-綠-黃-紅）
            case density    // 密度配色（透明-藍-紫）
            case activity   // 活動配色（綠-黃-橙-紅）
            case monochrome // 單色配色（灰度）
        }
        
        static let `default` = HeatmapConfiguration(
            gridSize: 100,
            smoothingRadius: 50,
            intensityMultiplier: 1.0,
            colorScheme: .thermal,
            showLabels: true,
            minDensityThreshold: 0.1
        )
    }
    
    // MARK: - 熱力圖數據結構
    struct HeatmapData {
        let regions: [DensityRegion]
        let maxDensity: Double
        let averageDensity: Double
        let totalCoverage: Double
        let hotspots: [Hotspot]
    }
    
    struct Hotspot: Identifiable {
        let id = UUID()
        let center: CGPoint
        let intensity: Double
        let radius: CGFloat
        let windowCount: Int
    }
    
    // MARK: - 主要生成方法
    
    /// 生成視窗密度熱力圖
    /// - Parameters:
    ///   - windows: 視窗佈局數組
    ///   - bounds: 總邊界範圍
    ///   - configuration: 熱力圖配置
    /// - Returns: 熱力圖數據
    static func generateHeatmap(
        for windows: [WindowLayout],
        in bounds: CGRect,
        configuration: DensityHeatmapGenerator.HeatmapConfiguration = .default
    ) -> DensityHeatmapGenerator.HeatmapData {
        
        guard !windows.isEmpty && bounds.width > 0 && bounds.height > 0 else {
            return DensityHeatmapGenerator.HeatmapData(regions: [], maxDensity: 0, averageDensity: 0, totalCoverage: 0, hotspots: [])
        }
        
        // 1. 創建密度網格
        let densityGrid = createDensityGrid(
            windows: windows,
            bounds: bounds,
            gridSize: configuration.gridSize
        )
        
        // 2. 應用平滑處理
        let smoothedGrid = applySmoothingFilter(
            grid: densityGrid,
            radius: configuration.smoothingRadius,
            gridSize: configuration.gridSize
        )
        
        // 3. 生成密度區域
        let regions = generateDensityRegions(
            from: smoothedGrid,
            bounds: bounds,
            gridSize: configuration.gridSize,
            configuration: configuration
        )
        
        // 4. 檢測熱點
        let hotspots = detectHotspots(
            from: smoothedGrid,
            bounds: bounds,
            gridSize: configuration.gridSize,
            windows: windows
        )
        
        // 5. 計算統計信息
        let statistics = calculateHeatmapStatistics(regions: regions, bounds: bounds)
        
        return DensityHeatmapGenerator.HeatmapData(
            regions: regions,
            maxDensity: statistics.maxDensity,
            averageDensity: statistics.averageDensity,
            totalCoverage: statistics.totalCoverage,
            hotspots: hotspots
        )
    }
    
    // MARK: - 私有輔助方法
    
    /// 創建密度網格
    private static func createDensityGrid(
        windows: [WindowLayout],
        bounds: CGRect,
        gridSize: CGFloat
    ) -> [[Double]] {
        
        let cols = Int(ceil(bounds.width / gridSize))
        let rows = Int(ceil(bounds.height / gridSize))
        
        var grid = Array(repeating: Array(repeating: 0.0, count: cols), count: rows)
        
        for window in windows {
            let windowFrame = CGRect(
                x: window.frame.x,
                y: window.frame.y,
                width: window.frame.w,
                height: window.frame.h
            )
            
            // 計算視窗覆蓋的網格範圍
            let startCol = max(0, Int((windowFrame.minX - bounds.minX) / gridSize))
            let endCol = min(cols - 1, Int((windowFrame.maxX - bounds.minX) / gridSize))
            let startRow = max(0, Int((windowFrame.minY - bounds.minY) / gridSize))
            let endRow = min(rows - 1, Int((windowFrame.maxY - bounds.minY) / gridSize))
            
            // 計算視窗對每個網格的貢獻
            for row in startRow...endRow {
                for col in startCol...endCol {
                    let cellBounds = CGRect(
                        x: bounds.minX + CGFloat(col) * gridSize,
                        y: bounds.minY + CGFloat(row) * gridSize,
                        width: gridSize,
                        height: gridSize
                    )
                    
                    // 計算重疊面積比例
                    let intersection = windowFrame.intersection(cellBounds)
                    let overlapRatio = intersection.area / cellBounds.area
                    
                    // 根據視窗大小調整權重
                    let windowArea = windowFrame.area
                    let sizeWeight = calculateSizeWeight(windowArea)
                    
                    grid[row][col] += overlapRatio * sizeWeight
                }
            }
        }
        
        return grid
    }
    
    /// 計算視窗大小權重
    private static func calculateSizeWeight(_ area: CGFloat) -> Double {
        // 標準視窗大小約為 800x600 = 480,000
        let standardArea: CGFloat = 480000
        let ratio = area / standardArea
        
        // 使用對數函數避免極大視窗過度影響密度
        return max(0.1, min(2.0, log(ratio + 1) + 0.5))
    }
    
    /// 應用平滑濾波器
    private static func applySmoothingFilter(
        grid: [[Double]],
        radius: CGFloat,
        gridSize: CGFloat
    ) -> [[Double]] {
        
        let rows = grid.count
        let cols = grid[0].count
        let kernelSize = Int(ceil(radius / gridSize))
        
        var smoothedGrid = grid
        
        // 高斯核
        let kernel = createGaussianKernel(size: kernelSize, sigma: Double(kernelSize) / 3.0)
        
        for row in 0..<rows {
            for col in 0..<cols {
                var sum = 0.0
                var weightSum = 0.0
                
                for kr in -kernelSize...kernelSize {
                    for kc in -kernelSize...kernelSize {
                        let r = row + kr
                        let c = col + kc
                        
                        if r >= 0 && r < rows && c >= 0 && c < cols {
                            let weight = kernel[kr + kernelSize][kc + kernelSize]
                            sum += grid[r][c] * weight
                            weightSum += weight
                        }
                    }
                }
                
                smoothedGrid[row][col] = weightSum > 0 ? sum / weightSum : 0
            }
        }
        
        return smoothedGrid
    }
    
    /// 創建高斯核
    private static func createGaussianKernel(size: Int, sigma: Double) -> [[Double]] {
        let kernelSize = 2 * size + 1
        var kernel = Array(repeating: Array(repeating: 0.0, count: kernelSize), count: kernelSize)
        
        let twoSigmaSquared = 2.0 * sigma * sigma
        var sum = 0.0
        
        for i in 0..<kernelSize {
            for j in 0..<kernelSize {
                let x = Double(i - size)
                let y = Double(j - size)
                let value = exp(-(x * x + y * y) / twoSigmaSquared)
                kernel[i][j] = value
                sum += value
            }
        }
        
        // 正規化
        for i in 0..<kernelSize {
            for j in 0..<kernelSize {
                kernel[i][j] /= sum
            }
        }
        
        return kernel
    }
    
    /// 生成密度區域
    private static func generateDensityRegions(
        from grid: [[Double]],
        bounds: CGRect,
        gridSize: CGFloat,
        configuration: DensityHeatmapGenerator.HeatmapConfiguration
    ) -> [DensityRegion] {
        
        let rows = grid.count
        let cols = grid[0].count
        var regions: [DensityRegion] = []
        
        for row in 0..<rows {
            for col in 0..<cols {
                let density = grid[row][col] * configuration.intensityMultiplier
                
                if density > configuration.minDensityThreshold {
                    let regionBounds = CGRect(
                        x: bounds.minX + CGFloat(col) * gridSize,
                        y: bounds.minY + CGFloat(row) * gridSize,
                        width: gridSize,
                        height: gridSize
                    )
                    
                    // 估算此區域的視窗數量
                    let estimatedWindowCount = max(1, Int(round(density)))
                    
                    regions.append(DensityRegion(
                        bounds: regionBounds,
                        windowCount: estimatedWindowCount,
                        density: density
                    ))
                }
            }
        }
        
        return regions
    }
    
    /// 檢測熱點
    private static func detectHotspots(
        from grid: [[Double]],
        bounds: CGRect,
        gridSize: CGFloat,
        windows: [WindowLayout]
    ) -> [DensityHeatmapGenerator.Hotspot] {
        
        let rows = grid.count
        let cols = grid[0].count
        var hotspots: [DensityHeatmapGenerator.Hotspot] = []
        
        // 尋找局部最大值
        for row in 1..<(rows-1) {
            for col in 1..<(cols-1) {
                let currentDensity = grid[row][col]
                
                // 檢查是否為局部最大值
                var isLocalMaximum = true
                var neighborSum = 0.0
                var neighborCount = 0
                
                for dr in -1...1 {
                    for dc in -1...1 {
                        if dr == 0 && dc == 0 { continue }
                        
                        let neighborDensity = grid[row + dr][col + dc]
                        neighborSum += neighborDensity
                        neighborCount += 1
                        
                        if neighborDensity >= currentDensity {
                            isLocalMaximum = false
                        }
                    }
                }
                
                // 如果是局部最大值且密度足夠高
                if isLocalMaximum && currentDensity > 1.0 {
                    let center = CGPoint(
                        x: bounds.minX + (CGFloat(col) + 0.5) * gridSize,
                        y: bounds.minY + (CGFloat(row) + 0.5) * gridSize
                    )
                    
                    // 計算熱點半徑
                    let radius = calculateHotspotRadius(
                        center: center,
                        density: currentDensity,
                        gridSize: gridSize
                    )
                    
                    // 計算此熱點區域的實際視窗數量
                    let windowCount = countWindowsInRadius(
                        center: center,
                        radius: radius,
                        windows: windows
                    )
                    
                    hotspots.append(DensityHeatmapGenerator.Hotspot(
                        center: center,
                        intensity: currentDensity,
                        radius: radius,
                        windowCount: windowCount
                    ))
                }
            }
        }
        
        // 按強度排序，只保留最重要的熱點
        return Array(hotspots.sorted { $0.intensity > $1.intensity }.prefix(10))
    }
    
    /// 計算熱點半徑
    private static func calculateHotspotRadius(
        center: CGPoint,
        density: Double,
        gridSize: CGFloat
    ) -> CGFloat {
        // 基於密度計算半徑
        let baseRadius = gridSize * 1.5
        let densityFactor = sqrt(density)
        return baseRadius * CGFloat(densityFactor)
    }
    
    /// 計算半徑內的視窗數量
    private static func countWindowsInRadius(
        center: CGPoint,
        radius: CGFloat,
        windows: [WindowLayout]
    ) -> Int {
        return windows.filter { window in
            let windowCenter = CGPoint(
                x: window.frame.x + window.frame.w / 2,
                y: window.frame.y + window.frame.h / 2
            )
            
            let distance = sqrt(
                pow(windowCenter.x - center.x, 2) +
                pow(windowCenter.y - center.y, 2)
            )
            
            return distance <= radius
        }.count
    }
    
    /// 計算熱力圖統計信息
    private static func calculateHeatmapStatistics(
        regions: [DensityRegion],
        bounds: CGRect
    ) -> (maxDensity: Double, averageDensity: Double, totalCoverage: Double) {
        
        guard !regions.isEmpty else {
            return (0, 0, 0)
        }
        
        let densities = regions.map { $0.density }
        let maxDensity = densities.max() ?? 0
        let averageDensity = densities.reduce(0, +) / Double(densities.count)
        
        let totalRegionArea = regions.reduce(0.0) { result, region in
            result + Double(region.bounds.area)
        }
        let totalCoverage = totalRegionArea / Double(bounds.area)
        
        return (maxDensity, averageDensity, totalCoverage)
    }
}

// MARK: - 顏色生成擴展
extension DensityHeatmapGenerator {
    
    /// 根據密度和配色方案生成顏色
    static func colorForDensity(
        _ density: Double,
        maxDensity: Double,
        colorScheme: DensityHeatmapGenerator.HeatmapConfiguration.ColorScheme
    ) -> Color {
        
        let normalizedDensity = maxDensity > 0 ? min(1.0, density / maxDensity) : 0
        
        switch colorScheme {
        case .thermal:
            return thermalColor(for: normalizedDensity)
        case .density:
            return densityColor(for: normalizedDensity)
        case .activity:
            return activityColor(for: normalizedDensity)
        case .monochrome:
            return monochromeColor(for: normalizedDensity)
        }
    }
    
    /// 熱力圖配色
    private static func thermalColor(for value: Double) -> Color {
        if value < 0.25 {
            // 藍色到青色
            let t = value / 0.25
            return Color(red: 0, green: t * 0.5, blue: 1.0 - t * 0.5)
        } else if value < 0.5 {
            // 青色到綠色
            let t = (value - 0.25) / 0.25
            return Color(red: 0, green: 0.5 + t * 0.5, blue: 0.5 - t * 0.5)
        } else if value < 0.75 {
            // 綠色到黃色
            let t = (value - 0.5) / 0.25
            return Color(red: t, green: 1.0, blue: 0)
        } else {
            // 黃色到紅色
            let t = (value - 0.75) / 0.25
            return Color(red: 1.0, green: 1.0 - t, blue: 0)
        }
    }
    
    /// 密度配色
    private static func densityColor(for value: Double) -> Color {
        return Color(red: 0.2, green: 0.4 + value * 0.6, blue: 1.0, opacity: value * 0.8)
    }
    
    /// 活動配色
    private static func activityColor(for value: Double) -> Color {
        if value < 0.5 {
            let t = value / 0.5
            return Color(red: t, green: 1.0, blue: 0, opacity: 0.3 + value * 0.5)
        } else {
            let t = (value - 0.5) / 0.5
            return Color(red: 1.0, green: 1.0 - t * 0.5, blue: 0, opacity: 0.3 + value * 0.5)
        }
    }
    
    /// 單色配色
    private static func monochromeColor(for value: Double) -> Color {
        return Color(white: value, opacity: 0.3 + value * 0.5)
    }
}

// MARK: - 視覺化輔助方法
extension DensityHeatmapGenerator {
    
    /// 生成熱力圖圖例
    static func generateLegend(
        maxDensity: Double,
        colorScheme: DensityHeatmapGenerator.HeatmapConfiguration.ColorScheme,
        size: CGSize = CGSize(width: 200, height: 20)
    ) -> some View {
        
        return HStack(spacing: 0) {
            ForEach(0..<20, id: \.self) { index in
                let value = Double(index) / 19.0
                let color = colorForDensity(value, maxDensity: 1.0, colorScheme: colorScheme)
                
                Rectangle()
                    .fill(color)
                    .frame(width: size.width / 20, height: size.height)
            }
        }
        .overlay(
            HStack {
                Text("低")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                Spacer()
                Text("高")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 4)
        )
    }
    
    /// 生成熱力圖統計視圖
    static func generateStatisticsView(data: DensityHeatmapGenerator.HeatmapData) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("熱力圖統計")
                .font(.headline)
            
            HStack {
                Text("最大密度:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
                Text(String(format: "%.2f", data.maxDensity))
                    .font(.caption)
                    .fontWeight(.medium)
            }
            
            HStack {
                Text("平均密度:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
                Text(String(format: "%.2f", data.averageDensity))
                    .font(.caption)
                    .fontWeight(.medium)
            }
            
            HStack {
                Text("覆蓋率:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
                Text(String(format: "%.1f%%", data.totalCoverage * 100))
                    .font(.caption)
                    .fontWeight(.medium)
            }
            
            HStack {
                Text("熱點數量:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
                Text("\(data.hotspots.count)")
                    .font(.caption)
                    .fontWeight(.medium)
            }
        }
        .padding(8)
        .background(Color(NSColor.controlBackgroundColor).opacity(0.8))
        .cornerRadius(6)
    }
}