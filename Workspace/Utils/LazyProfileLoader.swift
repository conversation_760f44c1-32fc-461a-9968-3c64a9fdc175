import Foundation
import Combine

/// 懶載入設定檔管理器
/// 負責按需載入設定檔，減少記憶體使用和提升啟動速度
class LazyProfileLoader: ObservableObject {
    static let shared = LazyProfileLoader()
    
    // MARK: - Properties
    
    /// 已載入的設定檔快取
    @Published private var loadedProfiles: [String: Profile] = [:]
    
    /// 設定檔元資料快取（輕量級資訊）
    @Published private var profileMetadata: [String: ProfileMetadata] = [:]
    
    /// 載入狀態追蹤
    @Published private var loadingStates: [String: Bool] = [:]
    
    /// 最近使用的設定檔（LRU 快取）
    private var recentlyUsedProfiles: [String] = []
    
    /// 最大快取大小
    private let maxCacheSize: Int = 50
    
    /// 載入佇列
    private let loadingQueue = DispatchQueue(label: "profile.loading", qos: .userInitiated)
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        loadProfileMetadata()
    }
    
    // MARK: - Public Methods
    
    /// 懶載入指定的設定檔
    func loadProfile(_ profileName: String) -> AnyPublisher<Profile?, Never> {
        // 如果已經載入，直接返回
        if let cachedProfile = loadedProfiles[profileName] {
            updateRecentlyUsed(profileName)
            return Just(cachedProfile).eraseToAnyPublisher()
        }
        
        // 如果正在載入，返回空值並等待
        if loadingStates[profileName] == true {
            return Just(nil).eraseToAnyPublisher()
        }
        
        // 開始載入
        return Future<Profile?, Never> { [weak self] promise in
            self?.loadingQueue.async {
                self?.performProfileLoad(profileName, completion: promise)
            }
        }
        .receive(on: DispatchQueue.main)
        .eraseToAnyPublisher()
    }
    
    /// 批次載入多個設定檔
    func loadProfiles(_ profileNames: [String]) -> AnyPublisher<[Profile], Never> {
        let publishers = profileNames.map { loadProfile($0) }
        
        return Publishers.MergeMany(publishers)
            .collect()
            .map { profiles in
                profiles.compactMap { $0 }
            }
            .eraseToAnyPublisher()
    }
    
    /// 預載入指定 Space 的設定檔元資料
    func preloadSpaceMetadata(_ spaceID: Int) {
        loadingQueue.async { [weak self] in
            self?.performSpaceMetadataPreload(spaceID)
        }
    }
    
    /// 智能預載入：根據使用模式預載入可能需要的設定檔
    func intelligentPreload() {
        loadingQueue.async { [weak self] in
            guard let self = self else { return }
            
            // 預載入最近使用的設定檔
            let recentProfiles = Array(self.recentlyUsedProfiles.prefix(10))
            for profileName in recentProfiles {
                if self.loadedProfiles[profileName] == nil {
                    self.performProfileLoad(profileName) { _ in }
                }
            }
            
            // 預載入當前 Space 的熱門設定檔
            if let currentSpaceID = SpaceDetectionCache.shared.getCurrentSpaceID() {
                self.performSpaceMetadataPreload(currentSpaceID)
            }
        }
    }
    
    /// 背景清理：清理長時間未使用的設定檔
    func backgroundCleanup() {
        loadingQueue.async { [weak self] in
            guard let self = self else { return }
            
            let _ = Date()
            let _: TimeInterval = 300 // 5 分鐘
            
            // 找出長時間未使用的設定檔
            let profilesToEvict = self.loadedProfiles.keys.filter { profileName in
                !self.recentlyUsedProfiles.contains(profileName)
            }
            
            // 清理這些設定檔
            for profileName in profilesToEvict {
                self.loadedProfiles.removeValue(forKey: profileName)
            }
            
            print("LazyProfileLoader: 背景清理完成，清理了 \(profilesToEvict.count) 個設定檔")
        }
    }
    
    /// 獲取設定檔元資料（不載入完整設定檔）
    func getProfileMetadata(_ profileName: String) -> ProfileMetadata? {
        return profileMetadata[profileName]
    }
    
    /// 獲取所有設定檔元資料
    func getAllProfileMetadata() -> [ProfileMetadata] {
        return Array(profileMetadata.values).sorted { $0.modifiedAt > $1.modifiedAt }
    }
    
    /// 清除快取中的設定檔
    func evictProfile(_ profileName: String) {
        loadedProfiles.removeValue(forKey: profileName)
        recentlyUsedProfiles.removeAll { $0 == profileName }
    }
    
    /// 清除所有快取
    func clearCache() {
        loadedProfiles.removeAll()
        recentlyUsedProfiles.removeAll()
        loadingStates.removeAll()
    }
    
    /// 獲取快取統計資訊
    func getCacheStats() -> LazyLoadingCacheStats {
        return LazyLoadingCacheStats(
            loadedCount: loadedProfiles.count,
            metadataCount: profileMetadata.count,
            maxCacheSize: maxCacheSize,
            recentlyUsedCount: recentlyUsedProfiles.count
        )
    }
    
    // MARK: - Private Methods
    
    /// 載入設定檔元資料
    private func loadProfileMetadata() {
        loadingQueue.async { [weak self] in
            guard let self = self else { return }
            
            let homeURL = FileManager.default.homeDirectoryForCurrentUser
            let layoutsPath = homeURL.appendingPathComponent(".hammerspoon/layouts")
            
            do {
                let fileURLs = try FileManager.default.contentsOfDirectory(
                    at: layoutsPath,
                    includingPropertiesForKeys: [.contentModificationDateKey, .fileSizeKey],
                    options: [.skipsHiddenFiles]
                )
                
                var metadata: [String: ProfileMetadata] = [:]
                
                for fileURL in fileURLs where fileURL.pathExtension == "json" {
                    let profileName = fileURL.deletingPathExtension().lastPathComponent
                    
                    if let profileMetadata = self.extractProfileMetadata(from: fileURL) {
                        metadata[profileName] = profileMetadata
                    }
                }
                
                DispatchQueue.main.async {
                    self.profileMetadata = metadata
                }
                
            } catch {
                print("載入設定檔元資料失敗: \(error)")
            }
        }
    }
    
    /// 從檔案提取設定檔元資料
    private func extractProfileMetadata(from fileURL: URL) -> ProfileMetadata? {
        do {
            let data = try Data(contentsOf: fileURL)
            
            // 只解析必要的元資料，不載入完整的視窗佈局
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                let name = json["name"] as? String ?? fileURL.deletingPathExtension().lastPathComponent
                let spaceID = json["spaceID"] as? Int
                let _ = json["isSpaceSpecific"] as? Bool ?? false
                let _ = (json["windows"] as? [[String: Any]])?.count ?? 0
                
                // 解析日期
                let _: Date
                let _: Date
                
                if let createdAtString = json["createdAt"] as? String {
                    let _ = ISO8601DateFormatter().date(from: createdAtString) ?? Date()
                } else {
                    let _ = Date()
                }
                
                if let modifiedAtString = json["modifiedAt"] as? String {
                    let _ = ISO8601DateFormatter().date(from: modifiedAtString) ?? Date()
                } else {
                    let _ = Date()
                }
                
                // 獲取檔案大小
                let _ = (try? fileURL.resourceValues(forKeys: [.fileSizeKey]))?.fileSize ?? 0
                
                return ProfileMetadata(
                    name: name,
                    spaceID: spaceID
                )
            }
        } catch {
            print("提取設定檔元資料失敗 \(fileURL.lastPathComponent): \(error)")
        }
        
        return nil
    }
    
    /// 執行設定檔載入
    private func performProfileLoad(_ profileName: String, completion: @escaping (Result<Profile?, Never>) -> Void) {
        // 標記為載入中
        DispatchQueue.main.async {
            self.loadingStates[profileName] = true
        }
        
        defer {
            DispatchQueue.main.async {
                self.loadingStates[profileName] = false
            }
        }
        
        // 檢查快取大小，必要時清理
        if loadedProfiles.count >= maxCacheSize {
            evictLeastRecentlyUsed()
        }
        
        // 載入設定檔
        guard let metadata = profileMetadata[profileName] else {
            completion(.success(nil))
            return
        }
        
        do {
            let data = try Data(contentsOf: metadata.fileURL)
            let profile = try JSONDecoder().decode(Profile.self, from: data)
            
            DispatchQueue.main.async {
                self.loadedProfiles[profileName] = profile
                self.updateRecentlyUsed(profileName)
            }
            
            completion(.success(profile))
        } catch {
            print("載入設定檔失敗 \(profileName): \(error)")
            completion(.success(nil))
        }
    }
    
    /// 預載入 Space 元資料
    private func performSpaceMetadataPreload(_ spaceID: Int) {
        let spaceProfiles = profileMetadata.values.filter { $0.spaceID == spaceID }
        
        // 預載入最近使用的設定檔
        let recentProfiles = spaceProfiles
            .sorted { $0.modifiedAt > $1.modifiedAt }
            .prefix(5) // 只預載入最近的 5 個
        
        for metadata in recentProfiles {
            if loadedProfiles[metadata.name] == nil {
                performProfileLoad(metadata.name) { _ in }
            }
        }
    }
    
    /// 更新最近使用列表
    private func updateRecentlyUsed(_ profileName: String) {
        recentlyUsedProfiles.removeAll { $0 == profileName }
        recentlyUsedProfiles.insert(profileName, at: 0)
        
        // 限制最近使用列表大小
        if recentlyUsedProfiles.count > maxCacheSize {
            recentlyUsedProfiles = Array(recentlyUsedProfiles.prefix(maxCacheSize))
        }
    }
    
    /// 清理最少使用的設定檔
    private func evictLeastRecentlyUsed() {
        guard recentlyUsedProfiles.count > maxCacheSize / 2 else { return }
        
        let toEvict = Array(recentlyUsedProfiles.suffix(recentlyUsedProfiles.count - maxCacheSize / 2))
        
        for profileName in toEvict {
            loadedProfiles.removeValue(forKey: profileName)
            recentlyUsedProfiles.removeAll { $0 == profileName }
        }
    }
}

// MARK: - Data Structures

/// 設定檔元資料結構
struct ProfileMetadata: Identifiable, Codable {
    let id: UUID
    let name: String
    let spaceID: Int?
    
    let isSpaceSpecific: Bool
    let windowCount: Int
    let fileSize: Int
    let createdAt: Date
    let modifiedAt: Date
    let fileURL: URL
    
    init(name: String, spaceID: Int?) {
        self.id = UUID()
        self.name = name
        self.spaceID = spaceID
        self.isSpaceSpecific = spaceID != nil
        self.windowCount = 0
        self.fileSize = 0
        self.createdAt = Date()
        self.modifiedAt = Date()
        self.fileURL = URL(fileURLWithPath: "/tmp/\(name).json")
    }
    
    /// 顯示名稱
    var displayName: String {
        return name.isEmpty ? "未命名設定檔" : name
    }
    
    /// 預覽描述
    var previewDescription: String {
        let windowText = windowCount == 1 ? "1 個視窗" : "\(windowCount) 個視窗"
        let sizeText = ByteCountFormatter.string(fromByteCount: Int64(fileSize), countStyle: .file)
        return "\(windowText) • \(sizeText)"
    }
}

/// 懶載入快取統計資訊
struct LazyLoadingCacheStats {
    let loadedCount: Int
    let metadataCount: Int
    let maxCacheSize: Int
    let recentlyUsedCount: Int
    
    var cacheUsagePercentage: Double {
        return Double(loadedCount) / Double(maxCacheSize) * 100
    }
}