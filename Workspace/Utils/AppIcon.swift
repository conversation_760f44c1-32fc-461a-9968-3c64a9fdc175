import Foundation
import AppKit

// MARK: - AppIconProvider
/// 提供應用程式圖示獲取功能的工具類
struct AppIconProvider {
    
    /// 獲取應用程式圖示，如果無法獲取則返回預設圖示
    /// - Parameters:
    ///   - bundleID: 應用程式的 Bundle ID
    ///   - appName: 應用程式名稱（作為備用）
    /// - Returns: NSImage 或 nil
    static func getIconWithFallback(bundleID: String?, appName: String?) -> NSImage? {
        // 首先嘗試通過 Bundle ID 獲取圖示
        if let bundleID = bundleID, !bundleID.isEmpty {
            if let icon = getIconByBundleID(bundleID) {
                return icon
            }
        }
        
        // 如果 Bundle ID 無效，嘗試通過應用程式名稱獲取
        if let appName = appName, !appName.isEmpty {
            if let icon = getIconByAppName(appName) {
                return icon
            }
        }
        
        // 返回預設圖示
        return getDefaultIcon()
    }
    
    /// 通過 Bundle ID 獲取應用程式圖示
    private static func getIconByBundleID(_ bundleID: String) -> NSImage? {
        guard let appURL = NSWorkspace.shared.urlForApplication(withBundleIdentifier: bundleID) else {
            return nil
        }
        
        return NSWorkspace.shared.icon(forFile: appURL.path)
    }
    
    /// 通過應用程式名稱獲取圖示
    private static func getIconByAppName(_ appName: String) -> NSImage? {
        // 嘗試在 Applications 目錄中查找
        let applicationsURL = URL(fileURLWithPath: "/Applications")
        let appURL = applicationsURL.appendingPathComponent("\(appName).app")
        
        if FileManager.default.fileExists(atPath: appURL.path) {
            return NSWorkspace.shared.icon(forFile: appURL.path)
        }
        
        // 嘗試通過 NSWorkspace 查找（使用新的 API）
        if let appURL = NSWorkspace.shared.urlForApplication(withBundleIdentifier: appName) {
            return NSWorkspace.shared.icon(forFile: appURL.path)
        }
        
        return nil
    }
    
    /// 獲取預設應用程式圖示
    private static func getDefaultIcon() -> NSImage? {
        return NSImage(systemSymbolName: "app", accessibilityDescription: "應用程式")
    }
    
    /// 獲取指定尺寸的圖示
    /// - Parameters:
    ///   - bundleID: Bundle ID
    ///   - appName: 應用程式名稱
    ///   - size: 所需尺寸
    /// - Returns: 調整尺寸後的 NSImage
    static func getIconWithSize(bundleID: String?, appName: String?, size: CGSize) -> NSImage? {
        guard let originalIcon = getIconWithFallback(bundleID: bundleID, appName: appName) else {
            return nil
        }
        
        let resizedIcon = NSImage(size: size)
        resizedIcon.lockFocus()
        originalIcon.draw(in: NSRect(origin: .zero, size: size))
        resizedIcon.unlockFocus()
        
        return resizedIcon
    }
    
    /// 快取圖示以提高性能
    private static var iconCache: [String: NSImage] = [:]
    
    /// 獲取快取的圖示
    static func getCachedIcon(bundleID: String?, appName: String?) -> NSImage? {
        let cacheKey = "\(bundleID ?? "")_\(appName ?? "")"
        
        if let cachedIcon = iconCache[cacheKey] {
            return cachedIcon
        }
        
        let icon = getIconWithFallback(bundleID: bundleID, appName: appName)
        if let icon = icon {
            iconCache[cacheKey] = icon
        }
        
        return icon
    }
    
    /// 清除圖示快取
    static func clearCache() {
        iconCache.removeAll()
    }
}