import SwiftUI

// MARK: - Interactive Demo
/// 演示交互功能的簡單應用
struct InteractiveDemoApp: App {
    var body: some Scene {
        WindowGroup {
            InteractiveDemoView()
        }
    }
}

struct InteractiveDemoView: View {
    @State private var demoProfile: Profile
    
    init() {
        // 創建演示用的 Profile
        _demoProfile = State(initialValue: Profile(
            name: "交互功能演示",
            windows: [
                WindowLayout(
                    app: "Safari",
                    bundleID: "com.apple.Safari",
                    title: "Apple 官方網站",
                    frame: WindowLayout.WindowFrame(x: 100, y: 100, w: 1200, h: 800)
                ),
                WindowLayout(
                    app: "Xcode",
                    bundleID: "com.apple.dt.Xcode",
                    title: "Workspace 項目",
                    frame: WindowLayout.WindowFrame(x: 1400, y: 200, w: 1400, h: 900)
                ),
                WindowLayout(
                    app: "Terminal",
                    bundleID: "com.apple.Terminal",
                    title: "終端機",
                    frame: WindowLayout.WindowFrame(x: 200, y: 1000, w: 800, h: 500)
                ),
                WindowLayout(
                    app: "Finder",
                    bundleID: "com.apple.finder",
                    title: "文件管理器",
                    frame: WindowLayout.WindowFrame(x: 1100, y: 1100, w: 900, h: 600)
                ),
                WindowLayout(
                    app: "Notes",
                    bundleID: "com.apple.Notes",
                    title: "筆記",
                    frame: WindowLayout.WindowFrame(x: 2900, y: 300, w: 600, h: 700)
                ),
                WindowLayout(
                    app: "Calendar",
                    bundleID: "com.apple.iCal",
                    title: "日曆",
                    frame: WindowLayout.WindowFrame(x: 3600, y: 400, w: 800, h: 600)
                )
            ]
        ))
    }
    
    var body: some View {
        VStack(spacing: 20) {
            // 標題
            Text("交互功能演示")
                .font(.largeTitle)
                .fontWeight(.bold)
                .padding()
            
            // 功能說明
            VStack(alignment: .leading, spacing: 8) {
                Text("已實現的交互功能：")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("✅ 視窗點擊選擇功能（單選和多選）")
                    Text("✅ 懸停工具提示顯示視窗基本信息")
                    Text("✅ 載入狀態指示器")
                    Text("✅ 錯誤狀態友好提示和降級顯示")
                    Text("⚠️ 鍵盤導航支持（待完善）")
                }
                .font(.body)
                .foregroundColor(.secondary)
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
            
            // 主要預覽區域
            LayoutPreviewContainer(profile: demoProfile)
                .frame(height: 500)
                .padding()
            
            // 控制按鈕
            HStack(spacing: 16) {
                Button("添加視窗") {
                    addRandomWindow()
                }
                .buttonStyle(.borderedProminent)
                
                Button("移除視窗") {
                    removeLastWindow()
                }
                .buttonStyle(.bordered)
                .disabled(demoProfile.windows?.isEmpty ?? true)
                
                Button("重置演示") {
                    resetDemo()
                }
                .buttonStyle(.bordered)
            }
            .padding()
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Demo Actions
    
    private func addRandomWindow() {
        let apps = ["Mail", "Music", "Photos", "Messages", "FaceTime", "Maps"]
        let bundleIDs = ["com.apple.mail", "com.apple.Music", "com.apple.Photos", "com.apple.MobileSMS", "com.apple.FaceTime", "com.apple.Maps"]
        
        let randomIndex = Int.random(in: 0..<apps.count)
        let randomX = Double.random(in: 0...3000)
        let randomY = Double.random(in: 0...2000)
        let randomW = Double.random(in: 400...1200)
        let randomH = Double.random(in: 300...800)
        
        let newWindow = WindowLayout(
            app: apps[randomIndex],
            bundleID: bundleIDs[randomIndex],
            title: "\(apps[randomIndex]) - 隨機視窗",
            frame: WindowLayout.WindowFrame(x: randomX, y: randomY, w: randomW, h: randomH)
        )
        
        demoProfile.windows?.append(newWindow)
    }
    
    private func removeLastWindow() {
        if !(demoProfile.windows?.isEmpty ?? true) {
            demoProfile.windows?.removeLast()
        }
    }
    
    private func resetDemo() {
        demoProfile = Profile(
            name: "交互功能演示",
            windows: [
                WindowLayout(
                    app: "Safari",
                    bundleID: "com.apple.Safari",
                    title: "Apple 官方網站",
                    frame: WindowLayout.WindowFrame(x: 100, y: 100, w: 1200, h: 800)
                ),
                WindowLayout(
                    app: "Xcode",
                    bundleID: "com.apple.dt.Xcode",
                    title: "Workspace 項目",
                    frame: WindowLayout.WindowFrame(x: 1400, y: 200, w: 1400, h: 900)
                ),
                WindowLayout(
                    app: "Terminal",
                    bundleID: "com.apple.Terminal",
                    title: "終端機",
                    frame: WindowLayout.WindowFrame(x: 200, y: 1000, w: 800, h: 500)
                )
            ]
        )
    }
}

// MARK: - Preview
#Preview {
    InteractiveDemoView()
        .frame(width: 1200, height: 800)
}