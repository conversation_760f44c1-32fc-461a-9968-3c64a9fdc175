import SwiftUI

struct PreviewModeSelector: View {
    @Binding var selectedMode: PreviewMode
    @Binding var configuration: PreviewConfiguration
    
    var body: some View {
        HStack {
            Picker("預覽模式", selection: $selectedMode) {
                ForEach(PreviewMode.allCases) { mode in
                    Text(mode.displayName).tag(mode)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
}