import SwiftUI

// MARK: - SpaceTabView

/**
 * Space 標籤視圖組件
 *
 * 顯示可用的 macOS Spaces 並允許使用者在不同 Space 之間切換。
 * 組件支持最多 3 個 Spaces，提供視覺化的 Space 選擇界面。
 *
 * ## 功能特點
 * - 顯示所有可用的 macOS Spaces
 * - 突出顯示當前活躍的 Space
 * - 支持 Space 切換時的 UI 狀態更新
 * - 響應式設計適應不同螢幕尺寸
 * - 完整的無障礙支持
 *
 * ## 使用示例
 * ```swift
 * SpaceTabView(selectedSpaceID: $selectedSpace)
 * ```
 *
 * ## 設計考量
 * - 使用系統標準的標籤樣式
 * - 當前 Space 使用強調色標示
 * - 支持深色模式自動適應
 * - 按鈕大小適合觸控操作
 *
 * @since 1.0.0
 * <AUTHOR> Integration Team
 */
struct SpaceTabView: View {
    
    // MARK: - Properties
    
    /// 當前選中的 Space ID
    /// 雙向綁定，支持外部狀態同步
    @Binding var selectedSpaceID: Int?
    
    /// Space 偵測器實例
    /// 用於獲取可用的 Spaces 資訊
    @StateObject private var spaceDetector = SpaceDetector.shared
    
    /// 動畫狀態
    @State private var isAnimating = false
    
    // MARK: - Initialization
    
    /**
     * 初始化 Space 標籤視圖
     *
     * @param selectedSpaceID 當前選中的 Space ID 綁定
     */
    init(selectedSpaceID: Binding<Int?>) {
        self._selectedSpaceID = selectedSpaceID
    }
    
    // MARK: - Body
    
    /**
     * 主視圖內容
     *
     * 創建水平排列的 Space 標籤按鈕，每個按鈕代表一個可用的 Space。
     * 當前活躍的 Space 會被特別標示。
     *
     * ## 視覺設計
     * - 使用圓角矩形背景提供視覺邊界
     * - 活躍 Space 使用系統強調色背景
     * - 非活躍 Space 使用透明背景
     * - 統一的內邊距確保點擊區域足夠大
     *
     * ## 交互行為
     * - 點擊標籤觸發 Space 切換
     * - 懸停顯示 Space 詳細資訊
     * - 支持鍵盤導航
     */
    var body: some View {
        HStack(spacing: 8) {
            // 遍歷所有可用的 Spaces
            ForEach(spaceDetector.availableSpaces) { space in
                SpaceTabButton(
                    space: space,
                    isSelected: isSpaceSelected(space),
                    isCurrentSpace: spaceDetector.isCurrentSpace(space.id),
                    onTap: {
                        handleSpaceSelection(space)
                    }
                )
            }
            
            // 如果沒有可用的 Spaces，顯示佔位符
            if spaceDetector.availableSpaces.isEmpty {
                emptyStateView
            }
        }
        .padding(4)
        .background(containerBackground)
        .accessibilityElement(children: .contain)
        .accessibilityLabel("Space 標籤選擇器")
        .accessibilityHint("選擇不同的 macOS Space")
        .onAppear {
            initializeSelectedSpace()
        }
        .onChange(of: spaceDetector.currentSpaceID) { newSpaceID in
            handleCurrentSpaceChange(newSpaceID)
        }
        .onChange(of: spaceDetector.availableSpaces) { newSpaces in
            handleAvailableSpacesChange(newSpaces)
        }
    }
    
    // MARK: - Private Views
    
    /**
     * 空狀態視圖
     *
     * 當沒有可用 Spaces 時顯示的佔位符
     */
    @ViewBuilder
    private var emptyStateView: some View {
        HStack(spacing: 6) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.orange)
            
            Text("無可用 Space")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color.orange.opacity(0.1))
        )
        .accessibilityLabel("無可用的 macOS Space")
    }
    
    /**
     * 容器背景視圖
     *
     * 提供整個標籤選擇器的背景和邊框
     */
    @ViewBuilder
    private var containerBackground: some View {
        RoundedRectangle(cornerRadius: 8)
            .fill(Color(NSColor.controlBackgroundColor))
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.secondary.opacity(0.2), lineWidth: 1)
            )
    }
    
    // MARK: - Private Methods
    
    /**
     * 檢查指定 Space 是否被選中
     *
     * @param space 要檢查的 Space
     * @return 是否被選中
     */
    private func isSpaceSelected(_ space: SpaceInfo) -> Bool {
        return selectedSpaceID == space.id
    }
    
    /**
     * 初始化選中的 Space
     *
     * 在視圖出現時設置初始的選中 Space
     */
    private func initializeSelectedSpace() {
        // 如果沒有選中的 Space，使用當前 Space 或預設 Space
        if selectedSpaceID == nil {
            if let currentSpace = spaceDetector.currentSpaceID {
                selectedSpaceID = currentSpace
            } else if let firstSpace = spaceDetector.availableSpaces.first {
                selectedSpaceID = firstSpace.id
            }
        }
    }
    
    /**
     * 處理當前 Space 變更
     *
     * 當系統偵測到 Space 切換時更新 UI 狀態
     *
     * @param newSpaceID 新的當前 Space ID
     */
    private func handleCurrentSpaceChange(_ newSpaceID: Int?) {
        print("SpaceTabView: 偵測到當前 Space 變更: \(newSpaceID ?? -1)")
        
        // 如果當前 Space 發生變化，可以選擇是否自動切換選中狀態
        // 這裡保持用戶的選擇，不自動切換
        
        // 觸發 UI 更新動畫
        withAnimation(.easeInOut(duration: 0.2)) {
            isAnimating.toggle()
        }
    }
    
    /**
     * 處理可用 Spaces 變更
     *
     * 當可用的 Spaces 發生變化時更新選中狀態
     *
     * @param newSpaces 新的可用 Spaces 列表
     */
    private func handleAvailableSpacesChange(_ newSpaces: [SpaceInfo]) {
        print("SpaceTabView: 可用 Spaces 變更，數量: \(newSpaces.count)")
        
        // 如果選中的 Space 不再可用，重置選擇
        if let selectedID = selectedSpaceID,
           !newSpaces.contains(where: { $0.id == selectedID }) {
            selectedSpaceID = newSpaces.first?.id
        }
        
        // 如果沒有選中的 Space 且有可用的 Spaces，選擇第一個
        if selectedSpaceID == nil, let firstSpace = newSpaces.first {
            selectedSpaceID = firstSpace.id
        }
    }
    
    /**
     * 處理 Space 選擇
     *
     * 執行 Space 切換邏輯，包括動畫和狀態更新
     *
     * @param space 要切換到的 Space
     */
    private func handleSpaceSelection(_ space: SpaceInfo) {
        // 避免重複選擇同一 Space
        guard selectedSpaceID != space.id else { return }
        
        // 使用動畫進行平滑切換
        withAnimation(.easeInOut(duration: 0.2)) {
            selectedSpaceID = space.id
        }
        
        // 提供觸覺反饋（在支持的設備上）
        #if os(iOS)
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        #endif
        
        // 記錄 Space 切換事件
        print("切換到 Space: \(space.displayName) (ID: \(space.id))")
    }
}

// MARK: - Preview

#if DEBUG
struct SpaceTabView_Previews: PreviewProvider {
    @State static var selectedSpaceID: Int? = 1
    
    static var previews: some View {
        Group {
            // 正常狀態預覽
            SpaceTabView(selectedSpaceID: $selectedSpaceID)
                .previewDisplayName("正常狀態")
                .padding()
            
            // 深色模式預覽
            SpaceTabView(selectedSpaceID: $selectedSpaceID)
                .previewDisplayName("深色模式")
                .preferredColorScheme(.dark)
                .padding()
        }
    }
}
#endif