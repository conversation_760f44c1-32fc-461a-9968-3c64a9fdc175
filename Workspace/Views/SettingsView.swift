import SwiftUI
import ServiceManagement

struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var hammerspoonManager = HammerspoonManager.shared

    @AppStorage("alertTextSize") private var alertTextSize: Double = 64
    @AppStorage("alertDuration") private var alertDuration: Double = 0.5
    @AppStorage("launchAtLogin") private var launchAtLogin: Bool = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 標題列
            HStack {
                Text("設定")
                    .font(.title)
                    .fontWeight(.semibold)

                Spacer()

                Button("完成") {
                    dismiss()
                }
                .buttonStyle(.borderedProminent)
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 16)
            .background(Color(NSColor.controlBackgroundColor))

            Divider()

            ScrollView {
                VStack(spacing: 24) {
                    // 進度提示樣式
                    VStack(alignment: .leading, spacing: 16) {
                        Text("進度提示樣式")
                            .font(.title2)
                            .fontWeight(.semibold)

                        VStack(alignment: .leading, spacing: 12) {
                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Text("字體大小")
                                        .font(.headline)
                                        .fontWeight(.medium)

                                    Spacer()

                                    Text("\(Int(alertTextSize))")
                                        .font(.headline)
                                        .foregroundColor(.secondary)
                                }

                                Slider(value: $alertTextSize, in: 20...100, step: 4) {
                                    Text("字體大小")
                                }
                                .onChange(of: alertTextSize) { newValue in
                                    hammerspoonManager.updateAlertSettings(textSize: newValue, duration: alertDuration)
                                }
                            }

                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Text("顯示時間")
                                        .font(.headline)
                                        .fontWeight(.medium)

                                    Spacer()

                                    Text("\(alertDuration, specifier: "%.1f")秒")
                                        .font(.headline)
                                        .foregroundColor(.secondary)
                                }

                                Slider(value: $alertDuration, in: 0.2...2.0, step: 0.1) {
                                    Text("顯示時間")
                                }
                                .onChange(of: alertDuration) { newValue in
                                    hammerspoonManager.updateAlertSettings(textSize: alertTextSize, duration: newValue)
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 24)

                    Divider()

                    // 應用程式設定
                    VStack(alignment: .leading, spacing: 16) {
                        Text("應用程式")
                            .font(.title2)
                            .fontWeight(.semibold)

                        VStack(spacing: 12) {
                            Toggle("開機時自動啟動", isOn: $launchAtLogin)
                                .onChange(of: launchAtLogin) { newValue in
                                    setLaunchAtLogin(newValue)
                                }

                            HStack {
                                Text("Hammerspoon 狀態")
                                    .font(.headline)
                                    .fontWeight(.medium)

                                Spacer()

                                HStack(spacing: 8) {
                                    Circle()
                                        .fill(hammerspoonManager.isRunning ? .green : .red)
                                        .frame(width: 8, height: 8)

                                    Text(hammerspoonManager.isRunning ? "運行中" : "未運行")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 24)

                    Divider()

                    // 重設按鈕
                    VStack(alignment: .leading, spacing: 16) {
                        Text("重設選項")
                            .font(.title2)
                            .fontWeight(.semibold)

                        Button("重設為預設值") {
                            alertTextSize = 64
                            alertDuration = 0.5
                            launchAtLogin = false
                            setLaunchAtLogin(false)
                            hammerspoonManager.updateAlertSettings(textSize: 64, duration: 0.5)
                        }
                        .buttonStyle(.bordered)
                        .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    .padding(.horizontal, 24)
                }
                .padding(.vertical, 24)
            }
        }
        .frame(width: 450, height: 500)
        .background(Color(NSColor.windowBackgroundColor))
        .onAppear {
            hammerspoonManager.checkInstallation()
        }
    }
    
    private func setLaunchAtLogin(_ enabled: Bool) {
        if #available(macOS 13.0, *) {
            do {
                if enabled {
                    try SMAppService.mainApp.register()
                } else {
                    try SMAppService.mainApp.unregister()
                }
            } catch {
                print("設定開機啟動失敗: \(error)")
            }
        } else {
            // 舊版 macOS 的實作
            setLaunchAtLoginLegacy(enabled)
        }
    }

    private func setLaunchAtLoginLegacy(_ enabled: Bool) {
        // 簡化的實作，僅支援 macOS 13+ 的 SMAppService
        print("Launch at login setting: \(enabled)")
        // 在實際應用中，這裡可以實作舊版 macOS 的登入項目管理
    }
}