import SwiftUI

struct ListPreviewMode: View {
    let windows: [WindowLayout]
    @Binding var selectedWindow: WindowLayout?
    @Binding var selectedWindows: Set<String>
    let configuration: PreviewConfiguration
    let onWindowTap: (WindowLayout) -> Void
    
    var body: some View {
        List(windows, id: \.id) { window in
            WindowListItem(
                window: window,
                isSelected: selectedWindows.contains(window.id)
            )
            .onTapGesture {
                onWindowTap(window)
            }
        }
    }
}

struct WindowListItem: View {
    let window: WindowLayout
    let isSelected: Bool
    
    var body: some View {
        HStack {
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.gray.opacity(0.2))
                .frame(width: 40, height: 30)
            
            VStack(alignment: .leading) {
                Text(window.app.isEmpty ? "Unknown App" : window.app)
                    .font(.headline)
                    .lineLimit(1)
                
                Text(window.title.isEmpty ? "Untitled Window" : window.title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            
            Spacer()
            
            if isSelected {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.blue)
            }
        }
        .padding(.vertical, 4)
        .background(isSelected ? Color.blue.opacity(0.1) : Color.clear)
    }
}