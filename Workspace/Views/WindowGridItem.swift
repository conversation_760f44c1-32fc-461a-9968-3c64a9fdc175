import SwiftUI

struct WindowGridItem: View {
    let window: WindowLayout
    let isSelected: Bool
    let configuration: PreviewConfiguration
    let onTap: () -> Void
    let onHover: (CGPoint?) -> Void
    
    var body: some View {
        VStack {
            RoundedRectangle(cornerRadius: 8)
                .fill(isSelected ? Color.blue.opacity(0.3) : Color.gray.opacity(0.2))
                .frame(width: 120, height: 80)
                .overlay(
                    Text(window.app.isEmpty ? "Unknown" : window.app)
                        .font(.caption)
                        .lineLimit(1)
                )
            
            Text(window.title.isEmpty ? "Untitled" : window.title)
                .font(.caption2)
                .lineLimit(1)
                .foregroundColor(.secondary)
        }
        .onTapGesture {
            onTap()
        }
        .onHover { hovering in
            onHover(hovering ? CGPoint(x: 0, y: 0) : nil)
        }
    }
}