import SwiftUI

struct ProfileEditorView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var profileManager = ProfileManager.shared
    @StateObject private var hammerspoonManager = HammerspoonManager.shared
    @StateObject private var spaceProfileManager = SpaceProfileManager.shared
    @StateObject private var spaceDetector = SpaceDetector.shared

    let originalProfile: Profile
    @State private var currentProfile: Profile
    @State private var editedName: String
    @State private var showingDeleteAlert = false
    @State private var showingRenameAlert = false
    @State private var showingSpaceMoveAlert = false
    @State private var selectedTargetSpaceID: Int?

    // 選中狀態管理
    @State private var selectedWindow: WindowLayout?

    init(profile: Profile) {
        print("🏗️ ProfileEditorView init - Profile: \(profile.name), ID: \(profile.id)")
        print("🏗️ ProfileEditorView init - Windows count: \(profile.windows?.count ?? 0)")
        self.originalProfile = profile
        self._currentProfile = State(initialValue: profile)
        self._editedName = State(initialValue: profile.name)
        print("🏗️ ProfileEditorView init 完成")
    }

    var body: some View {
        let _ = print("🎨 ProfileEditorView body 被調用 - Profile: \(currentProfile.name)")
        return editorBody
    }

    private var editorBody: some View {
        VStack(spacing: 0) {
            // 標題列 (緊湊設計)
            HStack {
                Text("編輯 Profile")
                    .font(.title3)
                    .fontWeight(.medium)

                Spacer()

                Button("完成") {
                    dismiss()
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.small)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
            .background(Color(NSColor.controlBackgroundColor))
            
            Divider()
            
            ScrollView {
                VStack(spacing: 24) {
                    // Profile 資訊 (緊湊設計)
                    VStack(spacing: 16) {
                        HStack(spacing: 16) {
                            // Profile 圖示和基本資訊
                            HStack(spacing: 12) {
                                // Profile 圖示
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.accentColor.opacity(0.1))
                                    .frame(width: 40, height: 40)
                                    .overlay(
                                        Image(systemName: "rectangle.3.group")
                                            .font(.title3)
                                            .foregroundColor(.accentColor)
                                    )

                                VStack(alignment: .leading, spacing: 2) {
                                    Text("Profile 資訊")
                                        .font(.headline)
                                        .fontWeight(.semibold)

                                    Text("\(currentProfile.windows?.count ?? 0) 個視窗")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }

                            Spacer()

                            // 名稱編輯區域
                            HStack(spacing: 8) {
                                VStack(alignment: .leading, spacing: 2) {
                                    Text("名稱")
                                        .font(.caption)
                                        .foregroundColor(.secondary)

                                    TextField("Profile 名稱", text: $editedName)
                                        .textFieldStyle(.roundedBorder)
                                        .frame(width: 140)
                                }

                                Button("重命名") {
                                    showingRenameAlert = true
                                }
                                .buttonStyle(.bordered)
                                .disabled(editedName == originalProfile.name || editedName.isEmpty)
                            }

                            Spacer()

                            // 修改時間
                            VStack(alignment: .trailing, spacing: 2) {
                                Text("修改時間")
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Text(formatDate(currentProfile.modifiedAt))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        // Space 資訊區域
                        HStack(spacing: 16) {
                            // 當前 Space 資訊
                            HStack(spacing: 8) {
                                Image(systemName: "square.3.layers.3d")
                                    .font(.title3)
                                    .foregroundColor(.blue)
                                
                                VStack(alignment: .leading, spacing: 2) {
                                    Text("所屬 Space")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    
                                    if let spaceID = currentProfile.spaceID {
                                        Text(spaceDetector.getDisplayName(for: spaceID))
                                            .font(.headline)
                                            .foregroundColor(.primary)
                                    } else {
                                        Text("未指定")
                                            .font(.headline)
                                            .foregroundColor(.secondary)
                                    }
                                }
                            }
                            
                            Spacer()
                            
                            // Space 移動控制
                            HStack(spacing: 8) {
                                VStack(alignment: .leading, spacing: 2) {
                                    Text("移動到 Space")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    
                                    Picker("選擇 Space", selection: $selectedTargetSpaceID) {
                                        Text("選擇 Space").tag(nil as Int?)
                                        ForEach(spaceDetector.availableSpaces) { space in
                                            Text(space.displayName).tag(space.id as Int?)
                                        }
                                    }
                                    .pickerStyle(.menu)
                                    .frame(width: 120)
                                }
                                
                                Button("移動") {
                                    showingSpaceMoveAlert = true
                                }
                                .buttonStyle(.bordered)
                                .disabled(selectedTargetSpaceID == nil || selectedTargetSpaceID == currentProfile.spaceID)
                            }
                            
                            Spacer()
                            
                            // Space 狀態指示
                            VStack(alignment: .trailing, spacing: 2) {
                                Text("Space 狀態")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                
                                HStack(spacing: 4) {
                                    Circle()
                                        .fill(getSpaceStatusColor())
                                        .frame(width: 8, height: 8)
                                    
                                    Text(getSpaceStatusText())
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color(NSColor.controlBackgroundColor))
                            .overlay(
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color.secondary.opacity(0.2), lineWidth: 1)
                            )
                    )
                    .padding(.horizontal, 24)
                    
                    Divider()
                    
                    // 視覺化佈局預覽
                    VStack(alignment: .leading, spacing: 16) {
                        Text("佈局預覽")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .padding(.horizontal, 24)

                        // 使用簡化的預覽組件支持選中狀態
                        SimpleLayoutPreviewView(
                            profile: currentProfile,
                            selectedWindow: $selectedWindow,
                            onWindowSelected: { window in
                                selectedWindow = window
                                // 高亮實際視窗
                                hammerspoonManager.highlightWindow(
                                    appName: window.app,
                                    windowTitle: window.title,
                                    bundleID: window.bundleID
                                )
                            }
                        )
                        .frame(height: 300)
                        .padding(.horizontal, 24)
                    }
                    
                    Divider()
                    
                    // 視窗列表
                    VStack(alignment: .leading, spacing: 16) {
                        Text("視窗列表")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .padding(.horizontal, 24)

                        LazyVStack(spacing: 8) {
                            ForEach(currentProfile.windows ?? []) { window in
                                Button(action: {
                                    selectedWindow = window
                                    // 高亮實際視窗
                                    hammerspoonManager.highlightWindow(
                                        appName: window.app,
                                        windowTitle: window.title,
                                        bundleID: window.bundleID
                                    )
                                }) {
                                    HStack(spacing: 12) {
                                        // 應用程式圖示
                                        if let icon = AppIconProvider.getIconWithFallback(
                                            bundleID: window.bundleID.isEmpty ? nil : window.bundleID,
                                            appName: window.app.isEmpty ? nil : window.app
                                        ) {
                                            Image(nsImage: icon)
                                                .resizable()
                                                .frame(width: 32, height: 32)
                                                .clipShape(RoundedRectangle(cornerRadius: 6))
                                        } else {
                                            RoundedRectangle(cornerRadius: 6)
                                                .fill(Color.secondary.opacity(0.3))
                                                .frame(width: 32, height: 32)
                                                .overlay(
                                                    Image(systemName: "app")
                                                        .foregroundColor(.secondary)
                                                )
                                        }

                                        VStack(alignment: .leading, spacing: 2) {
                                            Text(window.app)
                                                .font(.headline)
                                                .fontWeight(selectedWindow?.id == window.id ? .bold : .medium)
                                                .foregroundColor(selectedWindow?.id == window.id ? .accentColor : .primary)
                                                .lineLimit(1)
                                            Text(window.title)
                                                .font(.body)
                                                .foregroundColor(selectedWindow?.id == window.id ? .primary : .secondary)
                                                .lineLimit(1)
                                        }

                                        Spacer()

                                        VStack(alignment: .trailing, spacing: 2) {
                                            Text("\(Int(window.frame.w)) × \(Int(window.frame.h))")
                                                .font(.caption)
                                                .fontWeight(selectedWindow?.id == window.id ? .semibold : .regular)
                                                .foregroundColor(selectedWindow?.id == window.id ? .accentColor : .secondary)
                                            Text("(\(Int(window.frame.x)), \(Int(window.frame.y)))")
                                                .font(.caption)
                                                .fontWeight(selectedWindow?.id == window.id ? .semibold : .regular)
                                                .foregroundColor(selectedWindow?.id == window.id ? .accentColor : .secondary)
                                        }

                                        Image(systemName: selectedWindow?.id == window.id ? "info.circle.fill" : "info.circle")
                                            .font(.body)
                                            .foregroundColor(selectedWindow?.id == window.id ? .accentColor : .secondary)
                                            .scaleEffect(selectedWindow?.id == window.id ? 1.1 : 1.0)
                                            .animation(.easeInOut(duration: 0.2), value: selectedWindow?.id == window.id)
                                    }
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 12)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(selectedWindow?.id == window.id ? Color.accentColor.opacity(0.2) : Color(NSColor.controlBackgroundColor))
                                    )
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(selectedWindow?.id == window.id ? Color.accentColor : Color.secondary.opacity(0.2), lineWidth: selectedWindow?.id == window.id ? 3 : 1)
                                    )
                                    .overlay(
                                        // 選中時的內層高亮效果
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(Color.white.opacity(selectedWindow?.id == window.id ? 0.5 : 0), lineWidth: selectedWindow?.id == window.id ? 2 : 0)
                                            .padding(2)
                                    )
                                    .overlay(
                                        // 選中時的脈衝邊框效果
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(Color.accentColor.opacity(selectedWindow?.id == window.id ? 0.4 : 0), lineWidth: selectedWindow?.id == window.id ? 4 : 0)
                                            .scaleEffect(selectedWindow?.id == window.id ? 1.05 : 1.0)
                                            .opacity(selectedWindow?.id == window.id ? 0.6 : 0)
                                            .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: selectedWindow?.id == window.id)
                                    )
                                    .scaleEffect(selectedWindow?.id == window.id ? 1.03 : 1.0)
                                    .shadow(color: selectedWindow?.id == window.id ? Color.accentColor.opacity(0.5) : Color.clear, radius: selectedWindow?.id == window.id ? 4 : 0)
                                    .animation(.easeInOut(duration: 0.25), value: selectedWindow?.id == window.id)
                                }
                                .buttonStyle(PlainButtonStyle())
                                .help("點擊查看詳細信息")
                            }
                        }
                        .padding(.horizontal, 24)
                    }
                    
                    // 危險操作區域
                    VStack(alignment: .leading, spacing: 16) {
                        Text("危險操作")
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(.red)
                            .padding(.horizontal, 24)
                        
                        Button(action: {
                            showingDeleteAlert = true
                        }) {
                            HStack {
                                Image(systemName: "trash")
                                Text("刪除 Profile")
                                    .font(.headline)
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(Color.red)
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                        }
                        .buttonStyle(PlainButtonStyle())
                        .padding(.horizontal, 24)
                    }
                }
                .padding(.vertical, 24)
            }
        }
        .frame(width: 800, height: 900)
        .background(Color(NSColor.windowBackgroundColor))
        .alert("確認重命名", isPresented: $showingRenameAlert) {
            Button("取消", role: .cancel) { }
            Button("重命名") {
                profileManager.renameProfile(originalProfile, to: editedName)
            }
        } message: {
            Text("確定要將 Profile 重命名為「\(editedName)」嗎？")
        }
        .alert("確認刪除", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("刪除", role: .destructive) {
                profileManager.deleteProfile(originalProfile)
                DispatchQueue.main.async {
                    dismiss()
                }
            }
        } message: {
            Text("確定要刪除 Profile「\(originalProfile.name)」嗎？此操作無法復原。")
        }
        .alert("確認移動 Profile", isPresented: $showingSpaceMoveAlert) {
            Button("取消", role: .cancel) { }
            Button("移動") {
                if let targetSpaceID = selectedTargetSpaceID,
                   let currentSpaceID = currentProfile.spaceID {
                    spaceProfileManager.moveProfileBetweenSpaces(
                        currentProfile,
                        from: currentSpaceID,
                        to: targetSpaceID
                    )
                }
            }
        } message: {
            if let targetSpaceID = selectedTargetSpaceID {
                Text("確定要將 Profile「\(currentProfile.name)」移動到「\(spaceDetector.getDisplayName(for: targetSpaceID))」嗎？")
            } else {
                Text("請選擇目標 Space")
            }
        }
        .onAppear {
            updateCurrentProfile()
        }
        .onChange(of: profileManager.profiles) { _ in
            updateCurrentProfile()
        }
    }
    
    private func updateCurrentProfile() {
        // 透過 ID 尋找更新後的 profile，確保一致性
        guard let updatedProfile = profileManager.profiles.first(where: { $0.id == originalProfile.id }) else {
            print("⚠️ ProfileEditorView: 無法找到 ID 為 '\(originalProfile.id)' 的 Profile")
            // 如果 Profile 被刪除，則關閉視圖
            DispatchQueue.main.async {
                dismiss()
            }
            return
        }
        
        print("🔄 ProfileEditorView: 更新 Profile '\(currentProfile.name)' (舊ID: \(currentProfile.id), 新ID: \(updatedProfile.id))")
        
        DispatchQueue.main.async {
            self.currentProfile = updatedProfile
        }
    }
    
    // MARK: - Helper Methods
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_TW")
        return formatter.string(from: date)
    }
    
    private func getSpaceStatusColor() -> Color {
        guard let spaceID = currentProfile.spaceID else {
            return .orange
        }
        
        if spaceDetector.isCurrentSpace(spaceID) {
            return .green
        } else if spaceDetector.isSpaceAccessible(spaceID) {
            return .blue
        } else {
            return .red
        }
    }
    
    private func getSpaceStatusText() -> String {
        guard let spaceID = currentProfile.spaceID else {
            return "未指定"
        }
        
        if spaceDetector.isCurrentSpace(spaceID) {
            return "當前 Space"
        } else if spaceDetector.isSpaceAccessible(spaceID) {
            return "可存取"
        } else {
            return "無法存取"
        }
    }
}

private func getWindowsForPreview(for profile: Profile) -> [WindowLayout] {
    return profile.windows ?? []
}

// MARK: - Supporting Views

struct LayoutPreviewView: View {
    let profile: Profile
    @State private var selectedWindow: WindowLayout?

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(NSColor.controlBackgroundColor))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                    )

                if profile.windows?.isEmpty ?? true {
                    // 空狀態
                    VStack {
                        Image(systemName: "rectangle.3.group")
                            .font(.system(size: 48))
                            .foregroundColor(.secondary)
                        Text("沒有視窗")
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                } else {
                    // 模擬螢幕邊界
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.secondary.opacity(0.5), lineWidth: 2)
                        .padding(20)

                    // 視窗預覽
                    ForEach(getWindowsForPreview(for: profile)) { window in
                        InteractiveWindowPreview(
                            window: window,
                            canvasSize: geometry.size,
                            isSelected: selectedWindow?.id == window.id
                        )
                        .onTapGesture {
                            selectedWindow = window
                        }
                    }
                }
            }
        }
    }
}

struct InteractiveWindowPreview: View {
    let window: WindowLayout
    let canvasSize: CGSize
    let isSelected: Bool
    
    private var scaledFrame: CGRect {
        // 假設螢幕解析度為 2560x1440，縮放到畫布大小
        let screenWidth: CGFloat = 2560
        let screenHeight: CGFloat = 1440
        let padding: CGFloat = 40 // 20 * 2

        // 安全檢查畫布大小
        guard canvasSize.width > padding && canvasSize.height > padding else {
            return CGRect(x: 20, y: 20, width: 50, height: 30) // 默認大小
        }

        let availableWidth = canvasSize.width - padding
        let availableHeight = canvasSize.height - padding

        // 移除未使用的縮放計算，改用正規化方法

        // 安全檢查視窗框架值
        let safeX = window.frame.x.isFinite ? window.frame.x : 0
        let safeY = window.frame.y.isFinite ? window.frame.y : 0
        let safeW = window.frame.w.isFinite && window.frame.w > 0 ? window.frame.w : 100
        let safeH = window.frame.h.isFinite && window.frame.h > 0 ? window.frame.h : 100

        // 正規化座標到 0-1 範圍，然後映射到可用區域
        // 假設視窗座標範圍是 0 到螢幕尺寸
        let normalizedX = max(0, min(1, safeX / screenWidth))
        let normalizedY = max(0, min(1, safeY / screenHeight))
        let normalizedW = max(0.02, min(1, safeW / screenWidth)) // 最小寬度 2%
        let normalizedH = max(0.02, min(1, safeH / screenHeight)) // 最小高度 2%

        // 映射到可用區域
        let scaledX = normalizedX * availableWidth + padding/2
        let scaledY = normalizedY * availableHeight + padding/2
        let scaledWidth = normalizedW * availableWidth
        let scaledHeight = normalizedH * availableHeight

        // 確保視窗不會超出邊界
        let maxX = canvasSize.width - padding/2 - scaledWidth
        let maxY = canvasSize.height - padding/2 - scaledHeight

        let finalX = max(padding/2, min(maxX, scaledX))
        let finalY = max(padding/2, min(maxY, scaledY))
        let finalWidth = min(scaledWidth, canvasSize.width - padding)
        let finalHeight = min(scaledHeight, canvasSize.height - padding)

        return CGRect(x: finalX, y: finalY, width: finalWidth, height: finalHeight)
    }
    
    var body: some View {
        RoundedRectangle(cornerRadius: 4)
            .fill(isSelected ? Color.accentColor.opacity(0.6) : Color.accentColor.opacity(0.3))
            .overlay(
                RoundedRectangle(cornerRadius: 4)
                    .stroke(isSelected ? Color.accentColor : Color.accentColor.opacity(0.7), lineWidth: isSelected ? 2 : 1)
            )
            .overlay(
                VStack(spacing: 2) {
                    if let icon = AppIconProvider.getIconWithFallback(bundleID: window.bundleID.isEmpty ? nil : window.bundleID, appName: window.app.isEmpty ? nil : window.app) {
                        Image(nsImage: icon)
                            .resizable()
                            .frame(width: 16, height: 16)
                    } else {
                        Image(systemName: "app")
                            .frame(width: 16, height: 16)
                            .foregroundColor(.secondary)
                    }
                    
                    Text(window.app)
                        .font(.system(size: 12, weight: .medium))
                        .lineLimit(1)
                        .foregroundColor(.primary)
                }
                .padding(4)
            )
            .frame(width: scaledFrame.width, height: scaledFrame.height)
            .position(x: scaledFrame.midX, y: scaledFrame.midY)
            .help("\(window.app): \(window.title)")
    }
}

// MARK: - Simple Layout Preview View
struct SimpleLayoutPreviewView: View {
    let profile: Profile
    @Binding var selectedWindow: WindowLayout?
    let onWindowSelected: (WindowLayout) -> Void

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(NSColor.controlBackgroundColor))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                    )

                // 模擬螢幕邊界
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.secondary.opacity(0.5), lineWidth: 2)
                    .padding(20)

                // 視窗預覽
                ForEach(getWindowsForPreview(for: profile)) { window in
                    SimpleWindowPreviewView(
                        window: window,
                        canvasSize: geometry.size,
                        isSelected: selectedWindow?.id == window.id,
                        onTap: {
                            onWindowSelected(window)
                        }
                    )
                }
            }
        }
    }
}

// MARK: - Simple Window Preview View
struct SimpleWindowPreviewView: View {
    let window: WindowLayout
    let canvasSize: CGSize
    let isSelected: Bool
    let onTap: () -> Void

    private var scaledFrame: CGRect {
        let screenWidth: CGFloat = 2560
        let screenHeight: CGFloat = 1440
        let padding: CGFloat = 40

        guard canvasSize.width > padding && canvasSize.height > padding else {
            return CGRect(x: 20, y: 20, width: 50, height: 30)
        }

        let availableWidth = canvasSize.width - padding
        let availableHeight = canvasSize.height - padding

        let scaleX = availableWidth / screenWidth
        let scaleY = availableHeight / screenHeight
        let scale = min(scaleX, scaleY)

        let safeX = window.frame.x.isFinite ? window.frame.x : 0
        let safeY = window.frame.y.isFinite ? window.frame.y : 0
        let safeW = window.frame.w.isFinite && window.frame.w > 0 ? window.frame.w : 100
        let safeH = window.frame.h.isFinite && window.frame.h > 0 ? window.frame.h : 100

        // 計算縮放後的尺寸
        let scaledWidth = safeW * scale
        let scaledHeight = safeH * scale

        // 計算縮放後的位置（相對於螢幕中心）
        let scaledX = (safeX + screenWidth/2) * scale + padding/2
        let scaledY = (safeY + screenHeight/2) * scale + padding/2

        // 確保視窗完全在邊界內
        let minX = padding/2
        let minY = padding/2
        let maxX = canvasSize.width - padding/2 - scaledWidth
        let maxY = canvasSize.height - padding/2 - scaledHeight

        let finalX = max(minX, min(maxX, scaledX))
        let finalY = max(minY, min(maxY, scaledY))
        let finalWidth = min(scaledWidth, availableWidth)
        let finalHeight = min(scaledHeight, availableHeight)

        // 確保最小尺寸
        let clampedWidth = max(finalWidth, 20)
        let clampedHeight = max(finalHeight, 15)

        return CGRect(x: finalX, y: finalY, width: clampedWidth, height: clampedHeight)
    }

    var body: some View {
        Button(action: onTap) {
            RoundedRectangle(cornerRadius: 4)
                .fill(isSelected ? Color.accentColor.opacity(0.8) : Color.accentColor.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 4)
                        .stroke(isSelected ? Color.accentColor : Color.accentColor.opacity(0.7), lineWidth: isSelected ? 4 : 1)
                )
                .overlay(
                    // 選中時的外層高亮邊框
                    RoundedRectangle(cornerRadius: 4)
                        .stroke(Color.white.opacity(isSelected ? 1.0 : 0), lineWidth: isSelected ? 2 : 0)
                        .padding(2)
                )
                .overlay(
                    // 選中時的脈衝效果
                    RoundedRectangle(cornerRadius: 4)
                        .stroke(Color.accentColor.opacity(isSelected ? 0.6 : 0), lineWidth: isSelected ? 6 : 0)
                        .scaleEffect(isSelected ? 1.1 : 1.0)
                        .opacity(isSelected ? 0.5 : 0)
                        .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: isSelected)
                )
                .overlay(
                    VStack(spacing: 2) {
                        if let icon = AppIconProvider.getIconWithFallback(bundleID: window.bundleID.isEmpty ? nil : window.bundleID, appName: window.app.isEmpty ? nil : window.app) {
                            Image(nsImage: icon)
                                .resizable()
                                .frame(width: 16, height: 16)
                        } else {
                            Image(systemName: "app")
                                .frame(width: 16, height: 16)
                                .foregroundColor(.secondary)
                        }

                        Text(window.app)
                            .font(.system(size: 12, weight: isSelected ? .bold : .medium))
                            .lineLimit(1)
                            .foregroundColor(isSelected ? .white : .primary)
                    }
                    .padding(4)
                )
                .scaleEffect(isSelected ? 1.12 : 1.0)
                .shadow(color: isSelected ? Color.accentColor.opacity(0.8) : Color.clear, radius: isSelected ? 8 : 0)
                .animation(.easeInOut(duration: 0.25), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
        .frame(width: scaledFrame.width, height: scaledFrame.height)
        .position(x: scaledFrame.midX, y: scaledFrame.midY)
        .help("點擊查看 \(window.app) 的詳細信息")
    }
}