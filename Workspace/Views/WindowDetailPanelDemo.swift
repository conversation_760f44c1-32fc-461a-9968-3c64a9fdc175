import SwiftUI

// MARK: - Demo View for WindowDetailPanel
struct WindowDetailPanelDemo: View {
    @State private var selectedWindow: WindowLayout?
    @State private var showDetailPanel = false
    
    private let sampleWindows: [WindowLayout] = [
        WindowLayout(
            app: "Safari",
            bundleID: "com.apple.Safari",
            title: "Apple - 官方網站",
            frame: WindowLayout.WindowFrame(x: 100, y: 200, w: 1200, h: 800)
        ),
        WindowLayout(
            app: "Xcode",
            bundleID: "com.apple.dt.Xcode",
            title: "WindowDetailPanel.swift",
            frame: WindowLayout.WindowFrame(x: 300, y: 100, w: 1400, h: 900)
        ),
        WindowLayout(
            app: "Finder",
            bundleID: "com.apple.finder",
            title: "桌面",
            frame: WindowLayout.WindowFrame(x: 50, y: 50, w: 800, h: 600)
        )
    ]
    
    var body: some View {
        HStack(spacing: 0) {
            // 主要內容區域
            VStack(alignment: .leading, spacing: 20) {
                Text("WindowDetailPanel 演示")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text("點擊下方的視窗來查看詳細信息面板")
                    .font(.body)
                    .foregroundColor(.secondary)
                
                // 視窗列表
                VStack(spacing: 12) {
                    ForEach(sampleWindows) { window in
                        windowCard(for: window)
                    }
                }
                
                Spacer()
                
                // 控制按鈕
                HStack {
                    Button("關閉面板") {
                        withAnimation {
                            showDetailPanel = false
                            selectedWindow = nil
                        }
                    }
                    .disabled(!showDetailPanel)
                    
                    Spacer()
                    
                    if let selectedWindow = selectedWindow {
                        Text("已選中: \(selectedWindow.app)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(24)
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
            
            // WindowDetailPanel
            WindowDetailPanel(
                window: selectedWindow,
                isVisible: showDetailPanel,
                onClose: {
                    withAnimation {
                        showDetailPanel = false
                        selectedWindow = nil
                    }
                }
            )
        }
        .background(Color(NSColor.windowBackgroundColor))
    }
    
    private func windowCard(for window: WindowLayout) -> some View {
        Button(action: {
            selectedWindow = window
            withAnimation {
                showDetailPanel = true
            }
        }) {
            HStack(spacing: 12) {
                // 應用程式圖示
                if let icon = AppIconProvider.getIconWithFallback(
                    bundleID: window.bundleID.isEmpty ? nil : window.bundleID,
                    appName: window.app.isEmpty ? nil : window.app
                ) {
                    Image(nsImage: icon)
                        .resizable()
                        .frame(width: 32, height: 32)
                        .clipShape(RoundedRectangle(cornerRadius: 6))
                } else {
                    RoundedRectangle(cornerRadius: 6)
                        .fill(Color.secondary.opacity(0.3))
                        .frame(width: 32, height: 32)
                        .overlay(
                            Image(systemName: "app")
                                .foregroundColor(.secondary)
                        )
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(window.app)
                        .font(.headline)
                        .fontWeight(.medium)
                        .lineLimit(1)

                    Text(window.title)
                        .font(.body)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text("\(Int(window.frame.w)) × \(Int(window.frame.h))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("(\(Int(window.frame.x)), \(Int(window.frame.y)))")
                        .font(.caption)
                        .foregroundColor(Color.secondary)
                }
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(selectedWindow?.id == window.id ? Color.accentColor.opacity(0.1) : Color(NSColor.controlBackgroundColor))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(selectedWindow?.id == window.id ? Color.accentColor : Color.clear, lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
#Preview {
    WindowDetailPanelDemo()
        .frame(width: 800, height: 600)
}