# 視窗預覽系統 - 代碼文檔

## 概述

視窗預覽系統是一個高性能、多模式的視窗佈局預覽解決方案，專為處理大量視窗和複雜佈局而設計。系統提供四種不同的預覽模式，智能重疊檢測，以及完整的性能優化機制。

## 核心架構

### 主要組件層次結構

```
LayoutPreviewContainer (主容器)
├── PreviewModeSelector (模式選擇器)
├── 預覽模式組件
│   ├── ScaledPreviewMode (縮放模式)
│   ├── GridPreviewMode (網格模式)
│   ├── ListPreviewMode (列表模式)
│   └── MiniMapPreviewMode (小地圖模式)
├── WindowDetailPanel (詳細信息面板)
└── 性能優化組件
    ├── VirtualizationManager (虛擬化管理)
    ├── BatchUpdateManager (批次更新管理)
    ├── RenderCacheManager (渲染快取管理)
    └── PerformanceMonitor (性能監控)
```

## 公共 API 文檔

### LayoutPreviewContainer

主要的預覽容器組件，負責統一管理所有預覽功能。

#### 初始化方法

```swift
// 完整初始化（推薦用於新集成）
init(
    profile: Profile,
    selectedWindow: Binding<WindowLayout?>,
    selectedWindows: Binding<Set<UUID>>,
    onSelectionChange: ((WindowLayout?) -> Void)? = nil
)

// 簡化初始化（向後兼容）
init(profile: Profile)
```

**參數說明：**
- `profile`: 包含視窗佈局數據的 Profile 對象
- `selectedWindow`: 當前選中視窗的綁定
- `selectedWindows`: 多選視窗集合的綁定
- `onSelectionChange`: 選擇變化時的回調函數

#### 主要功能

- **多模式預覽**: 支持縮放、網格、列表、小地圖四種預覽模式
- **智能選擇管理**: 支持單選和多選模式，跨模式保持選擇狀態
- **性能優化**: 自動虛擬化渲染，批次狀態更新
- **無障礙支持**: 完整的鍵盤導航和螢幕閱讀器支持

### PreviewMode 枚舉

定義四種預覽模式的枚舉類型。

```swift
enum PreviewMode: String, CaseIterable, Identifiable {
    case scaled = "scaled"      // 縮放預覽
    case grid = "grid"          // 網格預覽
    case list = "list"          // 列表預覽
    case miniMap = "miniMap"    // 小地圖預覽
}
```

#### 屬性

- `displayName`: 用戶友好的顯示名稱
- `icon`: SF Symbols 圖標名稱
- `description`: 模式的詳細描述

### PreviewConfiguration 結構體

預覽配置管理結構體，控制預覽行為和外觀。

```swift
struct PreviewConfiguration: Codable, Equatable {
    var mode: PreviewMode                // 當前預覽模式
    var showLabels: Bool                 // 是否顯示標籤
    var showOverlapIndicators: Bool      // 是否顯示重疊指示器
    var groupByApplication: Bool         // 是否按應用程式分組
    var maxWindowsPerView: Int          // 每個視圖的最大視窗數
    var enableAnimations: Bool          // 是否啟用動畫
    var showWindowDetails: Bool         // 是否顯示視窗詳細信息
}
```

#### 靜態方法

```swift
// 獲取默認配置
static let `default`: PreviewConfiguration

// 根據視窗數量獲取優化配置
static func optimized(for windowCount: Int) -> PreviewConfiguration
```

### WindowDisplayInfo 結構體

視窗顯示信息的數據模型，包含渲染所需的所有計算數據。

```swift
struct WindowDisplayInfo: Identifiable, Equatable {
    let window: WindowLayout        // 原始視窗數據
    let displayFrame: CGRect        // 顯示框架
    let isOverlapping: Bool         // 是否重疊
    let overlapLevel: Int          // 重疊層級
    let isVisible: Bool            // 是否可見
    let opacity: Double            // 透明度
    let zIndex: Int               // Z軸順序
}
```

#### 實用方法

```swift
// 檢查視窗是否在指定區域內可見
func isVisible(in bounds: CGRect) -> Bool

// 計算與另一個視窗的重疊面積
func overlapArea(with other: WindowDisplayInfo) -> CGFloat
```

## 預覽模式詳細說明

### ScaledPreviewMode (縮放模式)

智能縮放顯示模式，自動處理視窗重疊和佈局優化。

**特點：**
- 智能重疊檢測和避免算法
- 動態透明度調整
- 層次化顯示重疊視窗
- 保持視窗比例和相對位置

**適用場景：**
- 視窗數量適中（< 30個）
- 需要保持真實佈局關係
- 重疊情況較多的佈局

### GridPreviewMode (網格模式)

統一網格排列模式，提供整齊的視窗預覽。

**特點：**
- 自適應網格佈局計算
- 統一視窗卡片大小
- 支持滾動和虛擬化
- 應用程式圖標顯示

**適用場景：**
- 大量視窗（> 30個）
- 需要快速瀏覽所有視窗
- 空間利用率要求高

### ListPreviewMode (列表模式)

緊湊列表顯示模式，最大化信息密度。

**特點：**
- 水平滾動列表佈局
- 緊湊的信息顯示
- 快速搜索和篩選
- 應用程式分組選項

**適用場景：**
- 極大量視窗（> 50個）
- 需要快速定位特定視窗
- 螢幕空間有限

### MiniMapPreviewMode (小地圖模式)

多螢幕概覽模式，提供整體佈局視圖。

**特點：**
- 多螢幕區域自動檢測
- 視窗密度熱力圖顯示
- 縮放和平移支持
- 螢幕邊界視覺標示

**適用場景：**
- 多螢幕配置
- 需要整體佈局概覽
- 視窗分佈分析

## 性能優化組件

### VirtualizationManager

虛擬化渲染管理器，只渲染可見區域的視窗。

```swift
class VirtualizationManager: ObservableObject {
    // 更新可見範圍
    func updateVisibleRange(
        viewportSize: CGSize,
        scrollOffset: CGPoint,
        totalItems: Int,
        mode: PreviewMode
    )
    
    // 獲取虛擬化項目
    func getVirtualizedItems<T>(from items: [T]) -> [VirtualizedItem<T>]
}
```

### BatchUpdateManager

批次更新管理器，減少不必要的重新渲染。

```swift
class BatchUpdateManager: ObservableObject {
    // 添加更新到批次隊列
    func addUpdate(type: UpdateType, data: Any, completion: (() -> Void)?)
    
    // 立即處理所有待處理的更新
    func flushUpdates()
    
    // 便捷方法
    func updateWindowSelection(_ selectedWindows: [UUID])
    func updatePreviewMode(_ mode: PreviewMode)
    func updateConfiguration(_ config: PreviewConfiguration)
}
```

### RenderCacheManager

渲染快取管理器，快取計算結果和渲染內容。

```swift
class RenderCacheManager: ObservableObject {
    // 快取視窗顯示信息
    func cacheWindowDisplayInfo(_ info: [WindowDisplayInfo], for key: String)
    
    // 獲取快取的顯示信息
    func getCachedWindowDisplayInfo(for key: String) -> [WindowDisplayInfo]?
    
    // 清理過期快取
    func clearExpiredEntries()
}
```

### PerformanceMonitor

性能監控器，追蹤渲染性能和系統指標。

```swift
class PerformanceMonitor: ObservableObject {
    // 記錄幀時間
    func recordFrameTime()
    
    // 記錄渲染時間
    func recordRenderingTime(_ time: TimeInterval)
    
    // 記錄佈局時間
    func recordLayoutTime(_ time: TimeInterval)
    
    // 獲取當前性能指標
    var currentMetrics: PerformanceMetrics { get }
}
```

## 使用示例

### 基本集成

```swift
struct MyView: View {
    let profile: Profile
    @State private var selectedWindow: WindowLayout?
    @State private var selectedWindows: Set<UUID> = []
    
    var body: some View {
        LayoutPreviewContainer(
            profile: profile,
            selectedWindow: $selectedWindow,
            selectedWindows: $selectedWindows,
            onSelectionChange: { window in
                // 處理選擇變化
                print("選中視窗: \(window?.title ?? "無")")
            }
        )
    }
}
```

### 自定義配置

```swift
struct CustomPreviewView: View {
    let profile: Profile
    @StateObject private var configManager = PreviewConfigurationManager.shared
    
    var body: some View {
        VStack {
            // 配置控制
            HStack {
                Toggle("顯示標籤", isOn: $configManager.currentConfiguration.showLabels)
                Toggle("顯示重疊指示器", isOn: $configManager.currentConfiguration.showOverlapIndicators)
            }
            
            // 預覽容器
            LayoutPreviewContainer(profile: profile)
        }
        .onAppear {
            // 根據視窗數量優化配置
            let optimizedConfig = PreviewConfiguration.optimized(for: profile.windows.count)
            configManager.updateConfiguration(optimizedConfig)
        }
    }
}
```

## 錯誤處理

系統提供完整的錯誤處理機制：

### PreviewErrorState 枚舉

```swift
enum PreviewErrorState: Equatable {
    case noWindows                          // 沒有視窗
    case tooManyWindows(count: Int)        // 視窗過多
    case invalidData                       // 數據無效
    case renderingFailed(String)           // 渲染失敗
}
```

### 錯誤恢復

- **自動重試機制**: 渲染失敗時自動重試
- **降級模式**: 性能不足時自動切換到簡化模式
- **用戶友好提示**: 提供清晰的錯誤信息和解決建議

## 無障礙支持

系統完全支持無障礙功能：

- **鍵盤導航**: 方向鍵導航，空格鍵切換多選，回車鍵選擇
- **螢幕閱讀器**: 完整的 VoiceOver 支持
- **高對比度**: 自動適應系統高對比度設置
- **動畫控制**: 遵循系統動畫偏好設置

## 性能指標

### 目標性能指標

- **渲染時間**: < 16ms (60fps)
- **模式切換**: < 200ms
- **記憶體使用**: < 100MB (1000個視窗)
- **CPU 使用率**: < 10% (空閒時)

### 性能監控

使用 `PerformanceMonitor` 追蹤實際性能：

```swift
let monitor = PerformanceMonitor()
let metrics = monitor.currentMetrics

print("平均幀時間: \(metrics.averageFrameTime)ms")
print("渲染效率: \(metrics.renderingEfficiency)%")
print("記憶體使用: \(metrics.memoryUsage)MB")
```

## 擴展指南

### 添加新的預覽模式

1. 在 `PreviewMode` 枚舉中添加新案例
2. 創建對應的視圖組件
3. 在 `LayoutPreviewContainer` 中添加切換邏輯
4. 更新配置和測試

### 自定義渲染邏輯

1. 繼承或實現相應的協議
2. 註冊自定義渲染器
3. 更新配置管理器
4. 添加相應的測試

## 故障排除

常見問題和解決方案請參考 [故障排除指南](./TROUBLESHOOTING.md)。

## 更新日誌

詳細的更新記錄請參考 [CHANGELOG.md](./CHANGELOG.md)。