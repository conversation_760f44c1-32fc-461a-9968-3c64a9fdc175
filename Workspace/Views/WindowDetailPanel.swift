import SwiftUI

struct WindowDetailPanel: View {
    let window: WindowLayout?
    let isVisible: Bool
    let onClose: () -> Void
    
    var body: some View {
        if isVisible, let window = window {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("視窗詳情")
                        .font(.headline)
                    
                    Spacer()
                    
                    But<PERSON>(action: onClose) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                
                Divider()
                
                VStack(alignment: .leading, spacing: 8) {
                    DetailRow(label: "應用程式", value: window.app.isEmpty ? "Unknown" : window.app)
                    DetailRow(label: "標題", value: window.title.isEmpty ? "Untitled" : window.title)
                    DetailRow(label: "位置", value: "\(Int(window.frame.x)), \(Int(window.frame.y))")
                    DetailRow(label: "尺寸", value: "\(Int(window.frame.w)) × \(Int(window.frame.h))")
                    
                    if !window.bundleID.isEmpty {
                        DetailRow(label: "Bundle ID", value: window.bundleID)
                    }
                }
                
                Spacer()
            }
            .padding()
            .frame(width: 250)
            .background(.regularMaterial)
            .cornerRadius(12)
            .shadow(radius: 8)
        }
    }
}

struct DetailRow: View {
    let label: String
    let value: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.body)
                .textSelection(.enabled)
        }
    }
}