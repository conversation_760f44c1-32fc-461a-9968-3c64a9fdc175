import SwiftUI

struct VirtualizedScrollView<Content: View>: View {
    let totalHeight: CGFloat
    let onScroll: (CGPoint) -> Void
    let content: () -> Content
    
    var body: some View {
        ScrollView {
            content()
                .frame(height: totalHeight)
        }
        .onPreferenceChange(ScrollOffsetPreferenceKey.self) { offset in
            onScroll(offset)
        }
    }
}

struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGPoint = .zero
    
    static func reduce(value: inout CGPoint, nextValue: () -> CGPoint) {
        value = nextValue()
    }
}