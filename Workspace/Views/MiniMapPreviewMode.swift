import SwiftUI

struct MiniMapPreviewMode: View {
    let profile: Profile
    let configuration: PreviewConfiguration
    @Binding var selectedWindow: WindowLayout?
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.1))
                
                // Windows as small rectangles
                ForEach(profile.windows ?? [], id: \.id) { window in
                    RoundedRectangle(cornerRadius: 2)
                        .fill(selectedWindow?.id == window.id ? Color.blue : Color.gray)
                        .frame(width: 8, height: 6)
                        .position(
                            x: (window.frame.x / 1920) * geometry.size.width,
                            y: (window.frame.y / 1080) * geometry.size.height
                        )
                }
            }
        }
        .frame(height: 200)
    }
}