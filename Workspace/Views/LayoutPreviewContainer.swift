import SwiftUI
import Combine

// MARK: - LayoutPreviewContainer
struct LayoutPreviewContainer: View {
    let profile: Profile
    
    // MARK: - External Selection Bindings (for integration with ProfileEditorView)
    @Binding var selectedWindow: WindowLayout?
    @Binding var selectedWindows: Set<String>
    let onSelectionChange: ((WindowLayout?) -> Void)?
    
    // MARK: - State Management
    @StateObject private var previewState = PreviewStateManager()
    @StateObject private var configManager = PreviewConfigurationManager.shared
    @State private var showingDetail: Bool = false
    @State private var isLoading: Bool = true
    @State private var loadingProgress: Double = 0.0
    @State private var errorState: PreviewErrorState?
    
    // MARK: - Performance Optimization
    @StateObject private var virtualizationManager = VirtualizationManager()
    @StateObject private var cacheManager = RenderCacheManager()
    @StateObject private var batchUpdateManager = BatchUpdateManager()
    @StateObject private var performanceMonitor = PerformanceMonitor.shared
    @State private var showingPerformanceDebug: Bool = false
    
    // MARK: - Interactive State
    @State private var hoveredWindow: WindowLayout?
    @State private var showingTooltip: Bool = false
    @State private var tooltipPosition: CGPoint = .zero
    @State private var isMultiSelectMode: Bool = false
    @FocusState private var isPreviewFocused: Bool
    
    // MARK: - Accessibility
    @State private var focusedWindowIndex: Int = 0
    @State private var announceSelection: String = ""
    
    private var configuration: PreviewConfiguration {
        configManager.currentConfiguration
    }
    
    // MARK: - Computed Properties
    private var visibleWindows: [WindowLayout] {
        Array((profile.windows ?? []).prefix(configuration.maxWindowsPerView))
    }
    
    private var windowCount: Int {
        profile.windows?.count ?? 0
    }

    private var isInErrorState: Bool {
        errorState != nil
    }
    
    // MARK: - Initializers
    
    // New initializer with selection bindings for integration
    init(
        profile: Profile,
        selectedWindow: Binding<WindowLayout?>,
        selectedWindows: Binding<Set<String>>,
        onSelectionChange: ((WindowLayout?) -> Void)? = nil
    ) {
        self.profile = profile
        self._selectedWindow = selectedWindow
        self._selectedWindows = selectedWindows
        self.onSelectionChange = onSelectionChange
    }
    
    // Legacy initializer for backward compatibility
    init(profile: Profile) {
        self.profile = profile
        self._selectedWindow = .constant(nil)
        self._selectedWindows = .constant([])
        self.onSelectionChange = nil
    }
    
    var body: some View {
        mainContent
            .onAppear { setupInitialState() }
            .onChange(of: profile.windows) { _ in updatePreviewState() }
            .onChange(of: previewState.currentMode) { _ in updateConfiguration() }
            .accessibilityElement(children: .contain)
            .accessibilityLabel("視窗預覽區域")
            .accessibilityHint("使用方向鍵導航，空格鍵切換多選模式，回車鍵選擇視窗")
            .accessibilityValue(announceSelection)
    }
    
    private var mainContent: some View {
        VStack(spacing: 0) {
            headerSection
            Divider()
            mainPreviewArea
            
            if showingPerformanceDebug {
                Divider()
                performanceDebugSection
            }
        }
    }
    
    private var performanceDebugSection: some View {
        VStack {
            Text("性能監控")
                .font(.headline)
            Text("記憶體使用: \(String(format: "%.1f", performanceMonitor.metrics.memoryUsage)) MB")
            Text("CPU 使用: \(String(format: "%.1f", performanceMonitor.metrics.cpuUsage))%")
        }
        .padding()
        .background(Color.secondary.opacity(0.1))
        .transition(.slide)
    }
    
    private var headerSection: some View {
        HStack {
            PreviewModeSelector(
                selectedMode: $previewState.currentMode,
                configuration: $configManager.currentConfiguration
            )
            
            Spacer()
            
            // 性能調試按鈕
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    showingPerformanceDebug.toggle()
                }
            }) {
                Image(systemName: showingPerformanceDebug ? "speedometer.fill" : "speedometer")
                    .foregroundColor(performanceMonitor.currentMetrics.isPerformanceOptimal ? .green : .orange)
            }
            .buttonStyle(PlainButtonStyle())
            .help("性能監控")
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
    }
    
    private var mainPreviewArea: some View {
        GeometryReader { geometry in
            ZStack {
                backgroundView
                contentView(geometry: geometry)
                overlayViews
            }
        }
        .clipped()
    }
    
    private var backgroundView: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(Color(NSColor.controlBackgroundColor))
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
            )
    }
    
    @ViewBuilder
    private func contentView(geometry: GeometryProxy) -> some View {
        if isLoading {
            loadingView
        } else if isInErrorState {
            errorView
        } else {
            // 使用虛擬化渲染的預覽內容
            virtualizedPreviewContent(in: geometry.size)
                .focused($isPreviewFocused)
                .onAppear {
                    // 記錄渲染開始時間
                    let startTime = Date()
                    DispatchQueue.main.async {
                        let renderTime = Date().timeIntervalSince(startTime)
                        performanceMonitor.recordRenderingTime(renderTime)
                    }
                }
        }
    }
    
    private var overlayViews: some View {
        ZStack {
            if showingTooltip, let hoveredWindow = hoveredWindow {
                windowTooltip(for: hoveredWindow)
                    .position(tooltipPosition)
                    .transition(.opacity.combined(with: .scale(scale: 0.8)))
                    .animation(.easeInOut(duration: 0.2), value: showingTooltip)
            }
            
            WindowDetailPanel(
                window: selectedWindow,
                isVisible: showingDetail,
                onClose: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        showingDetail = false
                        selectedWindow = nil
                    }
                }
            )
            .transition(.asymmetric(
                insertion: .move(edge: .trailing).combined(with: .opacity),
                removal: .move(edge: .trailing).combined(with: .opacity)
            ))
        }
    }
    
    // MARK: - Error State
    enum PreviewErrorState: Equatable {
        case noWindows
        case tooManyWindows(count: Int)
        case invalidData
        case renderingFailed(String)
        
        var title: String {
            switch self {
            case .noWindows:
                return "沒有視窗"
            case .tooManyWindows(let count):
                return "視窗過多 (\(count))"
            case .invalidData:
                return "數據無效"
            case .renderingFailed:
                return "渲染失敗"
            }
        }
        
        var message: String {
            switch self {
            case .noWindows:
                return "當前配置中沒有視窗可以預覽"
            case .tooManyWindows(let count):
                return "視窗數量 (\(count)) 超過了最大顯示限制，請考慮使用篩選功能"
            case .invalidData:
                return "視窗數據格式不正確，無法正常顯示"
            case .renderingFailed(let error):
                return "預覽渲染時發生錯誤：\(error)"
            }
        }
        
        var icon: String {
            switch self {
            case .noWindows:
                return "rectangle.3.group"
            case .tooManyWindows:
                return "exclamationmark.triangle"
            case .invalidData:
                return "xmark.octagon"
            case .renderingFailed:
                return "exclamationmark.circle"
            }
        }
        
        var color: Color {
            switch self {
            case .noWindows:
                return .secondary
            case .tooManyWindows:
                return .orange
            case .invalidData, .renderingFailed:
                return .red
            }
        }
    }
    
    // MARK: - Loading View
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("載入中...")
                .font(.headline)
                .foregroundColor(.secondary)
            
            if loadingProgress > 0 {
                ProgressView(value: loadingProgress, total: 1.0)
                    .frame(width: 200)
                
                Text("\(Int(loadingProgress * 100))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(NSColor.controlBackgroundColor).opacity(0.5))
    }
    
    // MARK: - Error View
    private var errorView: some View {
        VStack(spacing: 20) {
            if let errorState = errorState {
                Image(systemName: errorState.icon)
                    .font(.system(size: 48))
                    .foregroundColor(errorState.color)
                
                Text(errorState.title)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(errorState.message)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
                
                // 錯誤恢復按鈕
                HStack(spacing: 12) {
                    Button("重試") {
                        retryPreview()
                    }
                    .buttonStyle(.borderedProminent)
                    
                    if errorState == .noWindows {
                        Button("添加視窗") {
                            // 這裡可以觸發添加視窗的操作
                        }
                        .buttonStyle(.bordered)
                    }
                    
                    if case .tooManyWindows = errorState {
                        Button("使用網格模式") {
                            switchToGridMode()
                        }
                        .buttonStyle(.bordered)
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(NSColor.controlBackgroundColor).opacity(0.5))
    }
    
    // MARK: - Window Tooltip
    private func windowTooltip(for window: WindowLayout) -> some View {
        VStack(alignment: .leading, spacing: 6) {
            HStack(spacing: 8) {
                if let icon = AppIconProvider.getIconWithFallback(
                    bundleID: window.bundleID.isEmpty ? nil : window.bundleID,
                    appName: window.app.isEmpty ? nil : window.app
                ) {
                    Image(nsImage: icon)
                        .resizable()
                        .frame(width: 20, height: 20)
                        .clipShape(RoundedRectangle(cornerRadius: 4))
                }
                
                Text(window.app.isEmpty ? "未知應用程式" : window.app)
                    .font(.headline)
                    .fontWeight(.medium)
                    .lineLimit(1)
            }
            
            if !window.title.isEmpty {
                Text(window.title)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Divider()
            
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text("位置")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(Int(window.frame.x)), \(Int(window.frame.y))")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text("尺寸")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(Int(window.frame.w)) × \(Int(window.frame.h))")
                        .font(.caption)
                        .fontWeight(.medium)
                }
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(.regularMaterial)
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .frame(maxWidth: 250)
    }
    
    // MARK: - Virtualized Preview Content
    @ViewBuilder
    private func virtualizedPreviewContent(in size: CGSize) -> some View {
        let startTime = Date()
        
        // 更新虛擬化管理器的可見範圍
        let _ = {
            virtualizationManager.updateVisibleRange(
                viewportSize: size,
                scrollOffset: CGPoint.zero,
                totalItems: windowCount,
                mode: previewState.currentMode
            )
        }()
        
        // 獲取虛擬化的視窗項目
        let virtualizedWindows = virtualizationManager.getVirtualizedItems(from: profile.windows ?? [])
        let visibleVirtualizedWindows = virtualizedWindows.filter { $0.isVisible }
        
        VStack {
            switch previewState.currentMode {
            case .scaled:
                ScaledPreviewMode(
                    windows: visibleVirtualizedWindows.map { $0.item },
                    canvasSize: size,
                    selectedWindow: $selectedWindow,
                    selectedWindows: $selectedWindows,
                    configuration: configuration,
                    isMultiSelectMode: isMultiSelectMode,
                    focusedIndex: focusedWindowIndex,
                    onWindowTap: handleWindowSelection,
                    onWindowHover: handleWindowHover,
                    onWindowFocus: handleWindowFocus
                )
                
            case .grid:
                VirtualizedScrollView(
                    totalHeight: calculateTotalGridHeight(itemCount: windowCount, containerWidth: size.width),
                    onScroll: { offset in
                        virtualizationManager.updateVisibleRange(
                            viewportSize: size,
                            scrollOffset: offset,
                            totalItems: windowCount,
                            mode: .grid
                        )
                    }
                ) {
                    LazyVGrid(columns: createGridColumns(for: size.width), spacing: 8) {
                        ForEach(visibleVirtualizedWindows, id: \.id) { virtualizedWindow in
                            WindowGridItem(
                                window: virtualizedWindow.item,
                                isSelected: selectedWindows.contains(virtualizedWindow.item.id),
                                configuration: configuration,
                                onTap: { handleWindowSelection(virtualizedWindow.item) },
                                onHover: { position in
                                    if let position = position {
                                        handleWindowHover(virtualizedWindow.item, at: position)
                                    } else {
                                        handleWindowHover(nil, at: .zero)
                                    }
                                }
                            )
                            .id(virtualizedWindow.item.id)
                        }
                    }
                    .padding()
                }
                
            case .list:
                ListPreviewMode(
                windows: visibleVirtualizedWindows.map { $0.item },
                selectedWindow: $selectedWindow,
                selectedWindows: $selectedWindows,
                configuration: configuration,
                onWindowTap: handleWindowSelection
            )
                
            case .miniMap:
                MiniMapPreviewMode(
                    profile: profile,
                    configuration: configuration,
                    selectedWindow: $selectedWindow
                )
            }
        }
        .onAppear {
            // 記錄佈局時間
            DispatchQueue.main.async {
                let layoutTime = Date().timeIntervalSince(startTime)
                performanceMonitor.recordLayoutTime(layoutTime)
            }
        }
    }
    
    // MARK: - Helper Methods for Virtualization
    
    private func calculateTotalGridHeight(itemCount: Int, containerWidth: CGFloat) -> CGFloat {
        let itemWidth: CGFloat = 160
        let itemHeight: CGFloat = 120
        let spacing: CGFloat = 8
        let padding: CGFloat = 16
        
        let availableWidth = containerWidth - padding * 2
        let columnsPerRow = max(1, Int(availableWidth / (itemWidth + spacing)))
        let totalRows = (itemCount + columnsPerRow - 1) / columnsPerRow
        
        return CGFloat(totalRows) * (itemHeight + spacing) + padding * 2
    }
    
    private func createGridColumns(for containerWidth: CGFloat) -> [GridItem] {
        let itemWidth: CGFloat = 160
        let spacing: CGFloat = 8
        let padding: CGFloat = 16
        
        let availableWidth = containerWidth - padding * 2
        let columnsPerRow = max(1, Int(availableWidth / (itemWidth + spacing)))
        
        return Array(repeating: GridItem(.flexible(), spacing: spacing), count: columnsPerRow)
    }
    
    
    
    // MARK: - Event Handlers
    private func handleWindowSelection(_ window: WindowLayout) {
        let startTime = Date()
        
        if isMultiSelectMode {
            // 多選模式
            if selectedWindows.contains(window.id) {
                selectedWindows.remove(window.id)
                if selectedWindow?.id == window.id {
                    selectedWindow = selectedWindows.isEmpty ? nil : 
                        visibleWindows.first { selectedWindows.contains($0.id) }
                }
            } else {
                selectedWindows.insert(window.id)
                selectedWindow = window
            }
            
            // 使用批次更新管理器更新選擇狀態
            batchUpdateManager.updateWindowSelection(Array(selectedWindows))
            
            // 更新無障礙公告
            updateAccessibilityAnnouncement()
        } else {
            // 單選模式
            guard selectedWindow?.id != window.id else { return }
            
            selectedWindow = window
            selectedWindows = [window.id]
            
            // 使用批次更新管理器更新選擇狀態
            batchUpdateManager.updateWindowSelection([window.id])
            
            if configuration.showWindowDetails {
                withAnimation(.easeInOut(duration: 0.3)) {
                    showingDetail = true
                }
            }
        }
        
        // 通知外部選擇變化
        onSelectionChange?(selectedWindow)
        
        // 通知狀態管理器和配置管理器
        previewState.selectWindow(window)
        configManager.lastSelectedWindowId = window.id
        
        // 更新焦點索引
        if let index = visibleWindows.firstIndex(where: { $0.id == window.id }) {
            focusedWindowIndex = index
        }
        
        // 記錄選擇處理時間
        let selectionTime = Date().timeIntervalSince(startTime)
        if selectionTime > 0.01 { // 如果選擇處理時間超過10ms，記錄性能指標
            performanceMonitor.recordRenderingTime(selectionTime)
        }
    }
    
    private func handleWindowHover(_ window: WindowLayout?, at position: CGPoint) {
        if let window = window {
            hoveredWindow = window
            tooltipPosition = position
            
            // 延遲顯示工具提示
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                if hoveredWindow?.id == window.id {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        showingTooltip = true
                    }
                }
            }
        } else {
            hoveredWindow = nil
            withAnimation(.easeInOut(duration: 0.2)) {
                showingTooltip = false
            }
        }
    }
    
    private func handleWindowFocus(_ index: Int) {
        focusedWindowIndex = max(0, min(index, visibleWindows.count - 1))
        
        // 更新無障礙公告
        if focusedWindowIndex < visibleWindows.count {
            let window = visibleWindows[focusedWindowIndex]
            announceSelection = "焦點在 \(window.app) - \(window.title)"
        }
    }
    
    private func setupInitialState() {
        // 檢查數據有效性
        guard !(profile.windows?.isEmpty ?? true) else {
            errorState = .noWindows
            return
        }
        
        // 檢查視窗數量是否過多
        if windowCount > 200 {
            errorState = .tooManyWindows(count: windowCount)
            return
        }
        
        // 驗證視窗數據
        let invalidWindows = (profile.windows ?? []).filter { window in
            !window.frame.x.isFinite || !window.frame.y.isFinite ||
            !window.frame.w.isFinite || !window.frame.h.isFinite ||
            window.frame.w <= 0 || window.frame.h <= 0
        }
        
        if !invalidWindows.isEmpty {
            errorState = .invalidData
            return
        }
        
        // 清除錯誤狀態
        errorState = nil
        
        // 根據視窗數量優化初始配置
        configManager.optimizeConfiguration(for: windowCount)
        
        // 設置預覽狀態
        previewState.setup(with: profile.windows ?? [], configuration: configuration)
        
        // 設置性能優化組件
        setupPerformanceOptimizations()
        
        // 恢復上次選中的視窗
        if let lastSelectedId = configManager.lastSelectedWindowId,
           let window = (profile.windows ?? []).first(where: { $0.id == lastSelectedId }) {
            selectedWindow = window
            selectedWindows = [window.id]
            
            // 更新焦點索引
            if let index = visibleWindows.firstIndex(where: { $0.id == window.id }) {
                focusedWindowIndex = index
            }
        }
        
        updateAccessibilityAnnouncement()

        // Finish loading
        isLoading = false
    }
    
    private func setupPerformanceOptimizations() {
        // 設置性能監控器的依賴
        performanceMonitor.cacheManager = cacheManager
        performanceMonitor.virtualizationManager = virtualizationManager
        performanceMonitor.batchUpdateManager = batchUpdateManager
        
        // 開始性能監控
        performanceMonitor.startMonitoring()
        
        // 設置批次更新回調
        batchUpdateManager.onWindowSelectionUpdate = { selectedWindowIds in
            // 這裡可以添加選擇更新的處理邏輯
        }
        
        // 設置批次更新回調已簡化
        
        // 根據視窗數量配置虛擬化
        let virtualizationConfig = VirtualizationManager.VirtualizationConfig(
            bufferSize: windowCount > 50 ? 10 : 5,
            itemHeight: 120,
            itemWidth: 160,
            enableVirtualization: windowCount > 20
        )
        virtualizationManager.updateConfig(virtualizationConfig)
        
        // 清理過期的快取
        cacheManager.clearExpiredEntries()
    }
    
    private func updatePreviewState() {
        isLoading = true
        loadingProgress = 0.0
        
        // 使用 Task 來處理異步更新，避免阻塞 UI
        Task { @MainActor in
            defer { 
                isLoading = false
                loadingProgress = 0.0
            }
            
            // 模擬載入進度
            for i in 1...5 {
                try? await Task.sleep(nanoseconds: 50_000_000) // 0.05秒
                loadingProgress = Double(i) / 10.0
            }
            
            // 更新狀態管理器
            previewState.updateWindows(profile.windows ?? [])
            
            // 完成載入進度
            for i in 6...10 {
                try? await Task.sleep(nanoseconds: 30_000_000) // 0.03秒
                loadingProgress = Double(i) / 10.0
            }
            
            // 清理無效的選擇
            let validWindowIds = Set((profile.windows ?? []).map { $0.id })
            selectedWindows = selectedWindows.intersection(validWindowIds)
            
            // 如果選中的視窗不再存在，清除選擇
            if let selectedWindow = selectedWindow,
               !validWindowIds.contains(selectedWindow.id) {
                self.selectedWindow = selectedWindows.isEmpty ? nil : 
                    profile.windows?.first { selectedWindows.contains($0.id) }
                
                if self.selectedWindow == nil {
                    showingDetail = false
                }
            }
            
            // 更新焦點索引
            if let selectedWindow = selectedWindow,
               let index = visibleWindows.firstIndex(where: { $0.id == selectedWindow.id }) {
                focusedWindowIndex = index
            } else if focusedWindowIndex >= visibleWindows.count {
                focusedWindowIndex = max(0, visibleWindows.count - 1)
            }
            
            updateAccessibilityAnnouncement()
        }
    }
    
    private func updateConfiguration() {
        // 配置更改已經通過 configManager 自動保存
        // 這裡可以添加額外的配置同步邏輯
    }
    
    // MARK: - Interactive Methods
    
    // Keyboard handling will be implemented later with proper event handling
    
    private func navigateWindow(direction: Int) {
        let newIndex = focusedWindowIndex + direction
        let clampedIndex = max(0, min(newIndex, visibleWindows.count - 1))
        
        if clampedIndex != focusedWindowIndex {
            handleWindowFocus(clampedIndex)
        }
    }
    
    private func toggleMultiSelectMode() {
        isMultiSelectMode.toggle()
        
        if !isMultiSelectMode {
            // 退出多選模式時，只保留最後選中的視窗
            if let lastSelected = selectedWindow {
                selectedWindows = [lastSelected.id]
            } else {
                selectedWindows.removeAll()
            }
        }
        
        updateAccessibilityAnnouncement()
    }
    
    private func clearSelection() {
        selectedWindow = nil
        selectedWindows.removeAll()
        showingDetail = false
        isMultiSelectMode = false
        
        // 通知外部選擇變化
        onSelectionChange?(nil)
        
        updateAccessibilityAnnouncement()
    }
    
    private func updateAccessibilityAnnouncement() {
        if selectedWindows.isEmpty {
            announceSelection = "沒有選中的視窗"
        } else if selectedWindows.count == 1, let window = selectedWindow {
            announceSelection = "已選中 \(window.app) - \(window.title)"
        } else {
            announceSelection = "已選中 \(selectedWindows.count) 個視窗"
        }
        
        if isMultiSelectMode {
            announceSelection += "，多選模式已啟用"
        }
    }
    
    // MARK: - Error Handling Methods
    
    private func retryPreview() {
        errorState = nil
        isLoading = true
        loadingProgress = 0.0
        
        // 模擬重新載入過程
        Task { @MainActor in
            for i in 1...10 {
                try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
                loadingProgress = Double(i) / 10.0
            }
            
            // 檢查數據有效性
            if (profile.windows?.isEmpty ?? true) {
                errorState = .noWindows
            } else if windowCount > 200 {
                errorState = .tooManyWindows(count: windowCount)
            } else {
                // 嘗試設置預覽狀態
                setupInitialState()
            }
            
            isLoading = false
        }
    }
    
    private func switchToGridMode() {
        previewState.switchMode(to: .grid, animated: true)
        configManager.updatePreviewMode(.grid)
        errorState = nil
    }
}