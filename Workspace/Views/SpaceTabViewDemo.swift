import SwiftUI
import Foundation

// MARK: - SpaceTabViewDemo

/**
 * Space 標籤視圖演示
 *
 * 提供一個完整的演示界面來測試和展示 SpaceTabView 和 SpaceTabButton 的功能。
 * 這個演示可以用來手動驗證所有 Space 標籤相關的功能。
 *
 * ## 演示功能
 * - 顯示 Space 標籤選擇器
 * - 實時顯示選中的 Space 資訊
 * - 模擬不同的 Space 狀態
 * - 測試 UI 狀態更新
 *
 * ## 使用方法
 * 在主應用程式中添加此視圖來測試 Space 標籤功能
 *
 * @since 1.0.0
 * <AUTHOR> Integration Team
 */
struct SpaceTabViewDemo: View {
    
    // MARK: - Properties
    
    /// 當前選中的 Space ID
    @State private var selectedSpaceID: Int? = 1
    
    /// Space 偵測器實例
    @StateObject private var spaceDetector = SpaceDetector.shared
    
    /// 演示模式開關
    @State private var isDemoMode = false
    
    /// 模擬的 Space 資料（用於演示）
    @State private var demoSpaces: [SpaceInfo] = [
        SpaceInfo(id: 1, name: "Space 1", isActive: true),
        SpaceInfo(id: 2, name: "Space 2", isActive: false),
        SpaceInfo(id: 3, name: "Space 3", isActive: false)
    ]
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 20) {
            // 標題
            headerView
            
            // 控制面板
            controlPanel
            
            // Space 標籤視圖
            spaceTabSection
            
            // 狀態資訊
            statusInfoSection
            
            // 演示按鈕
            demoButtonsSection
            
            Spacer()
        }
        .padding()
        .frame(maxWidth: 600, maxHeight: 500)
        .background(Color(NSColor.windowBackgroundColor))
    }
    
    // MARK: - Private Views
    
    /**
     * 標題視圖
     */
    @ViewBuilder
    private var headerView: some View {
        VStack(spacing: 8) {
            Text("Space 標籤視圖演示")
                .font(.title)
                .fontWeight(.bold)
            
            Text("測試和驗證 Space 標籤功能")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
    
    /**
     * 控制面板
     */
    @ViewBuilder
    private var controlPanel: some View {
        HStack {
            Toggle("演示模式", isOn: $isDemoMode)
                .help("開啟演示模式使用模擬資料")
            
            Spacer()
            
            Button("重新整理") {
                spaceDetector.refreshSpaceInfo()
            }
            .help("重新整理 Space 資訊")
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(NSColor.controlBackgroundColor))
        )
    }
    
    /**
     * Space 標籤區段
     */
    @ViewBuilder
    private var spaceTabSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Space 標籤選擇器")
                .font(.headline)
            
            // 實際的 SpaceTabView
            SpaceTabView(selectedSpaceID: $selectedSpaceID)
            
            // 演示模式下的額外按鈕
            if isDemoMode {
                demoSpaceButtons
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(NSColor.controlBackgroundColor))
        )
    }
    
    /**
     * 演示模式下的 Space 按鈕
     */
    @ViewBuilder
    private var demoSpaceButtons: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("演示按鈕 (模擬資料)")
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack(spacing: 8) {
                ForEach(demoSpaces) { space in
                    SpaceTabButton(
                        space: space,
                        isSelected: selectedSpaceID == space.id,
                        isCurrentSpace: space.isActive,
                        onTap: {
                            selectedSpaceID = space.id
                            updateDemoSpaceActivity(space.id)
                        }
                    )
                }
            }
        }
    }
    
    /**
     * 狀態資訊區段
     */
    @ViewBuilder
    private var statusInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("狀態資訊")
                .font(.headline)
            
            VStack(alignment: .leading, spacing: 8) {
                statusRow("選中的 Space ID", value: selectedSpaceID?.description ?? "無")
                statusRow("當前 Space ID", value: spaceDetector.currentSpaceID?.description ?? "無")
                statusRow("可用 Spaces 數量", value: "\(spaceDetector.availableSpaces.count)")
                statusRow("演示模式", value: isDemoMode ? "開啟" : "關閉")
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(NSColor.controlBackgroundColor))
        )
    }
    
    /**
     * 狀態行
     */
    @ViewBuilder
    private func statusRow(_ label: String, value: String) -> some View {
        HStack {
            Text(label + ":")
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
        }
    }
    
    /**
     * 演示按鈕區段
     */
    @ViewBuilder
    private var demoButtonsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("測試操作")
                .font(.headline)
            
            HStack(spacing: 12) {
                Button("選擇 Space 1") {
                    selectedSpaceID = 1
                }
                
                Button("選擇 Space 2") {
                    selectedSpaceID = 2
                }
                
                Button("選擇 Space 3") {
                    selectedSpaceID = 3
                }
                
                Button("清除選擇") {
                    selectedSpaceID = nil
                }
            }
            
            HStack(spacing: 12) {
                Button("執行整合測試") {
                    runIntegrationTests()
                }
                .buttonStyle(.borderedProminent)
                
                Button("重置演示") {
                    resetDemo()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(NSColor.controlBackgroundColor))
        )
    }
    
    // MARK: - Private Methods
    
    /**
     * 更新演示 Space 的活躍狀態
     */
    private func updateDemoSpaceActivity(_ spaceID: Int) {
        for i in 0..<demoSpaces.count {
            demoSpaces[i] = SpaceInfo(
                id: demoSpaces[i].id,
                name: demoSpaces[i].name,
                isActive: demoSpaces[i].id == spaceID
            )
        }
    }
    
    /**
     * 執行整合測試
     */
    private func runIntegrationTests() {
        print("🚀 執行 SpaceTabView 整合測試...")
        
        // 執行整合測試
        let tester = SpaceTabViewIntegrationTests()
        let testResult = tester.runAllTests()
        
        // 顯示結果
        let alert = NSAlert()
        alert.messageText = "整合測試結果"
        alert.informativeText = testResult ? "所有測試通過！✅" : "部分測試失敗 ⚠️"
        alert.alertStyle = testResult ? .informational : .warning
        alert.addButton(withTitle: "確定")
        alert.runModal()
    }
    
    /**
     * 重置演示
     */
    private func resetDemo() {
        selectedSpaceID = 1
        isDemoMode = false
        demoSpaces = [
            SpaceInfo(id: 1, name: "Space 1", isActive: true),
            SpaceInfo(id: 2, name: "Space 2", isActive: false),
            SpaceInfo(id: 3, name: "Space 3", isActive: false)
        ]
        spaceDetector.refreshSpaceInfo()
    }
}

// MARK: - Preview

#if DEBUG
struct SpaceTabViewDemo_Previews: PreviewProvider {
    static var previews: some View {
        SpaceTabViewDemo()
            .previewDisplayName("Space 標籤演示")
    }
}
#endif