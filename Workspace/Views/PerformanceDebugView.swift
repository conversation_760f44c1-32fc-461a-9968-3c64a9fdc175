import SwiftUI

// MARK: - PerformanceDebugView
struct PerformanceDebugView: View {
    let monitor: PerformanceMonitor
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            headerSection
            metricsSection
            statusSection
        }
        .padding(12)
        .background(backgroundView)
    }
    
    private var headerSection: some View {
        Text("性能監控")
            .font(.headline)
            .fontWeight(.semibold)
    }
    
    private var metricsSection: some View {
        HStack {
            memoryMetric
            Spacer()
            cpuMetric
            Spacer()
            operationsMetric
        }
    }
    
    private var memoryMetric: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("記憶體使用")
                .font(.caption)
                .foregroundColor(.secondary)
            Text("\(monitor.metrics.memoryUsage, specifier: "%.1f")MB")
                .font(.system(.body, design: .monospaced))
                .fontWeight(.medium)
        }
    }
    
    private var cpuMetric: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("CPU 使用")
                .font(.caption)
                .foregroundColor(.secondary)
            Text("\(monitor.metrics.cpuUsage, specifier: "%.1f")%")
                .font(.system(.body, design: .monospaced))
                .fontWeight(.medium)
        }
    }
    
    private var operationsMetric: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("活躍操作")
                .font(.caption)
                .foregroundColor(.secondary)
            Text("\(monitor.metrics.activeOperations)")
                .font(.system(.body, design: .monospaced))
                .fontWeight(.medium)
        }
    }
    
    private var statusSection: some View {
        HStack {
            Circle()
                .fill(monitor.metrics.isPerformanceOptimal ? Color.green : Color.orange)
                .frame(width: 8, height: 8)
            
            Text(monitor.metrics.isPerformanceOptimal ? "性能良好" : "性能警告")
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
        }
    }
    
    private var backgroundView: some View {
        RoundedRectangle(cornerRadius: 8)
            .fill(Color(NSColor.controlBackgroundColor).opacity(0.8))
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.secondary.opacity(0.2), lineWidth: 1)
            )
    }
}