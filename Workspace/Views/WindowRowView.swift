import SwiftUI

struct WindowRowView: View {
    let window: WindowLayout
    let isSelected: Bool

    var body: some View {
        HStack(spacing: 12) {
            if let icon = AppIconProvider.getIconWithFallback(bundleID: window.bundleID.isEmpty ? nil : window.bundleID, appName: window.app.isEmpty ? nil : window.app) {
                Image(nsImage: icon)
                    .resizable()
                    .frame(width: 32, height: 32)
                    .clipShape(RoundedRectangle(cornerRadius: 6))
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(window.app)
                    .font(.headline)
                    .fontWeight(.medium)
                Text(window.title)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }

            Spacer()

            VStack(alignment: .trailing, spacing: 4) {
                Text("\(Int(window.frame.w)) x \(Int(window.frame.h))")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text("at (\(Int(window.frame.x)), \(Int(window.frame.y)))")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(10)
        .background(isSelected ? Color.accentColor.opacity(0.2) : Color.clear)
        .cornerRadius(8)
    }
}
