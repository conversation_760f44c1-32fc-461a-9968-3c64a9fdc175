import SwiftUI

struct KeyboardShortcutsView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var hammerspoonManager = HammerspoonManager.shared
    
    var body: some View {
        VStack(spacing: 0) {
            // 標題列
            HStack {
                Text("快捷鍵說明")
                    .font(.title)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("完成") {
                    dismiss()
                }
                .buttonStyle(.borderedProminent)
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 16)
            .background(Color(NSColor.controlBackgroundColor))
            
            Divider()
            
            ScrollView {
                VStack(spacing: 24) {
                    // 說明文字
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Hammerspoon 快捷鍵")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Text("以下是目前配置的 Hammerspoon 快捷鍵。您可以使用這些快捷鍵來快速儲存和還原視窗佈局，無需開啟選單。")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .fixedSize(horizontal: false, vertical: true)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 24)
                    
                    Divider()
                    
                    // 快捷鍵列表
                    VStack(spacing: 16) {
                        ForEach(hammerspoonManager.getKeyboardShortcuts(), id: \.profile) { shortcut in
                            ShortcutRowView(shortcut: shortcut)
                        }
                    }
                    .padding(.horizontal, 24)
                    
                    Divider()
                    
                    // 使用說明
                    VStack(alignment: .leading, spacing: 16) {
                        Text("使用說明")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        VStack(alignment: .leading, spacing: 12) {
                            InstructionRow(
                                icon: "1.circle.fill",
                                title: "儲存佈局",
                                description: "使用「儲存」快捷鍵來儲存當前的視窗佈局到對應的 Profile"
                            )
                            
                            InstructionRow(
                                icon: "2.circle.fill",
                                title: "還原佈局",
                                description: "使用「還原」快捷鍵來快速還原已儲存的視窗佈局"
                            )
                            
                            InstructionRow(
                                icon: "3.circle.fill",
                                title: "自動啟動",
                                description: "如果應用程式未運行，Hammerspoon 會自動啟動所需的應用程式"
                            )
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 24)
                    
                    Divider()
                    
                    // 注意事項
                    VStack(alignment: .leading, spacing: 16) {
                        Text("注意事項")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.orange)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("• 確保 Hammerspoon 正在運行，否則快捷鍵將無法使用")
                            Text("• 某些應用程式可能需要額外的權限才能正確管理視窗")
                            Text("• 如果快捷鍵與其他應用程式衝突，請修改 ~/.hammerspoon/init.lua")
                            Text("• 建議定期備份您的 Profile 檔案")
                        }
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.orange.opacity(0.1))
                    )
                    .padding(.horizontal, 24)
                }
                .padding(.vertical, 24)
            }
        }
        .frame(width: 500, height: 600)
        .background(Color(NSColor.windowBackgroundColor))
    }
}

// MARK: - Supporting Views

struct ShortcutRowView: View {
    let shortcut: KeyboardShortcut
    
    var body: some View {
        VStack(spacing: 12) {
            // Profile 名稱
            HStack {
                Text(shortcut.profile)
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
            }
            
            // 快捷鍵
            HStack(spacing: 24) {
                // 儲存快捷鍵
                VStack(alignment: .leading, spacing: 8) {
                    Text("儲存")
                        .font(.headline)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                    
                    KeyboardShortcutView(keys: shortcut.saveKeys, color: .green)
                }
                
                Spacer()
                
                // 還原快捷鍵
                VStack(alignment: .trailing, spacing: 8) {
                    Text("還原")
                        .font(.headline)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)
                    
                    KeyboardShortcutView(keys: shortcut.restoreKeys, color: .blue)
                }
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(NSColor.controlBackgroundColor))
        )
    }
}

struct KeyboardShortcutView: View {
    let keys: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 4) {
            ForEach(keys.map { String($0) }, id: \.self) { key in
                Text(key)
                    .font(.system(size: 18, weight: .medium, design: .monospaced))
                    .foregroundColor(.white)
                    .padding(.horizontal, 10)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(color)
                    )
            }
        }
    }
}

struct InstructionRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 20))
                .foregroundColor(.accentColor)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .fixedSize(horizontal: false, vertical: true)
            }
            
            Spacer()
        }
    }
}

#Preview {
    KeyboardShortcutsView()
}
