import SwiftUI

// MARK: - GridPreviewMode
struct GridPreviewMode: View {
    let windows: [WindowLayout]
    let canvasSize: CGSize
    @Binding var selectedWindow: WindowLayout?
    @Binding var selectedWindows: Set<String>
    let configuration: PreviewConfiguration
    let isMultiSelectMode: Bool
    let focusedIndex: Int
    let onWindowTap: (WindowLayout) -> Void
    let onWindowHover: (WindowLayout?, CGPoint) -> Void
    let onWindowFocus: (Int) -> Void

    private var gridLayout: GridLayoutCalculator.GridConfiguration {
        GridLayoutCalculator.calculateLayout(for: windows.count, in: canvasSize)
    }

    var body: some View {
        ScrollView {
            LazyVGrid(columns: gridLayout.columns, spacing: 16) {
                ForEach(Array(windows.enumerated()), id: \.element.id) { index, window in
                    WindowGridItem(
                        window: window,
                        isSelected: selectedWindows.contains(window.id),
                        configuration: configuration,
                        onTap: { onWindowTap(window) },
                        onHover: { position in onWindowHover(window, position ?? .zero) }
                    )
                    .frame(width: gridLayout.itemSize.width, height: gridLayout.itemSize.height)
                    .id(window.id)
                }
            }
            .padding()
        }
    }
}

