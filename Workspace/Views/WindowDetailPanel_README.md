# WindowDetailPanel 組件文檔

## 概述

`WindowDetailPanel` 是一個 SwiftUI 組件，用於顯示選中視窗的詳細信息。它提供了一個滑動式側邊面板，包含視窗的完整信息、格式化的數據顯示以及便捷的操作功能。

## 功能特性

### 🎨 視覺設計
- **響應式設計**: 適應不同的預覽區域大小
- **流暢動畫**: 面板顯示和隱藏的平滑過渡效果
- **現代化界面**: 使用 `.regularMaterial` 背景和陰影效果
- **清晰的信息層次**: 分區域顯示不同類型的信息

### 📊 信息顯示
- **應用程式信息**: 顯示應用程式名稱、圖示和 Bundle ID
- **視窗信息**: 顯示視窗標題和相關元數據
- **位置與尺寸**: 格式化顯示座標、尺寸、面積和中心點
- **智能格式化**: 自動處理無效數值和邊界情況

### ⚡ 操作功能
- **複製位置信息**: 快速複製視窗的位置和尺寸數據
- **複製完整信息**: 複製所有視窗詳細信息的格式化文本
- **複製 JSON 格式**: 以 JSON 格式複製視窗數據，便於開發調試
- **複製反饋**: 顯示複製成功的視覺反饋

## 使用方法

### 基本用法

```swift
import SwiftUI

struct MyView: View {
    @State private var selectedWindow: WindowLayout?
    @State private var showDetailPanel = false
    
    var body: some View {
        HStack {
            // 主要內容
            VStack {
                // 你的主要界面內容
                Text("主要內容區域")
                
                Button("顯示詳細面板") {
                    selectedWindow = someWindow
                    showDetailPanel = true
                }
            }
            
            // WindowDetailPanel
            WindowDetailPanel(
                window: selectedWindow,
                isVisible: showDetailPanel,
                onClose: {
                    showDetailPanel = false
                    selectedWindow = nil
                }
            )
        }
    }
}
```

### 與現有 ProfileEditorView 集成

參考 `ProfileEditorViewWithDetailPanel.swift` 文件，了解如何將 WindowDetailPanel 集成到現有的 Profile 編輯器中：

1. **添加狀態變量**:
```swift
@State private var selectedWindow: WindowLayout?
@State private var showDetailPanel = false
```

2. **修改佈局結構**:
```swift
HStack(spacing: 0) {
    // 主要內容區域
    VStack {
        // 現有內容
    }
    .frame(maxWidth: .infinity)
    
    // WindowDetailPanel
    WindowDetailPanel(
        window: selectedWindow,
        isVisible: showDetailPanel,
        onClose: { /* 關閉邏輯 */ }
    )
}
```

3. **添加視窗選擇邏輯**:
```swift
// 在視窗列表或預覽中添加點擊處理
.onTapGesture {
    selectedWindow = window
    showDetailPanel = true
}
```

## API 參考

### 初始化參數

| 參數 | 類型 | 描述 |
|------|------|------|
| `window` | `WindowLayout?` | 要顯示詳細信息的視窗，`nil` 時不顯示內容 |
| `isVisible` | `Bool` | 控制面板的顯示狀態 |
| `onClose` | `() -> Void` | 面板關閉時的回調函數 |

### 內部配置

| 屬性 | 值 | 描述 |
|------|-----|------|
| `panelWidth` | `280` | 面板的固定寬度（點） |
| `animationDuration` | `0.3` | 動畫持續時間（秒） |

## 信息格式化

### 座標格式化
- 有效數值: `"100 px"`
- 無效數值: `"無效"`

### 尺寸格式化
- 正數: `"800 px"`
- 零或負數: `"無效"`
- 無限值: `"無效"`

### 面積格式化
- 小於 1K: `"500 px²"`
- 1K-1M: `"480 K px²"`
- 大於 1M: `"2.0 M px²"`

### 中心點格式化
- 有效座標: `"(500, 400)"`
- 無效座標: `"無效"`

## 複製功能

### 位置信息格式
```
位置: (100, 200)
尺寸: 800 × 600
```

### 完整信息格式
```
應用程式: Safari
視窗標題: Apple - 官方網站
Bundle ID: com.apple.Safari
位置: (100, 200)
尺寸: 800 × 600
面積: 480 K px²
中心點: (500, 500)
```

### JSON 格式
```json
{
  "app" : "Safari",
  "bundleID" : "com.apple.Safari",
  "frame" : {
    "h" : 600,
    "w" : 800,
    "x" : 100,
    "y" : 200
  },
  "title" : "Apple - 官方網站"
}
```

## 無障礙支持

- **鍵盤導航**: 支持 Tab 鍵導航
- **工具提示**: 為所有按鈕提供 `help()` 提示
- **語義化標籤**: 使用適當的 SF Symbols 圖示
- **對比度**: 確保文字和背景有足夠的對比度

## 性能考慮

- **延遲渲染**: 只在 `isVisible` 為 `true` 時渲染面板內容
- **動畫優化**: 使用 SwiftUI 的內建動畫系統
- **記憶體效率**: 不快取不必要的數據
- **響應式更新**: 只在必要時重新計算格式化文本

## 錯誤處理

- **無效數值**: 自動檢測並顯示 "無效"
- **空字符串**: 顯示適當的預設值（如 "未知"、"無標題"）
- **JSON 編碼錯誤**: 顯示錯誤信息而不是崩潰
- **剪貼簿錯誤**: 靜默處理，不影響用戶體驗

## 自定義和擴展

### 修改面板寬度
```swift
// 在 WindowDetailPanel.swift 中修改
private let panelWidth: CGFloat = 320 // 改為你需要的寬度
```

### 添加新的操作按鈕
```swift
// 在 actionButtonsSection 中添加
actionButton(
    title: "你的操作",
    icon: "your.icon",
    action: { /* 你的邏輯 */ }
)
```

### 自定義格式化邏輯
```swift
// 修改相應的格式化方法
private func formatCoordinate(_ value: Double) -> String {
    // 你的自定義邏輯
}
```

## 測試建議

1. **不同視窗數據**: 測試有效和無效的視窗數據
2. **動畫流暢性**: 確保顯示/隱藏動畫流暢
3. **複製功能**: 驗證所有複製功能正常工作
4. **響應式設計**: 在不同窗口大小下測試
5. **無障礙功能**: 使用鍵盤導航測試

## 故障排除

### 面板不顯示
- 檢查 `isVisible` 狀態
- 確認 `window` 不為 `nil`
- 檢查父視圖的佈局約束

### 動畫不流暢
- 確保在主線程更新狀態
- 檢查是否有阻塞主線程的操作
- 考慮減少動畫持續時間

### 複製功能不工作
- 檢查應用程式的剪貼簿權限
- 確認 `NSPasteboard` 操作在主線程執行
- 驗證數據格式化是否正確

## 版本歷史

- **v1.0**: 初始實現，包含基本的信息顯示和複製功能
- 支持響應式設計和流暢動畫
- 完整的錯誤處理和無障礙支持