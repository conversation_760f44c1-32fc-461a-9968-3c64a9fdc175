import SwiftUI

// MARK: - SpaceTabButton

/**
 * Space 標籤按鈕組件
 *
 * 代表單個 macOS Space 的可點擊按鈕。顯示 Space 的名稱和狀態，
 * 支持選中狀態和當前 Space 的視覺指示。
 *
 * ## 功能特點
 * - 顯示 Space 名稱和狀態
 * - 支持選中狀態的視覺回饋
 * - 區分當前活躍 Space 和選中 Space
 * - 提供點擊動畫效果
 * - 完整的無障礙支持
 *
 * ## 使用示例
 * ```swift
 * SpaceTabButton(
 *     space: spaceInfo,
 *     isSelected: true,
 *     isCurrentSpace: false,
 *     onTap: { /* 處理點擊 */ }
 * )
 * ```
 *
 * ## 設計考量
 * - 使用不同的視覺樣式區分狀態
 * - 當前 Space 顯示特殊指示器
 * - 選中狀態使用強調色
 * - 支持懸停效果
 *
 * @since 1.0.0
 * <AUTHOR> Integration Team
 */
struct SpaceTabButton: View {
    
    // MARK: - Properties
    
    /// Space 資訊
    let space: SpaceInfo
    
    /// 是否被選中
    let isSelected: Bool
    
    /// 是否為當前 Space
    let isCurrentSpace: Bool
    
    /// 點擊回調
    let onTap: () -> Void
    
    /// 懸停狀態
    @State private var isHovering = false
    
    /// 按下狀態
    @State private var isPressed = false
    
    // MARK: - Initialization
    
    /**
     * 初始化 Space 標籤按鈕
     *
     * @param space Space 資訊
     * @param isSelected 是否被選中
     * @param isCurrentSpace 是否為當前 Space
     * @param onTap 點擊回調
     */
    init(
        space: SpaceInfo,
        isSelected: Bool,
        isCurrentSpace: Bool,
        onTap: @escaping () -> Void
    ) {
        self.space = space
        self.isSelected = isSelected
        self.isCurrentSpace = isCurrentSpace
        self.onTap = onTap
    }
    
    // MARK: - Body
    
    /**
     * 主視圖內容
     *
     * 創建包含 Space 名稱和狀態指示器的按鈕
     *
     * ## 視覺設計
     * - 選中狀態使用強調色背景
     * - 當前 Space 顯示圓點指示器
     * - 懸停時顯示輕微的背景變化
     * - 按下時提供視覺回饋
     *
     * ## 狀態組合
     * - 選中 + 當前：強調色背景 + 白色圓點
     * - 選中 + 非當前：強調色背景 + 無圓點
     * - 非選中 + 當前：透明背景 + 彩色圓點
     * - 非選中 + 非當前：透明背景 + 無圓點
     */
    var body: some View {
        Button(action: onTap) {
            buttonContent
        }
        .buttonStyle(PlainButtonStyle())
        .help(buttonTooltip)
        .accessibilityLabel(accessibilityLabel)
        .accessibilityHint(accessibilityHint)
        .accessibilityAddTraits(accessibilityTraits)
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.15)) {
                isHovering = hovering
            }
        }
        .pressEvents(
            onPress: {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = true
                }
            },
            onRelease: {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
            }
        )
    }
    
    // MARK: - Private Views
    
    /**
     * 按鈕內容視圖
     *
     * 包含 Space 名稱和狀態指示器的水平佈局
     */
    @ViewBuilder
    private var buttonContent: some View {
        HStack(spacing: 8) {
            // 當前 Space 指示器
            if isCurrentSpace {
                currentSpaceIndicator
            }
            
            // Space 名稱
            Text(space.displayName)
                .font(.system(size: 12, weight: .medium))
                .lineLimit(1)
            
            // Space ID 標籤（可選）
            if shouldShowSpaceID {
                spaceIDLabel
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(buttonBackground)
        .foregroundColor(buttonForegroundColor)
        .scaleEffect(isPressed ? 0.95 : 1.0)
    }
    
    /**
     * 當前 Space 指示器
     *
     * 小圓點，表示這是當前活躍的 Space
     */
    @ViewBuilder
    private var currentSpaceIndicator: some View {
        Circle()
            .fill(currentSpaceIndicatorColor)
            .frame(width: 6, height: 6)
            .accessibilityHidden(true)
    }
    
    /**
     * Space ID 標籤
     *
     * 顯示 Space 的數字 ID
     */
    @ViewBuilder
    private var spaceIDLabel: some View {
        Text("\(space.id)")
            .font(.system(size: 10, weight: .bold))
            .padding(.horizontal, 4)
            .padding(.vertical, 2)
            .background(
                RoundedRectangle(cornerRadius: 3)
                    .fill(spaceIDLabelBackground)
            )
            .foregroundColor(spaceIDLabelForeground)
            .accessibilityHidden(true)
    }
    
    /**
     * 按鈕背景視圖
     *
     * 根據狀態返回不同的背景樣式
     */
    @ViewBuilder
    private var buttonBackground: some View {
        RoundedRectangle(cornerRadius: 6)
            .fill(backgroundFill)
            .overlay(
                RoundedRectangle(cornerRadius: 6)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
    }
    
    // MARK: - Computed Properties
    
    /**
     * 是否應該顯示 Space ID
     */
    private var shouldShowSpaceID: Bool {
        // 在調試模式或特定情況下顯示 ID
        return false // 可以根據需要調整
    }
    
    /**
     * 按鈕背景填充色
     */
    private var backgroundFill: Color {
        if isSelected {
            return Color.accentColor
        } else if isHovering {
            return Color.secondary.opacity(0.1)
        } else {
            return Color.clear
        }
    }
    
    /**
     * 按鈕邊框顏色
     */
    private var borderColor: Color {
        if isCurrentSpace && !isSelected {
            return Color.accentColor.opacity(0.5)
        } else {
            return Color.clear
        }
    }
    
    /**
     * 按鈕邊框寬度
     */
    private var borderWidth: CGFloat {
        return isCurrentSpace && !isSelected ? 1 : 0
    }
    
    /**
     * 按鈕前景色
     */
    private var buttonForegroundColor: Color {
        return isSelected ? .white : .primary
    }
    
    /**
     * 當前 Space 指示器顏色
     */
    private var currentSpaceIndicatorColor: Color {
        if isSelected {
            return .white
        } else {
            return .accentColor
        }
    }
    
    /**
     * Space ID 標籤背景色
     */
    private var spaceIDLabelBackground: Color {
        if isSelected {
            return Color.white.opacity(0.2)
        } else {
            return Color.accentColor.opacity(0.1)
        }
    }
    
    /**
     * Space ID 標籤前景色
     */
    private var spaceIDLabelForeground: Color {
        if isSelected {
            return .white
        } else {
            return .accentColor
        }
    }
    
    /**
     * 按鈕工具提示
     */
    private var buttonTooltip: String {
        var tooltip = space.displayName
        
        if isCurrentSpace {
            tooltip += " (當前)"
        }
        
        if isSelected {
            tooltip += " - 已選中"
        }
        
        return tooltip
    }
    
    /**
     * 無障礙標籤
     */
    private var accessibilityLabel: String {
        var label = space.displayName
        
        if isCurrentSpace {
            label += "，當前 Space"
        }
        
        return label
    }
    
    /**
     * 無障礙提示
     */
    private var accessibilityHint: String {
        if isSelected {
            return "已選中此 Space"
        } else {
            return "點擊選擇此 Space"
        }
    }
    
    /**
     * 無障礙特徵
     */
    private var accessibilityTraits: AccessibilityTraits {
        var traits: AccessibilityTraits = [.isButton]
        
        if isSelected {
            traits = traits.union(.isSelected)
        }
        
        return traits
    }
}

// MARK: - Press Events Extension

/**
 * 按壓事件擴展
 *
 * 提供按下和釋放事件的處理
 */
extension View {
    func pressEvents(onPress: @escaping () -> Void, onRelease: @escaping () -> Void) -> some View {
        self.simultaneousGesture(
            DragGesture(minimumDistance: 0)
                .onChanged { _ in
                    onPress()
                }
                .onEnded { _ in
                    onRelease()
                }
        )
    }
}

// MARK: - Preview

#if DEBUG
struct SpaceTabButton_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 不同狀態的按鈕預覽
            VStack(spacing: 16) {
                // 選中 + 當前
                SpaceTabButton(
                    space: SpaceInfo(id: 1, name: "Space 1", isActive: true),
                    isSelected: true,
                    isCurrentSpace: true,
                    onTap: {}
                )
                .previewDisplayName("選中 + 當前")
                
                // 選中 + 非當前
                SpaceTabButton(
                    space: SpaceInfo(id: 2, name: "Space 2", isActive: false),
                    isSelected: true,
                    isCurrentSpace: false,
                    onTap: {}
                )
                .previewDisplayName("選中 + 非當前")
                
                // 非選中 + 當前
                SpaceTabButton(
                    space: SpaceInfo(id: 3, name: "Space 3", isActive: true),
                    isSelected: false,
                    isCurrentSpace: true,
                    onTap: {}
                )
                .previewDisplayName("非選中 + 當前")
                
                // 非選中 + 非當前
                SpaceTabButton(
                    space: SpaceInfo(id: 4, name: "Space 4", isActive: false),
                    isSelected: false,
                    isCurrentSpace: false,
                    onTap: {}
                )
                .previewDisplayName("非選中 + 非當前")
            }
            .padding()
            
            // 深色模式預覽
            VStack(spacing: 16) {
                SpaceTabButton(
                    space: SpaceInfo(id: 1, name: "Space 1", isActive: true),
                    isSelected: true,
                    isCurrentSpace: true,
                    onTap: {}
                )
                
                SpaceTabButton(
                    space: SpaceInfo(id: 2, name: "Space 2", isActive: false),
                    isSelected: false,
                    isCurrentSpace: false,
                    onTap: {}
                )
            }
            .padding()
            .preferredColorScheme(.dark)
            .previewDisplayName("深色模式")
        }
    }
}
#endif