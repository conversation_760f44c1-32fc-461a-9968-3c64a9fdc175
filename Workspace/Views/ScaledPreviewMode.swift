import SwiftUI

struct ScaledPreviewMode: View {
    let windows: [WindowLayout]
    let canvasSize: CGSize
    @Binding var selectedWindow: WindowLayout?
    @Binding var selectedWindows: Set<String>
    let configuration: PreviewConfiguration
    let isMultiSelectMode: Bool
    let focusedIndex: Int
    let onWindowTap: (WindowLayout) -> Void
    let onWindowHover: (WindowLayout?, CGPoint) -> Void
    let onWindowFocus: (Int) -> Void
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                ForEach(Array(windows.enumerated()), id: \.element.id) { index, window in
                    WindowPreviewItem(
                        window: window,
                        isSelected: selectedWindows.contains(window.id),
                        isFocused: index == focusedIndex,
                        scale: calculateScale(for: window, in: geometry.size)
                    )
                    .position(
                        x: window.frame.x * calculateScale(for: window, in: geometry.size) + geometry.size.width / 2,
                        y: window.frame.y * calculateScale(for: window, in: geometry.size) + geometry.size.height / 2
                    )
                    .onTapGesture {
                        onWindowTap(window)
                    }
                }
            }
        }
    }
    
    private func calculateScale(for window: WindowLayout, in size: CGSize) -> CGFloat {
        let scaleX = size.width / max(window.frame.w, 1)
        let scaleY = size.height / max(window.frame.h, 1)
        return min(scaleX, scaleY, 1.0) * 0.8 // 80% of calculated scale for padding
    }
}

struct WindowPreviewItem: View {
    let window: WindowLayout
    let isSelected: Bool
    let isFocused: Bool
    let scale: CGFloat
    
    var body: some View {
        RoundedRectangle(cornerRadius: 4)
            .foregroundColor(isSelected ? Color.blue.opacity(0.3) : Color.gray.opacity(0.2))
            .overlay(
                RoundedRectangle(cornerRadius: 4)
                    .stroke(isFocused ? Color.blue : Color.clear, lineWidth: 2)
            )
            .frame(
                width: window.frame.w * scale,
                height: window.frame.h * scale
            )
            .overlay(
                Text(window.app.isEmpty ? "Unknown" : window.app)
                    .font(.caption2)
                    .lineLimit(1)
            )
    }
}