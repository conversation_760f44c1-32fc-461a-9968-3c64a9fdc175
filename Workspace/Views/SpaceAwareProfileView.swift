import SwiftUI
import Combine

/**
 * Space 感知的設定檔列表視圖
 *
 * 這是主要的 UI 組件，負責顯示依照 macOS Spaces 分組的設定檔列表。
 * 支援 Space 切換、空狀態顯示和設定檔管理功能。
 *
 * ## 功能特點
 * - 依照 Space 分組顯示設定檔
 * - 支援 Space 標籤切換
 * - 空 Space 的空白狀態顯示
 * - 設定檔的還原和編輯功能
 * - 響應式設計適應不同螢幕尺寸
 *
 * ## 需求對應
 * - 需求 1.1: 依 Space 分組顯示設定檔
 * - 需求 1.4: 空 Space 顯示空白狀態
 * - 需求 5.1: 清楚的 Space 視覺指示
 * - 需求 5.4: 顯示每個 Space 的設定檔數量
 *
 * @since 1.0.0
 * <AUTHOR> Integration Team
 */
struct SpaceAwareProfileView: View {
    
    // MARK: - Properties
    
    /// Space-Profile 管理器
    @StateObject private var spaceProfileManager = SpaceProfileManager.shared
    
    /// Hammerspoon 管理器
    @StateObject private var hammerspoonManager = HammerspoonManager.shared
    
    /// Space 偵測器
    @StateObject private var spaceDetector = SpaceDetector.shared
    
    /// UI 效能最佳化器
    @StateObject private var performanceOptimizer = UIPerformanceOptimizer.shared
    
    /// 當前選中的 Space ID
    @State private var selectedSpaceID: Int?
    
    /// 選中的設定檔（用於編輯）
    @State private var selectedProfile: Profile?
    
    /// 顯示儲存對話框
    @State private var showingSaveDialog = false
    
    /// 新設定檔名稱
    @State private var newProfileName = ""
    
    /// 動畫狀態
    @State private var isAnimating = false
    
    /// 載入狀態
    @State private var isLoading = false
    
    // MARK: - Computed Properties
    
    /// 當前選中 Space 的設定檔
    private var currentSpaceProfiles: [Profile] {
        guard let spaceID = selectedSpaceID else {
            return []
        }
        return performanceOptimizer.measureUIOperation({
            return spaceProfileManager.getProfilesForSpace(spaceID)
        }, operationType: "getProfilesForSpace")
    }
    
    /// 當前選中 Space 的設定檔元資料（用於懶載入）
    private var currentSpaceProfileMetadata: [ProfileMetadata] {
        guard let spaceID = selectedSpaceID else {
            return []
        }
        return spaceProfileManager.getProfileMetadataForSpace(spaceID)
    }
    
    /// 當前選中的 Space 資訊
    private var currentSpaceInfo: SpaceInfo? {
        guard let spaceID = selectedSpaceID else { return nil }
        return spaceDetector.availableSpaces.first { $0.id == spaceID }
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 0) {
            // 標題區域
            headerView
            
            Divider()
            
            // Space 標籤選擇器
            spaceTabSection
            
            Divider()
            
            // 設定檔列表區域
            profileListSection
            
            Divider()
            
            // 儲存按鈕區域
            saveButtonSection
        }
        .frame(width: 320)
        .background(Color(NSColor.windowBackgroundColor))
        .onAppear {
            initializeView()
        }
        .onChange(of: spaceDetector.currentSpaceID) { newSpaceID in
            withAnimation(performanceOptimizer.optimizedAnimation(for: .spaceTransition)) {
                handleCurrentSpaceChange(newSpaceID)
            }
        }
        .onChange(of: spaceDetector.availableSpaces) { newSpaces in
            withAnimation(performanceOptimizer.optimizedAnimation(for: .tabSwitch)) {
                handleAvailableSpacesChange(newSpaces)
            }
        }
        .onChange(of: selectedSpaceID) { newSpaceID in
            if let spaceID = newSpaceID {
                // 預載入選中 Space 的設定檔
                spaceProfileManager.preloadSpaceProfiles(spaceID)
            }
        }
        .onReceive(spaceProfileManager.$spaceProfileMapping) { _ in
            withAnimation(performanceOptimizer.optimizedAnimation(for: .profileListUpdate)) {
                // 當映射變更時觸發 UI 更新
                // UI 會自動更新，因為 spaceProfileManager 是 @StateObject
            }
        }
        .sheet(item: $selectedProfile) { profile in
            ProfileEditorView(profile: profile)
        }
        .alert("儲存新佈局", isPresented: $showingSaveDialog) {
            TextField("Profile 名稱", text: $newProfileName)
            Button("取消", role: .cancel) {
                newProfileName = ""
            }
            Button("儲存") {
                saveNewProfile()
            }
        } message: {
            Text("請輸入新 Profile 的名稱")
        }
    }
    
    // MARK: - Header View
    
    @ViewBuilder
    private var headerView: some View {
        HStack {
            Image(systemName: "rectangle.3.group")
                .font(.title2)
                .foregroundColor(.accentColor)
            
            Text("Workspace")
                .font(.title2)
                .fontWeight(.semibold)
            
            Spacer()
            
            // Space 指示器
            if let currentSpace = currentSpaceInfo {
                HStack(spacing: 4) {
                    Image(systemName: "square.3.layers.3d")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(currentSpace.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.secondary.opacity(0.1))
                )
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    // MARK: - Space Tab Section
    
    @ViewBuilder
    private var spaceTabSection: some View {
        VStack(spacing: 8) {
            HStack {
                Text("選擇 Space")
                    .font(.headline)
                    .fontWeight(.medium)
                
                Spacer()
                
                // Space 統計資訊
                if let spaceID = selectedSpaceID {
                    let profileCount = spaceProfileManager.getProfileCount(for: spaceID)
                    Text("\(profileCount) 個設定檔")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal, 16)
            
            // Space 標籤選擇器
            SpaceTabView(selectedSpaceID: $selectedSpaceID)
                .padding(.horizontal, 16)
        }
        .padding(.vertical, 12)
        .background(Color(NSColor.controlBackgroundColor))
    }
    
    // MARK: - Profile List Section
    
    @ViewBuilder
    private var profileListSection: some View {
        if currentSpaceProfiles.isEmpty {
            emptyStateView
        } else {
            profileListView
        }
    }
    
    @ViewBuilder
    private var emptyStateView: some View {
        VStack(spacing: 12) {
            Image(systemName: "tray")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            VStack(spacing: 4) {
                Text("此 Space 尚無設定檔")
                    .font(.headline)
                    .foregroundColor(.secondary)
                
                if let spaceInfo = currentSpaceInfo {
                    Text("在 \(spaceInfo.displayName) 中儲存您的第一個佈局")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                } else {
                    Text("點擊下方按鈕儲存當前視窗佈局")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
            
            // 快速儲存按鈕
            Button(action: {
                showingSaveDialog = true
            }) {
                HStack(spacing: 6) {
                    Image(systemName: "plus.circle")
                        .font(.system(size: 16))
                    
                    Text("儲存當前佈局")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                .foregroundColor(.accentColor)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(Color.accentColor.opacity(0.1))
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 6)
                        .stroke(Color.accentColor.opacity(0.3), lineWidth: 1)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.vertical, 40)
        .accessibilityElement(children: .combine)
        .accessibilityLabel("空 Space 狀態")
        .accessibilityHint("此 Space 中沒有儲存的設定檔，點擊儲存按鈕建立新的設定檔")
    }
    
    @ViewBuilder
    private var profileListView: some View {
        ScrollView {
            LazyVStack(spacing: 2) {
                if isLoading {
                    ProgressView("載入設定檔...")
                        .frame(maxWidth: .infinity, minHeight: 100)
                        .optimizedAnimation(for: .contentLoad)
                } else {
                    ForEach(currentSpaceProfileMetadata) { metadata in
                        LazyProfileRowView(
                            metadata: metadata,
                            spaceInfo: currentSpaceInfo,
                            onRestore: { profile in
                                restoreProfile(profile)
                            },
                            onEdit: { profile in
                                selectedProfile = profile
                            }
                        )
                        .cached(key: "profile_row_\(metadata.name)")
                    }
                }
            }
            .padding(.vertical, 4)
            .optimizedAnimation(for: .profileListUpdate)
        }
        .frame(maxHeight: 400)
        .accessibilityElement(children: .contain)
        .accessibilityLabel("設定檔列表")
        .accessibilityHint("顯示當前 Space 中的所有設定檔")
    }
    
    // MARK: - Save Button Section
    
    @ViewBuilder
    private var saveButtonSection: some View {
        Button(action: {
            showingSaveDialog = true
        }) {
            HStack {
                Image(systemName: "plus.circle.fill")
                    .font(.system(size: 20))
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("儲存當前佈局")
                        .font(.headline)
                        .fontWeight(.medium)
                    
                    if let spaceInfo = currentSpaceInfo {
                        Text("儲存到 \(spaceInfo.displayName)")
                            .font(.caption)
                            .opacity(0.8)
                    }
                }
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(Color.accentColor)
            .clipShape(RoundedRectangle(cornerRadius: 8))
        }
        .buttonStyle(PlainButtonStyle())
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .accessibilityLabel("儲存當前佈局")
        .accessibilityHint("將當前視窗佈局儲存到選中的 Space")
    }
    
    // MARK: - Private Methods
    
    /// 初始化視圖
    private func initializeView() {
        // 載入設定檔和映射
        spaceProfileManager.reloadMappings()
        
        // 設定初始選中的 Space
        if selectedSpaceID == nil {
            if let currentSpaceID = spaceDetector.currentSpaceID {
                selectedSpaceID = currentSpaceID
            } else if let firstSpace = spaceDetector.availableSpaces.first {
                selectedSpaceID = firstSpace.id
            }
        }
        
        // 檢查 Hammerspoon 安裝狀態
        hammerspoonManager.checkInstallation()
    }
    
    /// 處理當前 Space 變更
    private func handleCurrentSpaceChange(_ newSpaceID: Int?) {
        print("SpaceAwareProfileView: 偵測到 Space 變更 \(selectedSpaceID ?? -1) -> \(newSpaceID ?? -1)")
        
        performanceOptimizer.startAnimation("space_change", context: .spaceTransition)
        
        // 可以選擇是否自動切換到當前 Space
        // 這裡保持用戶的選擇，不自動切換，但提供視覺反饋
        
        // 觸發動畫更新
        isAnimating.toggle()
        
        // 如果當前沒有選中的 Space，自動選擇當前 Space
        if selectedSpaceID == nil, let newSpaceID = newSpaceID {
            selectedSpaceID = newSpaceID
        }
        
        performanceOptimizer.endAnimation("space_change")
    }
    
    /// 處理可用 Spaces 變更
    private func handleAvailableSpacesChange(_ newSpaces: [SpaceInfo]) {
        print("SpaceAwareProfileView: 可用 Spaces 變更，數量: \(newSpaces.count)")
        
        // 如果選中的 Space 不再可用，切換到第一個可用的 Space
        if let selectedID = selectedSpaceID,
           !newSpaces.contains(where: { $0.id == selectedID }) {
            selectedSpaceID = newSpaces.first?.id
        }
        
        // 如果沒有選中的 Space 且有可用的 Spaces，選擇第一個
        if selectedSpaceID == nil, let firstSpace = newSpaces.first {
            selectedSpaceID = firstSpace.id
        }
    }
    
    /// 還原設定檔
    private func restoreProfile(_ profile: Profile) {
        // 檢查設定檔是否屬於當前 Space
        if let profileSpaceID = profile.spaceID,
           profileSpaceID != selectedSpaceID {
            // 跨 Space 還原警告
            showCrossSpaceRestoreAlert(profile: profile)
            return
        }
        
        // 執行還原
        if profile.isSpaceSpecific {
            hammerspoonManager.restoreCurrentSpaceLayout(profileName: profile.name)
        } else {
            hammerspoonManager.restoreLayout(profileName: profile.name)
        }
    }
    
    /// 顯示跨 Space 還原警告
    private func showCrossSpaceRestoreAlert(profile: Profile) {
        let alert = NSAlert()
        alert.messageText = "跨 Space 還原警告"
        alert.informativeText = "此設定檔屬於不同的 Space，確定要在當前 Space 中還原嗎？"
        alert.alertStyle = .warning
        alert.addButton(withTitle: "確定還原")
        alert.addButton(withTitle: "取消")
        
        let response = alert.runModal()
        if response == .alertFirstButtonReturn {
            if profile.isSpaceSpecific {
                hammerspoonManager.restoreCurrentSpaceLayout(profileName: profile.name)
            } else {
                hammerspoonManager.restoreLayout(profileName: profile.name)
            }
        }
    }
    
    /// 儲存新設定檔
    private func saveNewProfile() {
        guard !newProfileName.isEmpty,
              let _ = selectedSpaceID else {
            return
        }
        
        // 使用 Space-specific 保存函數
        hammerspoonManager.saveCurrentSpaceLayout(profileName: newProfileName)
        
        // 清空輸入
        newProfileName = ""
        
        // 延遲重新載入以確保檔案已儲存
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            spaceProfileManager.reloadMappings()
        }
    }
}

// MARK: - LazyProfileRowView

/**
 * 懶載入的設定檔行視圖
 *
 * 使用設定檔元資料顯示基本資訊，按需載入完整設定檔
 */
struct LazyProfileRowView: View {
    let metadata: ProfileMetadata
    let spaceInfo: SpaceInfo?
    let onRestore: (Profile) -> Void
    let onEdit: (Profile) -> Void
    
    @State private var loadedProfile: Profile?
    @State private var isLoading = false
    @StateObject private var lazyLoader = LazyProfileLoader.shared
    
    var body: some View {
        Button(action: {
            loadProfileAndRestore()
        }) {
            HStack(spacing: 8) {
                // 設定檔縮圖（使用元資料）
                MetadataProfileThumbnailView(metadata: metadata)
                
                // 設定檔資訊
                VStack(alignment: .leading, spacing: 1) {
                    HStack(spacing: 4) {
                        Text(metadata.displayName)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .lineLimit(1)
                        
                        // Space 指示器
                        if metadata.isSpaceSpecific {
                            Image(systemName: "square.3.layers.3d")
                                .font(.system(size: 10))
                                .foregroundColor(.accentColor)
                        }
                        
                        // 載入指示器
                        if isLoading {
                            ProgressView()
                                .scaleEffect(0.5)
                        }
                    }
                    
                    HStack(spacing: 4) {
                        Text(metadata.previewDescription)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(formatRelativeDate(metadata.modifiedAt))
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        // Space 名稱（如果與當前不同）
                        if let profileSpaceID = metadata.spaceID,
                           let currentSpaceID = spaceInfo?.id,
                           profileSpaceID != currentSpaceID {
                            Text("•")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Text("來自其他 Space")
                                .font(.caption)
                                .foregroundColor(.orange)
                        }
                    }
                }
                
                Spacer()
                
                // 編輯按鈕
                Button(action: {
                    loadProfileAndEdit()
                }) {
                    Image(systemName: "pencil")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
                .buttonStyle(PlainButtonStyle())
                .optimizedHover()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .optimizedHover()
        .background(
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.clear)
                .contentShape(Rectangle())
        )
        .accessibilityElement(children: .combine)
        .accessibilityLabel("設定檔 \(metadata.displayName)")
        .accessibilityHint("點擊還原此設定檔，或使用編輯按鈕進行編輯")
    }
    
    private func loadProfileAndRestore() {
        guard !isLoading else { return }
        
        if let profile = loadedProfile {
            onRestore(profile)
            return
        }
        
        isLoading = true
        lazyLoader.loadProfile(metadata.name)
            .receive(on: DispatchQueue.main)
            .sink { profile in
                isLoading = false
                if let profile = profile {
                    loadedProfile = profile
                    onRestore(profile)
                }
            }
            .store(in: &cancellables)
    }
    
    private func loadProfileAndEdit() {
        guard !isLoading else { return }
        
        if let profile = loadedProfile {
            onEdit(profile)
            return
        }
        
        isLoading = true
        lazyLoader.loadProfile(metadata.name)
            .receive(on: DispatchQueue.main)
            .sink { profile in
                isLoading = false
                if let profile = profile {
                    loadedProfile = profile
                    onEdit(profile)
                }
            }
            .store(in: &cancellables)
    }
    
    @State private var cancellables = Set<AnyCancellable>()
}

// MARK: - MetadataProfileThumbnailView

/**
 * 基於元資料的設定檔縮圖視圖
 */
struct MetadataProfileThumbnailView: View {
    let metadata: ProfileMetadata
    
    var body: some View {
        RoundedRectangle(cornerRadius: 4)
            .fill(Color.accentColor.opacity(0.1))
            .frame(width: 32, height: 24)
            .overlay(
                VStack(spacing: 1) {
                    Text("\(metadata.windowCount)")
                        .font(.system(size: 8, weight: .bold))
                        .foregroundColor(.accentColor)
                    
                    Text("視窗")
                        .font(.system(size: 6))
                        .foregroundColor(.secondary)
                }
            )
    }
}

// MARK: - SpaceAwareProfileRowView (保留向後相容性)

/**
 * Space 感知的設定檔行視圖
 *
 * 顯示單個設定檔的資訊，包括 Space 上下文和操作按鈕
 */
struct SpaceAwareProfileRowView: View {
    let profile: Profile
    let spaceInfo: SpaceInfo?
    let onRestore: () -> Void
    let onEdit: () -> Void
    
    var body: some View {
        Button(action: onRestore) {
            HStack(spacing: 8) {
                // 設定檔縮圖
                CompactProfileThumbnailView(profile: profile)
                
                // 設定檔資訊
                VStack(alignment: .leading, spacing: 1) {
                    HStack(spacing: 4) {
                        Text(profile.displayName)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .lineLimit(1)
                        
                        // Space 指示器
                        if profile.isSpaceSpecific {
                            Image(systemName: "square.3.layers.3d")
                                .font(.system(size: 10))
                                .foregroundColor(.accentColor)
                        }
                    }
                    
                    HStack(spacing: 4) {
                        Text(profile.previewDescription)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(formatRelativeDate(profile.modifiedAt))
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        // Space 名稱（如果與當前不同）
                        if let profileSpaceID = profile.spaceID,
                           let currentSpaceID = spaceInfo?.id,
                           profileSpaceID != currentSpaceID {
                            Text("•")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            Text("來自其他 Space")
                                .font(.caption)
                                .foregroundColor(.orange)
                        }
                    }
                }
                
                Spacer()
                
                // 編輯按鈕
                CompactEditButton(action: onEdit)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .optimizedHover()
        .background(
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.clear)
                .contentShape(Rectangle())
        )
        .accessibilityElement(children: .combine)
        .accessibilityLabel("設定檔 \(profile.displayName)")
        .accessibilityHint("點擊還原此設定檔，或使用編輯按鈕進行編輯")
    }
}

// MARK: - Helper Functions

private func formatRelativeDate(_ date: Date) -> String {
    let formatter = RelativeDateTimeFormatter()
    formatter.locale = Locale(identifier: "zh_TW")
    return formatter.localizedString(for: date, relativeTo: Date())
}

// MARK: - Preview

#if DEBUG
struct SpaceAwareProfileView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            SpaceAwareProfileView()
                .previewDisplayName("正常狀態")
            
            SpaceAwareProfileView()
                .previewDisplayName("深色模式")
                .preferredColorScheme(.dark)
        }
    }
}
#endif