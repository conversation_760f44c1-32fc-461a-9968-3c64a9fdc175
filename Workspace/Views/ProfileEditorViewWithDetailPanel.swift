import SwiftUI

// MARK: - Enhanced ProfileEditorView with WindowDetailPanel
struct ProfileEditorViewWithDetailPanel: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var profileManager = ProfileManager.shared

    let originalProfile: Profile
    @State private var currentProfile: Profile
    @State private var editedName: String
    @State private var showingDeleteAlert = false
    @State private var showingRenameAlert = false
    
    // WindowDetailPanel state
    @State private var selectedWindow: WindowLayout?
    @State private var showDetailPanel = false

    init(profile: Profile) {
        self.originalProfile = profile
        self._currentProfile = State(initialValue: profile)
        self._editedName = State(initialValue: profile.name)
    }
    
    var body: some View {
        HStack(spacing: 0) {
            // 主要內容區域
            VStack(spacing: 0) {
                // 標題列
                HStack {
                    Text("編輯 Profile")
                        .font(.title)
                        .fontWeight(.semibold)
                    
                    Spacer()
                    
                    Button("完成") {
                        dismiss()
                    }
                    .buttonStyle(.borderedProminent)
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 16)
                .background(Color(NSColor.controlBackgroundColor))
                
                Divider()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // Profile 資訊
                        profileInfoSection
                        
                        Divider()
                        
                        // 視覺化佈局預覽
                        layoutPreviewSection
                        
                        Divider()
                        
                        // 視窗列表
                        windowListSection
                        
                        // 危險操作區域
                        dangerZoneSection
                    }
                    .padding(.vertical, 24)
                }
            }
            .frame(maxWidth: .infinity)
            
            // WindowDetailPanel
            WindowDetailPanel(
                window: selectedWindow,
                isVisible: showDetailPanel,
                onClose: {
                    withAnimation {
                        showDetailPanel = false
                        selectedWindow = nil
                    }
                }
            )
        }
        .frame(width: showDetailPanel ? 880 : 600, height: 700)
        .background(Color(NSColor.windowBackgroundColor))
        .animation(.easeInOut(duration: 0.3), value: showDetailPanel)
        .alert("確認重命名", isPresented: $showingRenameAlert) {
            Button("取消", role: .cancel) { }
            Button("重命名") {
                profileManager.renameProfile(originalProfile, to: editedName)
            }
        } message: {
            Text("確定要將 Profile 重命名為「\(editedName)」嗎？")
        }
        .alert("確認刪除", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("刪除", role: .destructive) {
                profileManager.deleteProfile(originalProfile)
                DispatchQueue.main.async {
                    dismiss()
                }
            }
        } message: {
            Text("確定要刪除 Profile「\(originalProfile.name)」嗎？此操作無法復原。")
        }
        .onAppear {
            updateCurrentProfile()
        }
        .onChange(of: profileManager.profiles) { _ in
            updateCurrentProfile()
        }
    }
    
    // MARK: - Profile Info Section (Compact Design)
    private var profileInfoSection: some View {
        HStack(spacing: 20) {
            // Profile 圖示和基本資訊
            HStack(spacing: 12) {
                // Profile 圖示
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.accentColor.opacity(0.1))
                    .frame(width: 48, height: 48)
                    .overlay(
                        Image(systemName: "rectangle.3.group")
                            .font(.title2)
                            .foregroundColor(.accentColor)
                    )

                VStack(alignment: .leading, spacing: 2) {
                    Text("Profile 資訊")
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text("\(currentProfile.windows?.count ?? 0) 個視窗")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            Spacer()

            // 名稱編輯區域
            HStack(spacing: 8) {
                VStack(alignment: .leading, spacing: 2) {
                    Text("名稱")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    TextField("Profile 名稱", text: $editedName)
                        .textFieldStyle(.roundedBorder)
                        .frame(width: 180)
                }

                Button("重命名") {
                    showingRenameAlert = true
                }
                .buttonStyle(.bordered)
                .disabled(editedName == originalProfile.name || editedName.isEmpty)
            }

            Spacer()

            // 統計資訊
            VStack(alignment: .trailing, spacing: 2) {
                Text("修改時間")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text(formatDate(currentProfile.modifiedAt))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(NSColor.controlBackgroundColor))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.secondary.opacity(0.2), lineWidth: 1)
                )
        )
        .padding(.horizontal, 24)
    }
    
    // MARK: - Layout Preview Section
    private var layoutPreviewSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("佈局預覽")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if showDetailPanel {
                    Button("關閉詳細面板") {
                        withAnimation {
                            showDetailPanel = false
                            selectedWindow = nil
                        }
                    }
                    .buttonStyle(.bordered)
                }
            }
            .padding(.horizontal, 24)
            
            EnhancedLayoutPreviewView(
                profile: currentProfile,
                selectedWindow: $selectedWindow,
                onWindowSelected: { window in
                    selectedWindow = window
                    withAnimation {
                        showDetailPanel = true
                    }
                }
            )
            .frame(height: 300)
            .padding(.horizontal, 24)
        }
    }
    
    // MARK: - Window List Section
    private var windowListSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("視窗列表")
                .font(.title2)
                .fontWeight(.semibold)
                .padding(.horizontal, 24)
            
            LazyVStack(spacing: 8) {
                ForEach(currentProfile.windows ?? []) { window in
                    EnhancedWindowRowView(
                        window: window,
                        isSelected: selectedWindow?.id == window.id,
                        onTap: {
                            selectedWindow = window
                            withAnimation {
                                showDetailPanel = true
                            }
                        }
                    )
                }
            }
            .padding(.horizontal, 24)
        }
    }
    
    // MARK: - Danger Zone Section
    private var dangerZoneSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("危險操作")
                .font(.title2)
                .fontWeight(.semibold)
                .foregroundColor(.red)
                .padding(.horizontal, 24)
            
            Button(action: {
                showingDeleteAlert = true
            }) {
                HStack {
                    Image(systemName: "trash")
                    Text("刪除 Profile")
                        .font(.headline)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(Color.red)
                .clipShape(RoundedRectangle(cornerRadius: 8))
            }
            .buttonStyle(PlainButtonStyle())
            .padding(.horizontal, 24)
        }
    }
    
    // MARK: - Helper Methods
    private func updateCurrentProfile() {
        guard let updatedProfile = profileManager.profiles.first(where: { $0.id == originalProfile.id }) else {
            print("⚠️ ProfileEditorView: 無法找到 ID 為 '\(originalProfile.id)' 的 Profile")
            DispatchQueue.main.async {
                dismiss()
            }
            return
        }
        
        print("🔄 ProfileEditorView: 更新 Profile '\(currentProfile.name)' (舊ID: \(currentProfile.id), 新ID: \(updatedProfile.id))")
        
        DispatchQueue.main.async {
            self.currentProfile = updatedProfile
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_TW")
        return formatter.string(from: date)
    }
}

// MARK: - Enhanced Layout Preview View
struct EnhancedLayoutPreviewView: View {
    let profile: Profile
    @Binding var selectedWindow: WindowLayout?
    let onWindowSelected: (WindowLayout) -> Void
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(NSColor.controlBackgroundColor))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.secondary.opacity(0.3), lineWidth: 1)
                    )
                
                // 模擬螢幕邊界
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.secondary.opacity(0.5), lineWidth: 2)
                    .padding(20)
                
                // 視窗預覽
                ForEach(profile.windows ?? []) { window in
                    EnhancedWindowPreviewView(
                        window: window,
                        canvasSize: geometry.size,
                        isSelected: selectedWindow?.id == window.id,
                        onTap: {
                            onWindowSelected(window)
                        }
                    )
                }
            }
        }
    }
}

// MARK: - Enhanced Window Preview View
struct EnhancedWindowPreviewView: View {
    let window: WindowLayout
    let canvasSize: CGSize
    let isSelected: Bool
    let onTap: () -> Void
    
    private var scaledFrame: CGRect {
        let screenWidth: CGFloat = 2560
        let screenHeight: CGFloat = 1440
        let padding: CGFloat = 40
        
        guard canvasSize.width > padding && canvasSize.height > padding else {
            return CGRect(x: 20, y: 20, width: 50, height: 30)
        }
        
        let availableWidth = canvasSize.width - padding
        let availableHeight = canvasSize.height - padding
        
        let scaleX = availableWidth / screenWidth
        let scaleY = availableHeight / screenHeight
        let scale = min(scaleX, scaleY)
        
        let safeX = window.frame.x.isFinite ? window.frame.x : 0
        let safeY = window.frame.y.isFinite ? window.frame.y : 0
        let safeW = window.frame.w.isFinite && window.frame.w > 0 ? window.frame.w : 100
        let safeH = window.frame.h.isFinite && window.frame.h > 0 ? window.frame.h : 100
        
        let scaledX = (safeX + screenWidth/2) * scale + padding/2
        let scaledY = (safeY + screenHeight/2) * scale + padding/2
        let scaledWidth = safeW * scale
        let scaledHeight = safeH * scale
        
        let finalX = scaledX.isFinite ? scaledX : padding/2
        let finalY = scaledY.isFinite ? scaledY : padding/2
        let finalWidth = scaledWidth.isFinite && scaledWidth > 0 ? scaledWidth : 50
        let finalHeight = scaledHeight.isFinite && scaledHeight > 0 ? scaledHeight : 30
        
        return CGRect(x: finalX, y: finalY, width: finalWidth, height: finalHeight)
    }
    
    var body: some View {
        Button(action: onTap) {
            RoundedRectangle(cornerRadius: 4)
                .fill(isSelected ? Color.accentColor.opacity(0.7) : Color.accentColor.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 4)
                        .stroke(isSelected ? Color.accentColor : Color.accentColor.opacity(0.7), lineWidth: isSelected ? 3 : 1)
                )
                .overlay(
                    // 選中時的外層高亮邊框
                    RoundedRectangle(cornerRadius: 4)
                        .stroke(Color.white.opacity(isSelected ? 0.8 : 0), lineWidth: isSelected ? 1 : 0)
                        .padding(1)
                )
                .overlay(
                    VStack(spacing: 2) {
                        if let icon = AppIconProvider.getIconWithFallback(bundleID: window.bundleID.isEmpty ? nil : window.bundleID, appName: window.app.isEmpty ? nil : window.app) {
                            Image(nsImage: icon)
                                .resizable()
                                .frame(width: 16, height: 16)
                        } else {
                            Image(systemName: "app")
                                .frame(width: 16, height: 16)
                                .foregroundColor(.secondary)
                        }

                        Text(window.app)
                            .font(.system(size: 12, weight: isSelected ? .bold : .medium))
                            .lineLimit(1)
                            .foregroundColor(isSelected ? .white : .primary)
                    }
                    .padding(4)
                )
                .scaleEffect(isSelected ? 1.08 : 1.0)
                .shadow(color: isSelected ? Color.accentColor.opacity(0.5) : Color.clear, radius: isSelected ? 4 : 0)
                .animation(.easeInOut(duration: 0.25), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
        .frame(width: scaledFrame.width, height: scaledFrame.height)
        .position(x: scaledFrame.midX, y: scaledFrame.midY)
        .help("點擊查看 \(window.app) 的詳細信息")
    }
}

// MARK: - Enhanced Window Row View
struct EnhancedWindowRowView: View {
    let window: WindowLayout
    let isSelected: Bool
    let onTap: () -> Void
    
    private func safeInt(_ value: Double) -> Int {
        guard value.isFinite else { return 0 }
        return Int(value)
    }
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // 應用程式圖示
                if let icon = AppIconProvider.getIconWithFallback(bundleID: window.bundleID.isEmpty ? nil : window.bundleID, appName: window.app.isEmpty ? nil : window.app) {
                    Image(nsImage: icon)
                        .resizable()
                        .frame(width: 32, height: 32)
                        .clipShape(RoundedRectangle(cornerRadius: 6))
                } else {
                    RoundedRectangle(cornerRadius: 6)
                        .fill(Color.secondary.opacity(0.3))
                        .frame(width: 32, height: 32)
                        .overlay(
                            Image(systemName: "app")
                                .foregroundColor(.secondary)
                        )
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(window.app)
                        .font(.headline)
                        .fontWeight(isSelected ? .bold : .medium)
                        .lineLimit(1)
                        .foregroundColor(isSelected ? .accentColor : .primary)

                    Text(window.title)
                        .font(.body)
                        .foregroundColor(isSelected ? .primary : .secondary)
                        .lineLimit(1)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text("\(safeInt(window.frame.w)) × \(safeInt(window.frame.h))")
                        .font(.caption)
                        .fontWeight(isSelected ? .semibold : .regular)
                        .foregroundColor(isSelected ? .accentColor : .secondary)

                    Text("(\(safeInt(window.frame.x)), \(safeInt(window.frame.y)))")
                        .font(.caption)
                        .fontWeight(isSelected ? .semibold : .regular)
                        .foregroundColor(isSelected ? .accentColor : .secondary)
                }

                Image(systemName: isSelected ? "info.circle.fill" : "info.circle")
                    .font(.body)
                    .foregroundColor(isSelected ? .accentColor : .secondary)
                    .scaleEffect(isSelected ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 0.2), value: isSelected)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? Color.accentColor.opacity(0.15) : Color(NSColor.controlBackgroundColor))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(isSelected ? Color.accentColor : Color.secondary.opacity(0.2), lineWidth: isSelected ? 2 : 1)
            )
            .overlay(
                // 選中時的內層高亮效果
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.white.opacity(isSelected ? 0.3 : 0), lineWidth: isSelected ? 1 : 0)
                    .padding(1)
            )
            .scaleEffect(isSelected ? 1.02 : 1.0)
            .shadow(color: isSelected ? Color.accentColor.opacity(0.3) : Color.clear, radius: isSelected ? 2 : 0)
            .animation(.easeInOut(duration: 0.25), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
        .help("點擊查看詳細信息")
    }
}

// MARK: - Preview
#Preview {
    let sampleProfile = Profile(
        name: "測試 Profile",
        windows: [
            WindowLayout(
                app: "Safari",
                bundleID: "com.apple.Safari",
                title: "Apple - 官方網站",
                frame: WindowLayout.WindowFrame(x: 100, y: 200, w: 1200, h: 800)
            ),
            WindowLayout(
                app: "Xcode",
                bundleID: "com.apple.dt.Xcode",
                title: "WindowDetailPanel.swift",
                frame: WindowLayout.WindowFrame(x: 300, y: 100, w: 1400, h: 900)
            )
        ]
    )
    
    ProfileEditorViewWithDetailPanel(profile: sampleProfile)
}