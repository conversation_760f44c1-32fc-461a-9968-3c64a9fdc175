import SwiftUI

struct MainMenuView: View {
    @StateObject private var profileManager = ProfileManager.shared
    @StateObject private var hammerspoonManager = HammerspoonManager.shared
    @StateObject private var spaceDetector = SpaceDetector.shared
    @State private var showingSettings = false
    @State private var showingKeyboardShortcuts = false
    @State private var useSpaceAwareView = true // 控制是否使用 Space 感知視圖

    @State private var selectedProfile: Profile?
    @State private var showingSaveDialog = false
    @State private var newProfileName = ""

    var body: some View {
        VStack(spacing: 0) {
            // 檢查是否應該使用 Space 感知視圖
            if useSpaceAwareView && !spaceDetector.availableSpaces.isEmpty {
                // 使用新的 Space 感知視圖
                SpaceAwareProfileView()
                    .onReceive(spaceDetector.$currentSpaceID) { newSpaceID in
                        // 當 Space 變更時同步狀態
                        handleSpaceChange(newSpaceID)
                    }
            } else {
                // 使用傳統視圖作為後備
                traditionalView
            }
            
            // 底部選項區域
            bottomOptionsSection
        }
        .frame(width: 320)
        .background(Color(NSColor.windowBackgroundColor))
        .onAppear {
            initializeMainView()
        }
        .sheet(isPresented: $showingSettings) {
            SettingsView()
        }
        .sheet(isPresented: $showingKeyboardShortcuts) {
            KeyboardShortcutsView()
        }
        .sheet(item: $selectedProfile) { profile in
            ProfileEditorView(profile: profile)
        }
        .alert("儲存新佈局", isPresented: $showingSaveDialog) {
            TextField("Profile 名稱", text: $newProfileName)
            Button("取消", role: .cancel) {
                newProfileName = ""
            }
            Button("儲存") {
                if !newProfileName.isEmpty {
                    // 使用 Space-specific 保存函數，只保存當前 Space 的視窗
                    hammerspoonManager.saveCurrentSpaceLayout(profileName: newProfileName)
                    newProfileName = ""

                    // 延遲重新載入 profiles
                    DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                        profileManager.loadProfiles()
                    }
                }
            }
        } message: {
            Text("請輸入新 Profile 的名稱")
        }
    }
    
    // MARK: - Traditional View (Fallback)
    
    @ViewBuilder
    private var traditionalView: some View {
        VStack(spacing: 0) {
            // 緊湊的標題區域
            HStack {
                Image(systemName: "rectangle.3.group")
                    .font(.title2)
                    .foregroundColor(.accentColor)

                Text("Workspace")
                    .font(.title2)
                    .fontWeight(.semibold)

                Spacer()
                
                // Space 狀態指示器
                if !spaceDetector.availableSpaces.isEmpty {
                    Button(action: {
                        useSpaceAwareView = true
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "square.3.layers.3d")
                                .font(.caption)
                            Text("啟用 Space")
                                .font(.caption)
                        }
                        .foregroundColor(.accentColor)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill(Color.accentColor.opacity(0.1))
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(NSColor.controlBackgroundColor))

            Divider()

            // Profile 列表 - 移除高度限制，讓它自適應
            if profileManager.profiles.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "tray")
                        .font(.system(size: 32))
                        .foregroundColor(.secondary)

                    Text("尚無儲存的佈局")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Text("點擊下方按鈕儲存當前視窗佈局")
                        .font(.caption)
                        .foregroundColor(Color.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.vertical, 20)
            } else {
                ScrollView {
                    LazyVStack(spacing: 2) {
                        ForEach(profileManager.profiles) { profile in
                            CompactProfileRowView(
                                profile: profile,
                                onRestore: {
                                    if profile.isSpaceSpecific {
                                        hammerspoonManager.restoreCurrentSpaceLayout(profileName: profile.name)
                                    } else {
                                        hammerspoonManager.restoreLayout(profileName: profile.name)
                                    }
                                },
                                onEdit: {
                                    print("🔧 EditButton 被點擊，Profile: \(profile.name)")
                                    print("🔧 Profile ID: \(profile.id)")
                                    print("🔧 Profile windows count: \(profile.windows?.count ?? 0)")
                                    print("🔧 設置 selectedProfile...")
                                    selectedProfile = profile
                                    print("🔧 selectedProfile 已設置: \(selectedProfile?.name ?? "nil")")
                                }
                            )
                        }
                    }
                    .padding(.vertical, 4)
                }
                .frame(maxHeight: 400) // 增加最大高度
            }

            Divider()

            // 儲存當前佈局按鈕
            Button(action: {
                showingSaveDialog = true
            }) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 20))

                    Text("儲存當前佈局")
                        .font(.headline)
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 10)
                .background(Color.accentColor)
                .clipShape(RoundedRectangle(cornerRadius: 8))
            }
            .buttonStyle(PlainButtonStyle())
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
        }
    }
    
    // MARK: - Bottom Options Section
    
    @ViewBuilder
    private var bottomOptionsSection: some View {
        VStack(spacing: 0) {
            Divider()
            
            // 緊湊的底部選項
            HStack(spacing: 0) {
                CompactMenuButton(
                    icon: "keyboard",
                    action: { showingKeyboardShortcuts = true }
                )

                CompactMenuButton(
                    icon: "gearshape",
                    action: { showingSettings = true }
                )

                Spacer()
                
                // Space 模式切換按鈕
                if !spaceDetector.availableSpaces.isEmpty {
                    CompactMenuButton(
                        icon: useSpaceAwareView ? "square.3.layers.3d.slash" : "square.3.layers.3d",
                        action: { 
                            withAnimation(.easeInOut(duration: 0.3)) {
                                useSpaceAwareView.toggle()
                            }
                        }
                    )
                }

                CompactMenuButton(
                    icon: "info.circle",
                    action: { showAbout() }
                )

                CompactMenuButton(
                    icon: "arrow.clockwise",
                    action: { hammerspoonManager.reloadConfiguration() }
                )

                CompactMenuButton(
                    icon: "power",
                    action: { NSApplication.shared.terminate(nil) }
                )
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 6)
        }
    }
    
    // MARK: - Private Methods
    
    /// 初始化主視圖
    private func initializeMainView() {
        profileManager.loadProfiles()
        hammerspoonManager.checkInstallation()
        
        // 檢查 Space 功能是否可用
        checkSpaceAvailability()
        
        // 初始化 Space 感知功能
        initializeSpaceAwareness()
    }
    
    /// 檢查 Space 功能可用性
    private func checkSpaceAvailability() {
        // 檢查是否有可用的 Spaces
        if spaceDetector.availableSpaces.isEmpty {
            // 嘗試重新偵測 Spaces
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                if !spaceDetector.availableSpaces.isEmpty {
                    useSpaceAwareView = true
                }
            }
        }
    }
    
    /// 初始化 Space 感知功能
    private func initializeSpaceAwareness() {
        // 如果有可用的 Spaces，預設使用 Space 感知視圖
        if !spaceDetector.availableSpaces.isEmpty {
            useSpaceAwareView = true
        }
        
        // 確保 SpaceProfileManager 已初始化
        _ = SpaceProfileManager.shared
    }
    
    /// 處理 Space 變更
    private func handleSpaceChange(_ newSpaceID: Int?) {
        // 當 Space 變更時，可以執行額外的同步邏輯
        print("主視圖偵測到 Space 變更: \(newSpaceID ?? -1)")
        
        // 觸發 UI 更新
        withAnimation(.easeInOut(duration: 0.2)) {
            // 可以在這裡添加額外的狀態同步邏輯
        }
    }

    private func showAbout() {
        let alert = NSAlert()
        alert.messageText = "Workspace for macOS"
        alert.informativeText = "版本 1.0\n\n一個優雅的 macOS 視窗佈局管理工具，為專業人士和高級用戶提供直觀的圖形化介面來管理基於 Hammerspoon 的多個視窗佈局 Profile。"
        alert.alertStyle = .informational
        alert.addButton(withTitle: "確定")
        alert.runModal()
    }
}

// MARK: - Supporting Views

struct CompactProfileRowView: View {
    let profile: Profile
    let onRestore: () -> Void
    let onEdit: () -> Void

    var body: some View {
        Button(action: onRestore) {
            HStack(spacing: 8) {
                CompactProfileThumbnailView(profile: profile)

                VStack(alignment: .leading, spacing: 1) {
                    Text(profile.displayName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .lineLimit(1)

                    HStack(spacing: 4) {
                        Text(profile.previewDescription)
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text(formatRelativeDate(profile.modifiedAt))
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                CompactEditButton(action: onEdit)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .hoverEffect()
        .background(
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.clear)
                .contentShape(Rectangle())
        )
    }
}

struct ProfileRowView: View {
    let profile: Profile
    let onRestore: () -> Void
    let onEdit: () -> Void

    var body: some View {
        Button(action: onRestore) {
            HStack(spacing: 12) {
                ProfileThumbnailView(profile: profile)
                ProfileInfoView(profile: profile)
                Spacer()
                EditButton(action: onEdit)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .hoverEffect()
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color.clear)
                .contentShape(Rectangle())
        )
    }
}

struct ProfileThumbnailView: View {
    let profile: Profile

    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 6)
                .fill(Color.accentColor.opacity(0.1))
                .frame(width: 40, height: 30)

            HStack(spacing: -4) {
                let windowsToShow = getWindowsForPreview(for: profile)
                ForEach(Array(windowsToShow.enumerated()), id: \.offset) { index, window in
                    if let icon = AppIconProvider.getIconWithFallback(bundleID: window.bundleID.isEmpty ? nil : window.bundleID, appName: window.app.isEmpty ? nil : window.app) {
                        Image(nsImage: icon)
                            .resizable()
                            .frame(width: 12, height: 12)
                            .clipShape(RoundedRectangle(cornerRadius: 2))
                            .overlay(
                                RoundedRectangle(cornerRadius: 2)
                                    .stroke(Color.white, lineWidth: 0.5)
                            )
                            .zIndex(Double(3 - index))
                    } else {
                        RoundedRectangle(cornerRadius: 2)
                            .fill(Color.secondary.opacity(0.3))
                            .frame(width: 12, height: 12)
                            .overlay(
                                Image(systemName: "app")
                                    .font(.system(size: 8))
                                    .foregroundColor(.secondary)
                            )
                            .zIndex(Double(3 - index))
                    }
                }

                if (profile.windows?.count ?? 0) > 3 {
                    Text("+\((profile.windows?.count ?? 0) - 3)")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.secondary)
                }
            }
        }
    }
}

struct ProfileInfoView: View {
    let profile: Profile

    var body: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text(profile.displayName)
                .font(.headline)
                .fontWeight(.medium)
                .lineLimit(1)

            HStack(spacing: 4) {
                Text(profile.previewDescription)
                    .font(.body)
                    .foregroundColor(.secondary)

                Text("•")
                    .font(.body)
                    .foregroundColor(Color.secondary)

                Text(formatRelativeDate(profile.modifiedAt))
                    .font(.body)
                    .foregroundColor(Color.secondary)
            }
        }
    }
}

struct EditButton: View {
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Image(systemName: "pencil")
                .font(.system(size: 16))
                .foregroundColor(.secondary)
        }
        .buttonStyle(PlainButtonStyle())
        .help("編輯")
    }
}

struct MenuOptionButton: View {
    let icon: String
    let title: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 18))
                    .foregroundColor(.secondary)
                    .frame(width: 20)

                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)

                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .hoverEffect()
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(Color.clear)
                .contentShape(Rectangle())
        )
    }
}

// MARK: - Helper Functions

private func getWindowsForPreview(for profile: Profile) -> [WindowLayout] {
    return Array((profile.windows ?? []).prefix(3))
}

private func formatRelativeDate(_ date: Date) -> String {
    let formatter = RelativeDateTimeFormatter()
    formatter.locale = Locale(identifier: "zh_TW")
    return formatter.localizedString(for: date, relativeTo: Date())
}

// MARK: - Compact Components

struct CompactProfileThumbnailView: View {
    let profile: Profile

    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.accentColor.opacity(0.1))
                .frame(width: 32, height: 24)

            HStack(spacing: -2) {
                let windowsToShow = getWindowsForPreview(for: profile)
                ForEach(Array(windowsToShow.enumerated()), id: \.offset) { index, window in
                    if let icon = AppIconProvider.getIconWithFallback(bundleID: window.bundleID.isEmpty ? nil : window.bundleID, appName: window.app.isEmpty ? nil : window.app) {
                        Image(nsImage: icon)
                            .resizable()
                            .frame(width: 10, height: 10)
                            .clipShape(RoundedRectangle(cornerRadius: 1.5))
                            .overlay(
                                RoundedRectangle(cornerRadius: 1.5)
                                    .stroke(Color.white, lineWidth: 0.5)
                            )
                            .zIndex(Double(3 - index))
                    } else {
                        RoundedRectangle(cornerRadius: 1.5)
                            .fill(Color.secondary.opacity(0.3))
                            .frame(width: 10, height: 10)
                            .overlay(
                                Image(systemName: "app")
                                    .font(.system(size: 6))
                                    .foregroundColor(.secondary)
                            )
                            .zIndex(Double(3 - index))
                    }
                }

                if (profile.windows?.count ?? 0) > 3 {
                    Text("+\((profile.windows?.count ?? 0) - 3)")
                        .font(.system(size: 8, weight: .medium))
                        .foregroundColor(.secondary)
                }
            }
        }
    }
}

struct CompactEditButton: View {
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Image(systemName: "pencil")
                .font(.system(size: 12))
                .foregroundColor(.secondary)
                .frame(width: 20, height: 20)
                .background(Color.secondary.opacity(0.1))
                .clipShape(Circle())
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { isHovered in
            // 可以添加 hover 效果
        }
    }
}

struct CompactMenuButton: View {
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: 14))
                .foregroundColor(.secondary)
                .frame(width: 32, height: 28)
                .background(Color.clear)
                .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
        .hoverEffect()
    }
}