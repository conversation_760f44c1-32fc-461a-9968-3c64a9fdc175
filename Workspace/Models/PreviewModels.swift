import Foundation
import SwiftUI

// MARK: - PreviewMode 枚舉
enum PreviewMode: String, CaseIterable, Identifiable {
    case scaled = "scaled"
    case grid = "grid"
    case list = "list"
    case miniMap = "miniMap"
    
    var id: String { rawValue }
    
    var displayName: String {
        switch self {
        case .scaled: return "縮放預覽"
        case .grid: return "網格預覽"
        case .list: return "列表預覽"
        case .miniMap: return "小地圖"
        }
    }
    
    var icon: String {
        switch self {
        case .scaled: return "rectangle.3.group"
        case .grid: return "grid"
        case .list: return "list.bullet"
        case .miniMap: return "map"
        }
    }
    
    var description: String {
        switch self {
        case .scaled: return "智能縮放顯示，避免視窗重疊"
        case .grid: return "網格排列，統一視窗大小"
        case .list: return "緊湊列表，節省空間"
        case .miniMap: return "多螢幕概覽，顯示整體佈局"
        }
    }
}

// MARK: - PreviewConfiguration 結構體
struct PreviewConfiguration: Codable, Equatable {
    var mode: PreviewMode
    var showLabels: Bool
    var showOverlapIndicators: Bool
    var groupByApplication: Bool
    var maxWindowsPerView: Int
    var enableAnimations: Bool
    var showWindowDetails: Bool
    
    static let `default` = PreviewConfiguration(
        mode: .scaled,
        showLabels: true,
        showOverlapIndicators: true,
        groupByApplication: false,
        maxWindowsPerView: 50,
        enableAnimations: true,
        showWindowDetails: true
    )
    
    // 根據視窗數量自動調整配置
    static func optimized(for windowCount: Int) -> PreviewConfiguration {
        var config = PreviewConfiguration.default
        
        if windowCount > 30 {
            config.mode = .grid
            config.showLabels = false
            config.enableAnimations = false
        } else if windowCount > 15 {
            config.mode = .scaled
            config.showOverlapIndicators = true
        }
        
        return config
    }
}

// MARK: - WindowDisplayInfo 結構體
struct WindowDisplayInfo: Identifiable, Equatable {
    let id = UUID()
    let window: WindowLayout
    let displayFrame: CGRect
    let isOverlapping: Bool
    let overlapLevel: Int
    let isVisible: Bool
    let opacity: Double
    let zIndex: Int
    
    init(window: WindowLayout, displayFrame: CGRect, isOverlapping: Bool = false, overlapLevel: Int = 0, isVisible: Bool = true, opacity: Double = 1.0, zIndex: Int = 0) {
        self.window = window
        self.displayFrame = displayFrame
        self.isOverlapping = isOverlapping
        self.overlapLevel = overlapLevel
        self.isVisible = isVisible
        self.opacity = opacity
        self.zIndex = zIndex
    }
    
    // 計算視窗的實際框架（從 WindowLayout.WindowFrame 轉換為 CGRect）
    var actualFrame: CGRect {
        return CGRect(
            x: window.frame.x,
            y: window.frame.y,
            width: window.frame.w,
            height: window.frame.h
        )
    }
    
    // 檢查視窗是否在指定區域內可見
    func isVisible(in bounds: CGRect) -> Bool {
        return bounds.intersects(displayFrame) && displayFrame.width > 0 && displayFrame.height > 0
    }
    
    // 計算與另一個視窗的重疊面積
    func overlapArea(with other: WindowDisplayInfo) -> CGFloat {
        let intersection = displayFrame.intersection(other.displayFrame)
        return intersection.width * intersection.height
    }
}

// MARK: - ScreenBounds 結構體
struct ScreenBounds: Identifiable, Equatable, Codable {
    let id: UUID
    let bounds: CGRect
    let isPrimary: Bool
    let displayName: String
    let scaleFactor: CGFloat
    
    enum CodingKeys: String, CodingKey {
        case bounds, isPrimary, displayName, scaleFactor
    }
    
    init(bounds: CGRect, isPrimary: Bool = false, displayName: String = "Display", scaleFactor: CGFloat = 1.0) {
        self.id = UUID()
        self.bounds = bounds
        self.isPrimary = isPrimary
        self.displayName = displayName
        self.scaleFactor = scaleFactor
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = UUID()
        self.bounds = try container.decode(CGRect.self, forKey: .bounds)
        self.isPrimary = try container.decode(Bool.self, forKey: .isPrimary)
        self.displayName = try container.decode(String.self, forKey: .displayName)
        self.scaleFactor = try container.decode(CGFloat.self, forKey: .scaleFactor)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(bounds, forKey: .bounds)
        try container.encode(isPrimary, forKey: .isPrimary)
        try container.encode(displayName, forKey: .displayName)
        try container.encode(scaleFactor, forKey: .scaleFactor)
    }
    
    // 檢測多螢幕配置的靜態方法
    static func detectScreenBounds(from windows: [WindowLayout]) -> [ScreenBounds] {
        guard !windows.isEmpty else {
            // 如果沒有視窗，返回默認螢幕配置
            return [ScreenBounds(
                bounds: CGRect(x: 0, y: 0, width: 1920, height: 1080),
                isPrimary: true,
                displayName: "主螢幕"
            )]
        }
        
        // 計算所有視窗的邊界
        let allFrames = windows.map { window in
            CGRect(x: window.frame.x, y: window.frame.y, width: window.frame.w, height: window.frame.h)
        }
        
        // 找到整體邊界
        let minX = allFrames.map { $0.minX }.min() ?? 0
        let minY = allFrames.map { $0.minY }.min() ?? 0
        let maxX = allFrames.map { $0.maxX }.max() ?? 1920
        let maxY = allFrames.map { $0.maxY }.max() ?? 1080
        
        let totalBounds = CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)
        
        // 簡化版本：先返回單一螢幕邊界
        // 未來可以擴展為真正的多螢幕檢測算法
        return [ScreenBounds(
            bounds: totalBounds,
            isPrimary: true,
            displayName: "檢測到的螢幕區域",
            scaleFactor: 1.0
        )]
    }
    
    // 高級多螢幕檢測算法（基於視窗分佈模式）
    static func detectMultipleScreens(from windows: [WindowLayout]) -> [ScreenBounds] {
        // 使用新的 ScreenDetector 進行更智能的檢測
        let detectionResult = ScreenDetector.detectScreenConfiguration(from: windows)
        
        // 如果檢測結果可信度較低，回退到簡單檢測
        if detectionResult.confidence < 0.5 {
            return detectScreenBounds(from: windows)
        }
        
        return detectionResult.screens
    }
    
    // 檢查視窗是否在此螢幕範圍內
    func contains(window: WindowLayout) -> Bool {
        let windowFrame = CGRect(x: window.frame.x, y: window.frame.y, width: window.frame.w, height: window.frame.h)
        return bounds.intersects(windowFrame)
    }
    
    // 計算視窗在此螢幕中的相對位置
    func relativePosition(for window: WindowLayout) -> CGPoint {
        let windowCenter = CGPoint(x: window.frame.x + window.frame.w/2, y: window.frame.y + window.frame.h/2)
        let screenCenter = CGPoint(x: bounds.midX, y: bounds.midY)
        
        return CGPoint(
            x: (windowCenter.x - screenCenter.x) / bounds.width,
            y: (windowCenter.y - screenCenter.y) / bounds.height
        )
    }
}

// MARK: - 輔助擴展
extension CGRect {
    // 安全的 CGRect 初始化，處理無效值
    static func safe(x: Double, y: Double, width: Double, height: Double) -> CGRect {
        let safeX = x.isFinite ? CGFloat(x) : 0
        let safeY = y.isFinite ? CGFloat(y) : 0
        let safeWidth = width.isFinite && width > 0 ? CGFloat(width) : 100
        let safeHeight = height.isFinite && height > 0 ? CGFloat(height) : 100
        
        return CGRect(x: safeX, y: safeY, width: safeWidth, height: safeHeight)
    }
    
    // 計算兩個矩形的重疊百分比
    func overlapPercentage(with other: CGRect) -> CGFloat {
        let intersection = self.intersection(other)
        let intersectionArea = intersection.width * intersection.height
        let unionArea = self.area + other.area - intersectionArea
        
        return unionArea > 0 ? intersectionArea / unionArea : 0
    }
    
    // 矩形面積
    var area: CGFloat {
        return width * height
    }
}

// MARK: - PreviewMode 的 Codable 支持
extension PreviewMode: Codable {
    init(from decoder: Decoder) throws {
        let container = try decoder.singleValueContainer()
        let rawValue = try container.decode(String.self)
        self = PreviewMode(rawValue: rawValue) ?? .scaled
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.singleValueContainer()
        try container.encode(rawValue)
    }
}