import Foundation

// Space-specific 檔案的格式
struct SpaceLayoutData: Codable {
    let windows: [WindowLayout]
    let spaceID: Int?
    let screenUUID: String?
    let runningApps: [RunningApp]?
    let version: String?
    let type: String?
    let timestamp: TimeInterval?

    struct RunningApp: Codable {
        let name: String
        let bundleID: String?
        let path: String?
    }
}

struct Profile: Identifiable, Codable, Equatable {
    var id: String { name } // 使用名稱作為穩定的 ID
    var name: String
    var windows: [WindowLayout]?
    var createdAt: Date
    var modifiedAt: Date
    var isSpaceSpecific: Bool // 標記是否為 Space-specific profile
    var spaceID: Int? // 新增 Space 識別碼

    init(name: String, windows: [WindowLayout]? = nil, isSpaceSpecific: Bool = false, spaceID: Int? = nil) {
        self.name = name
        self.windows = windows
        self.createdAt = Date()
        self.modifiedAt = Date()
        self.isSpaceSpecific = isSpaceSpecific
        self.spaceID = spaceID
    }

    // 編碼所有屬性，不需要排除 id 因為它是計算屬性
    enum CodingKeys: String, CodingKey {
        case name, windows, createdAt, modifiedAt, isSpaceSpecific, spaceID
    }
}

struct WindowLayout: Identifiable, Codable, Equatable {
    var id: String { "\(app)-\(bundleID)-\(title)-\(frame.x)-\(frame.y)" } // 使用內容作為穩定的 ID
    let app: String
    let bundleID: String
    let title: String
    let frame: WindowFrame

    // Space-specific 檔案的額外欄位（可選）
    let specialData: SpecialData?
    let spaceID: Int?
    let windowID: Int?
    let isMinimized: Bool?

    // 便利初始化器，用於向後兼容
    init(app: String, bundleID: String, title: String, frame: WindowFrame) {
        self.app = app
        self.bundleID = bundleID
        self.title = title
        self.frame = frame
        self.specialData = nil
        self.spaceID = nil
        self.windowID = nil
        self.isMinimized = nil
    }

    // 編碼所有屬性，不需要排除 id 因為它是計算屬性
    enum CodingKeys: String, CodingKey {
        case app, bundleID, title, frame, specialData, spaceID, windowID, isMinimized
    }

    struct SpecialData: Codable, Equatable {
        let type: String?
        let url: Bool?
    }
    
    struct WindowFrame: Codable, Equatable {
        let x: Double
        let y: Double
        let w: Double
        let h: Double
    }
}

extension Profile {
    var jsonFileName: String {
        if let spaceID = spaceID {
            return "\(name)_space\(spaceID).json"
        }
        return isSpaceSpecific ? "\(name)_space.json" : "\(name).json"
    }

    var previewDescription: String {
        let typeDescription: String
        if let spaceID = spaceID {
            typeDescription = "Space \(spaceID)"
        } else if isSpaceSpecific {
            typeDescription = "Space 專用"
        } else {
            typeDescription = "全域"
        }
        return "\(windows?.count ?? 0) 個視窗 • \(typeDescription)"
    }

    var displayName: String {
        // 移除 SpaceProfile 前綴以獲得更清晰的顯示名稱
        if name.hasPrefix("SpaceProfile") {
            let suffix = String(name.dropFirst("SpaceProfile".count))
            return "Space \(suffix)"
        }
        return name
    }
    
    // 檢查設定檔是否屬於特定 Space
    func belongsToSpace(_ targetSpaceID: Int) -> Bool {
        return spaceID == targetSpaceID
    }
    
    // 檢查設定檔是否為 Space 感知的
    var isSpaceAware: Bool {
        return spaceID != nil || isSpaceSpecific
    }
}