#!/bin/bash

# 更新 Hammerspoon 配置腳本

echo "正在更新 Hammerspoon 配置..."

# 複製新的配置文件
cp "Workspace/Resources/init.lua" "$HOME/.hammerspoon/init.lua"

if [ $? -eq 0 ]; then
    echo "✅ 配置文件已更新"
    
    # 檢查 Hammerspoon 是否正在運行
    if pgrep -x "Hammerspoon" > /dev/null; then
        echo "🔄 重新載入 Hammerspoon 配置..."
        # 使用 URL scheme 重新載入配置
        open "hammerspoon://runLua?code=hs.reload()"
        echo "✅ 配置已重新載入"
    else
        echo "⚠️  Hammerspoon 未運行，請手動啟動 Hammerspoon"
    fi
else
    echo "❌ 配置文件更新失敗"
    exit 1
fi

echo "完成！"