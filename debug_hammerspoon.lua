-- 調試版本的 Hammerspoon 配置

local layoutsPath = os.getenv("HOME") .. "/.hammerspoon/layouts/"

-- 確保 layouts 目錄存在
function ensureLayoutsDirectory()
    local result = os.execute("mkdir -p " .. layoutsPath)
    if result ~= 0 then
        hs.alert.show("無法創建 layouts 目錄")
        return false
    end
    return true
end

-- 簡化的儲存函數，添加更多調試信息
function saveWindowLayout(profileName)
    hs.alert.show("開始儲存: " .. profileName)
    
    if not ensureLayoutsDirectory() then
        hs.alert.show("目錄創建失敗")
        return false
    end
    
    local windows = {}
    local allWindows = hs.window.allWindows()
    
    hs.alert.show("找到 " .. #allWindows .. " 個視窗")
    
    -- 檢查是否有可見視窗
    if #allWindows == 0 then
        hs.alert.show("沒有找到可見的視窗")
        return false
    end
    
    for _, win in ipairs(allWindows) do
        if win:isVisible() and win:application() then
            local app = win:application()
            local frame = win:frame()
            
            table.insert(windows, {
                app = app:name(),
                bundleID = app:bundleID() or "",
                title = win:title() or "",
                frame = {
                    x = frame.x,
                    y = frame.y,
                    w = frame.w,
                    h = frame.h
                }
            })
        end
    end
    
    hs.alert.show("準備儲存 " .. #windows .. " 個視窗")
    
    if #windows == 0 then
        hs.alert.show("沒有找到可儲存的視窗")
        return false
    end
    
    local success, json = pcall(hs.json.encode, windows)
    if not success then
        hs.alert.show("JSON 編碼失敗: " .. tostring(json))
        return false
    end
    
    local filePath = layoutsPath .. profileName .. ".json"
    hs.alert.show("寫入檔案: " .. filePath)
    
    local file = io.open(filePath, "w")
    if file then
        file:write(json)
        file:close()
        hs.alert.show("✅ 佈局「" .. profileName .. "」已儲存 (" .. #windows .. " 個視窗)")
        return true
    else
        hs.alert.show("❌ 無法寫入檔案: " .. filePath)
        return false
    end
end

-- 測試函數
function testSave()
    saveWindowLayout("DebugTest")
end

hs.alert.show("調試配置已載入")