# Profile 編輯按鈕修復測試指南

## 🎯 修復內容

### 問題根源
- **原問題**: 每次 `ProfileManager.loadProfiles()` 都會創建新的 Profile 實例，每個都有新的 UUID
- **導致**: SwiftUI 的 `sheet(item:)` 無法正確追蹤 Profile 的身份，點擊編輯按鈕時會 crash

### 修復方案
1. **Profile ID 穩定化**: 使用 Profile 名稱作為 ID (`var id: String { name }`)
2. **WindowLayout ID 穩定化**: 使用內容組合作為 ID (`var id: String { "\(app)-\(bundleID)-\(title)-\(frame.x)-\(frame.y)" }`)
3. **類型統一**: 將所有 `Set<UUID>` 改為 `Set<String>`
4. **相關組件更新**: 更新 PreviewConfigurationManager、BatchUpdateManager、RenderCacheManager

## 🧪 測試步驟

### 1. 確認應用程式運行
```bash
# 檢查應用程式是否在運行
ps aux | grep Workspace
```

### 2. 測試編輯按鈕
1. 點擊選單列的 Workspace 圖示
2. 查看 Profile 列表
3. 點擊任意 Profile 右側的筆圖案按鈕（編輯按鈕）
4. **預期結果**: 
   - ✅ 應該打開 ProfileEditorView 視窗
   - ✅ 視窗標題顯示「編輯 Profile」
   - ✅ 不應該出現 crash 或無反應

### 3. 檢查調試日誌
如果在終端運行 `swift run`，點擊編輯按鈕時應該看到：
```
🔧 EditButton 被點擊，Profile: [Profile名稱]
```

### 4. 測試編輯功能
1. 在打開的編輯視窗中測試各項功能：
   - 修改 Profile 名稱
   - 查看視窗列表
   - 點擊視窗項目
   - 使用「完成」按鈕關閉視窗

## 🔍 技術驗證

### Profile ID 一致性測試
```swift
// 在 ProfileManager.loadProfiles() 前後，Profile ID 應該保持一致
let profilesBefore = profileManager.profiles
profileManager.loadProfiles()
let profilesAfter = profileManager.profiles

// 相同名稱的 Profile 應該有相同的 ID
for profileBefore in profilesBefore {
    if let profileAfter = profilesAfter.first(where: { $0.name == profileBefore.name }) {
        assert(profileBefore.id == profileAfter.id, "Profile ID 應該保持一致")
    }
}
```

### SwiftUI Sheet 狀態管理測試
```swift
// sheet(item:) 現在應該能正確追蹤 Profile
.sheet(item: $selectedProfile) { profile in
    ProfileEditorView(profile: profile)
}
```

## 📊 修復前後對比

| 項目 | 修復前 | 修復後 |
|------|--------|--------|
| Profile ID | `UUID()` (每次重新生成) | `name` (穩定) |
| WindowLayout ID | `UUID()` (每次重新生成) | 內容組合 (穩定) |
| Sheet 管理 | `sheet(isPresented:)` | `sheet(item:)` |
| 狀態追蹤 | 不一致 | 一致 |
| 編輯按鈕 | Crash | 正常工作 |

## ✅ 成功指標

- [ ] 編譯無錯誤
- [ ] 應用程式正常啟動
- [ ] 編輯按鈕可以點擊
- [ ] ProfileEditorView 正常打開
- [ ] 沒有 crash 或異常
- [ ] 調試日誌正常輸出
- [ ] Profile 功能完整可用

## 🚨 如果仍有問題

如果編輯按鈕仍然有問題，請檢查：

1. **編譯錯誤**: 確保所有 UUID 類型都已改為 String
2. **運行時錯誤**: 檢查終端輸出的錯誤信息
3. **狀態不一致**: 確認 Profile ID 在載入前後保持一致
4. **SwiftUI 問題**: 確認 `sheet(item:)` 的使用正確

## 📝 測試結果記錄

**測試時間**: ___________
**測試者**: ___________
**結果**: 
- [ ] ✅ 完全修復
- [ ] ⚠️ 部分修復
- [ ] ❌ 仍有問題

**備註**: ___________
