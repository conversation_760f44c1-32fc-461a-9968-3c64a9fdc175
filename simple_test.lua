-- 最簡單的測試配置

function saveWindowLayout(profileName)
    hs.alert.show("收到儲存請求: " .. profileName)
    
    -- 直接寫入一個測試檔案
    local layoutsPath = os.getenv("HOME") .. "/.hammerspoon/layouts/"
    local filePath = layoutsPath .. profileName .. ".json"
    
    local file = io.open(filePath, "w")
    if file then
        file:write('{"test": "data"}')
        file:close()
        hs.alert.show("檔案已寫入: " .. filePath)
        return true
    else
        hs.alert.show("無法寫入檔案: " .. filePath)
        return false
    end
end

-- 啟用 AppleScript 支持
hs.allowAppleScript(true)

hs.alert.show("簡單測試配置已載入，AppleScript 已啟用")