#!/usr/bin/env swift

import Foundation

// 簡單測試 saveWindowLayout 功能
func testSaveLayout() {
    print("🧪 測試儲存佈局功能...")
    
    let appleScript = """
    tell application "Hammerspoon"
        execute lua code "return saveWindowLayout('test_swift')"
    end tell
    """
    
    let task = Process()
    task.launchPath = "/usr/bin/osascript"
    task.arguments = ["-e", appleScript]
    
    let pipe = Pipe()
    let errorPipe = Pipe()
    task.standardOutput = pipe
    task.standardError = errorPipe
    
    task.launch()
    task.waitUntilExit()
    
    let data = pipe.fileHandleForReading.readDataToEndOfFile()
    let errorData = errorPipe.fileHandleForReading.readDataToEndOfFile()
    let output = String(data: data, encoding: .utf8)?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
    let errorOutput = String(data: errorData, encoding: .utf8)?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
    
    print("AppleScript 輸出: '\(output)'")
    if !errorOutput.isEmpty {
        print("AppleScript 錯誤: '\(errorOutput)'")
    }
    print("終止狀態: \(task.terminationStatus)")
    
    if output.lowercased() == "true" {
        print("✅ 儲存成功！")
        
        // 檢查檔案是否存在
        let homeURL = FileManager.default.homeDirectoryForCurrentUser
        let layoutPath = homeURL.appendingPathComponent(".hammerspoon/layouts/test_swift.json")
        
        if FileManager.default.fileExists(atPath: layoutPath.path) {
            print("✅ 檔案已創建: \(layoutPath.path)")
        } else {
            print("❌ 檔案未創建")
        }
    } else {
        print("❌ 儲存失敗，返回值: \(output)")
    }
}

testSaveLayout()