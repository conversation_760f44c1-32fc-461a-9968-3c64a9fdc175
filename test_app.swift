#!/usr/bin/env swift

import Foundation
import AppKit

// 測試 AppIconProvider 的功能
class TestAppIconProvider {
    static func getIcon(for bundleID: String) -> NSImage? {
        guard !bundleID.isEmpty else { return nil }
        
        guard let appURL = NSWorkspace.shared.urlForApplication(withBundleIdentifier: bundleID) else {
            return nil
        }
        
        return NSWorkspace.shared.icon(forFile: appURL.path)
    }
    
    static func getIconByName(for appName: String) -> NSImage? {
        guard !appName.isEmpty else { return nil }
        
        let runningApps = NSWorkspace.shared.runningApplications
        
        if let app = runningApps.first(where: { $0.localizedName == appName }) {
            return app.icon
        }
        
        return NSImage(systemSymbolName: "app", accessibilityDescription: appName)
    }
    
    static func getIconWithFallback(bundleID: String?, appName: String?) -> NSImage? {
        if let bundleID = bundleID, let icon = getIcon(for: bundleID) {
            return icon
        }
        
        if let appName = appName, let icon = getIconByName(for: appName) {
            return icon
        }
        
        return NSImage(systemSymbolName: "app", accessibilityDescription: "Unknown App")
    }
}

// 測試常見的應用程式
let testCases = [
    ("com.apple.finder", "Finder"),
    ("com.apple.Safari", "Safari"),
    ("com.microsoft.VSCode", "Visual Studio Code"),
    ("", "Unknown App"),
    ("invalid.bundle.id", "Invalid App")
]

print("測試 AppIconProvider...")

for (bundleID, appName) in testCases {
    print("\n測試: \(bundleID.isEmpty ? "空字串" : bundleID)")
    
    // 測試 getIcon
    let icon1 = TestAppIconProvider.getIcon(for: bundleID)
    print("  getIcon: \(icon1 != nil ? "✅ 成功" : "❌ 失敗")")
    
    // 測試 getIconByName
    let icon2 = TestAppIconProvider.getIconByName(for: appName)
    print("  getIconByName: \(icon2 != nil ? "✅ 成功" : "❌ 失敗")")
    
    // 測試 getIconWithFallback
    let icon3 = TestAppIconProvider.getIconWithFallback(bundleID: bundleID.isEmpty ? nil : bundleID, appName: appName)
    print("  getIconWithFallback: \(icon3 != nil ? "✅ 成功" : "❌ 失敗")")
}

print("\n✅ 所有測試完成，沒有 crash！")