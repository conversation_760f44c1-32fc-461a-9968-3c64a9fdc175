-- 驗證 Space-Specific 修正
print("=== 驗證 Space-Specific 修正 ===")

-- 1. 檢查當前 space
local currentSpaceID = hs.spaces.focusedSpace()
print("當前 Space ID: " .. tostring(currentSpaceID))

-- 2. 檢查所有視窗和它們的 space 歸屬
local allWindows = hs.window.allWindows()
local currentSpaceCount = 0
local otherSpaceCount = 0

print("\n所有視窗的 Space 歸屬:")
for _, window in ipairs(allWindows) do
    if window and window:application() then
        local app = window:application()
        local appName = app:name()
        
        if appName ~= "Hammerspoon" then
            local windowSpaces = hs.spaces.windowSpaces(window)
            local isInCurrentSpace = false
            
            for _, spaceID in ipairs(windowSpaces) do
                if spaceID == currentSpaceID then
                    isInCurrentSpace = true
                    break
                end
            end
            
            if isInCurrentSpace then
                currentSpaceCount = currentSpaceCount + 1
                print("✓ " .. appName .. " - " .. (window:title() or "無標題") .. " (在當前 Space)")
            else
                otherSpaceCount = otherSpaceCount + 1
                print("✗ " .. appName .. " - " .. (window:title() or "無標題") .. " (在其他 Space: " .. table.concat(windowSpaces, ", ") .. ")")
            end
        end
    end
end

print("\n統計:")
print("當前 Space 視窗數: " .. currentSpaceCount)
print("其他 Space 視窗數: " .. otherSpaceCount)

-- 3. 測試保存功能
print("\n=== 測試保存功能 ===")
local testResult = saveCurrentSpaceLayout("VerifyTest")
print("保存結果: " .. tostring(testResult))

-- 4. 檢查保存的數據
hs.timer.doAfter(2, function()
    local file = io.open(os.getenv("HOME") .. "/.hammerspoon/layouts/VerifyTest_space.json", "r")
    if file then
        local content = file:read("*all")
        file:close()
        local data = hs.json.decode(content)
        
        print("\n=== 保存的數據檢查 ===")
        print("保存的 Space ID: " .. tostring(data.spaceID))
        print("保存的視窗數量: " .. #data.windows)
        
        -- 檢查是否所有保存的視窗都屬於當前 space
        local correctSpaceCount = 0
        for _, windowData in ipairs(data.windows) do
            if windowData.spaceID == currentSpaceID then
                correctSpaceCount = correctSpaceCount + 1
            end
        end
        
        print("正確 Space 的視窗數: " .. correctSpaceCount .. "/" .. #data.windows)
        
        if correctSpaceCount == #data.windows and #data.windows == currentSpaceCount then
            print("✅ 修正成功！只保存了當前 Space 的視窗")
        else
            print("❌ 仍有問題，保存了其他 Space 的視窗")
        end
    else
        print("❌ 無法讀取保存的檔案")
    end
end)
