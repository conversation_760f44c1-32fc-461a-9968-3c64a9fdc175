#!/bin/bash

echo "🧪 測試 Hammerspoon 基本功能..."

# 檢查 Hammerspoon 是否安裝
if [ ! -d "/Applications/Hammerspoon.app" ]; then
    echo "❌ Hammerspoon 未安裝"
    exit 1
fi

echo "✅ Hammerspoon 已安裝"

# 檢查 Hammerspoon 是否運行
if ! pgrep -f "Hammerspoon" > /dev/null; then
    echo "❌ Hammerspoon 未運行"
    echo "請啟動 Hammerspoon 應用程式"
    exit 1
fi

echo "✅ Hammerspoon 正在運行"

# 檢查配置檔案
if [ ! -f "$HOME/.hammerspoon/init.lua" ]; then
    echo "❌ 配置檔案不存在"
    exit 1
fi

echo "✅ 配置檔案存在"

# 測試基本 AppleScript 通信
echo "🔄 測試 AppleScript 通信..."
result=$(osascript -e 'tell application "Hammerspoon" to execute lua code "return \"test\""' 2>&1)

if [ $? -eq 0 ]; then
    echo "✅ AppleScript 通信正常"
    echo "返回值: $result"
else
    echo "❌ AppleScript 通信失敗"
    echo "錯誤: $result"
    exit 1
fi

# 測試 saveWindowLayout 函數是否存在
echo "🔄 測試 saveWindowLayout 函數..."
result=$(osascript -e 'tell application "Hammerspoon" to execute lua code "return type(saveWindowLayout)"' 2>&1)

if [ "$result" = "function" ]; then
    echo "✅ saveWindowLayout 函數存在"
else
    echo "❌ saveWindowLayout 函數不存在或有問題"
    echo "返回值: $result"
    exit 1
fi

# 測試目錄創建
echo "🔄 測試目錄創建..."
result=$(osascript -e 'tell application "Hammerspoon" to execute lua code "return ensureLayoutsDirectory()"' 2>&1)

if [ "$result" = "true" ]; then
    echo "✅ 目錄創建成功"
else
    echo "❌ 目錄創建失敗"
    echo "返回值: $result"
fi

# 檢查 layouts 目錄是否存在
if [ -d "$HOME/.hammerspoon/layouts" ]; then
    echo "✅ layouts 目錄存在"
else
    echo "❌ layouts 目錄不存在"
fi

echo "🎉 基本測試完成"