# Workspace for macOS 測試計劃

## 1. 測試概述

### 1.1 測試目標
- 驗證所有 PRD 中定義的功能需求是否正確實作
- 確保應用程式的穩定性和使用者體驗
- 驗證與 Hammerspoon 的整合是否正常運作
- 測試各種邊界情況和錯誤處理

### 1.2 測試環境
- **作業系統**: macOS 13.0 或更高版本
- **依賴軟體**: Hammerspoon (最新版本)
- **測試設備**: Mac 電腦 (支援多螢幕配置)

## 2. 功能測試

### 2.1 FR-1: 選單列常駐應用測試

#### 2.1.1 基本功能測試
- [ ] **測試項目**: 應用程式啟動後在選單列顯示圖示
  - **步驟**: 啟動 Workspace 應用程式
  - **預期結果**: 選單列右側出現 Workspace 圖示
  - **實際結果**: 
  - **狀態**: 

- [ ] **測試項目**: 左鍵點擊圖示彈出主選單
  - **步驟**: 左鍵點擊選單列圖示
  - **預期結果**: 彈出主下拉式功能表
  - **實際結果**: 
  - **狀態**: 

- [ ] **測試項目**: 右鍵點擊圖示顯示上下文選單
  - **步驟**: 右鍵點擊選單列圖示
  - **預期結果**: 顯示包含「關於」、「檢查更新」、「結束應用」的選單
  - **實際結果**: 
  - **狀態**: 

#### 2.1.2 開機自動啟動測試
- [ ] **測試項目**: 設定開機自動啟動
  - **步驟**: 在設定中啟用「開機時自動啟動」
  - **預期結果**: 重新開機後應用程式自動啟動
  - **實際結果**: 
  - **狀態**: 

### 2.2 FR-2: Profile 列表與觸發測試

#### 2.2.1 Profile 列表顯示測試
- [ ] **測試項目**: 自動掃描並顯示 Profile 列表
  - **步驟**: 確保 `~/.hammerspoon/layouts/` 目錄下有 JSON 檔案
  - **預期結果**: 主選單顯示所有 Profile，按修改時間排序
  - **實際結果**: 
  - **狀態**: 

- [ ] **測試項目**: Profile 預覽縮圖顯示
  - **步驟**: 檢查每個 Profile 條目的預覽圖
  - **預期結果**: 顯示應用程式圖示和視窗數量
  - **實際結果**: 
  - **狀態**: 

#### 2.2.2 Profile 觸發測試
- [ ] **測試項目**: 點擊 Profile 觸發還原
  - **步驟**: 點擊列表中的任一 Profile
  - **預期結果**: 執行對應的 Hammerspoon 還原函式
  - **實際結果**: 
  - **狀態**: 

- [ ] **測試項目**: 儲存當前佈局功能
  - **步驟**: 點擊「儲存當前佈局」按鈕，輸入名稱
  - **預期結果**: 創建新的 Profile JSON 檔案
  - **實際結果**: 
  - **狀態**: 

### 2.3 FR-3: Profile 編輯與預覽測試

#### 2.3.1 Profile 編輯器測試
- [ ] **測試項目**: 開啟 Profile 編輯視窗
  - **步驟**: 點擊 Profile 條目旁的編輯按鈕
  - **預期結果**: 開啟 Profile 編輯視窗
  - **實際結果**: 
  - **狀態**: 

- [ ] **測試項目**: 視覺化佈局預覽
  - **步驟**: 在編輯視窗中檢查佈局預覽區域
  - **預期結果**: 顯示視窗位置和大小的視覺化表示
  - **實際結果**: 
  - **狀態**: 

#### 2.3.2 Profile 管理測試
- [ ] **測試項目**: Profile 重命名功能
  - **步驟**: 修改 Profile 名稱並點擊重命名
  - **預期結果**: JSON 檔案被重命名
  - **實際結果**: 
  - **狀態**: 

- [ ] **測試項目**: Profile 刪除功能
  - **步驟**: 點擊刪除按鈕並確認
  - **預期結果**: JSON 檔案被刪除，列表更新
  - **實際結果**: 
  - **狀態**: 

### 2.4 FR-4: Hammerspoon 整合測試

#### 2.4.1 安裝檢查測試
- [ ] **測試項目**: Hammerspoon 安裝檢查
  - **步驟**: 在未安裝 Hammerspoon 的環境下啟動應用
  - **預期結果**: 顯示安裝提示並引導至官網
  - **實際結果**: 
  - **狀態**: 

- [ ] **測試項目**: 配置檔案安裝
  - **步驟**: 點擊「安裝配置檔案」按鈕
  - **預期結果**: 在 `~/.hammerspoon/` 創建 init.lua
  - **實際結果**: 
  - **狀態**: 

#### 2.4.2 快捷鍵說明測試
- [ ] **測試項目**: 快捷鍵說明視窗
  - **步驟**: 點擊「快捷鍵說明」選項
  - **預期結果**: 顯示所有 Profile 的快捷鍵列表
  - **實際結果**: 
  - **狀態**: 

### 2.5 FR-5: 使用者自訂設定測試

#### 2.5.1 設定介面測試
- [ ] **測試項目**: 設定視窗開啟
  - **步驟**: 點擊「設定」選項
  - **預期結果**: 開啟設定視窗
  - **實際結果**: 
  - **狀態**: 

- [ ] **測試項目**: 進度提示樣式設定
  - **步驟**: 調整字體大小和顯示時間滑桿
  - **預期結果**: 設定即時同步到 Hammerspoon
  - **實際結果**: 
  - **狀態**: 

## 3. 整合測試

### 3.1 Hammerspoon 通信測試
- [ ] **測試項目**: URL Scheme 通信
  - **步驟**: 執行儲存/還原操作
  - **預期結果**: Hammerspoon 正確接收並執行 Lua 代碼
  - **實際結果**: 
  - **狀態**: 

### 3.2 檔案系統操作測試
- [ ] **測試項目**: JSON 檔案讀寫
  - **步驟**: 創建、修改、刪除 Profile
  - **預期結果**: 檔案系統操作正確執行
  - **實際結果**: 
  - **狀態**: 

## 4. 使用者體驗測試

### 4.1 介面響應性測試
- [ ] **測試項目**: 選單開啟速度
  - **步驟**: 多次點擊選單列圖示
  - **預期結果**: 選單快速響應，無明顯延遲
  - **實際結果**: 
  - **狀態**: 

### 4.2 視覺設計測試
- [ ] **測試項目**: macOS 原生設計語言一致性
  - **步驟**: 檢查所有 UI 元素
  - **預期結果**: 符合 macOS 設計規範
  - **實際結果**: 
  - **狀態**: 

## 5. 錯誤處理測試

### 5.1 異常情況測試
- [ ] **測試項目**: Hammerspoon 未運行時的處理
  - **步驟**: 關閉 Hammerspoon 後執行操作
  - **預期結果**: 顯示適當的錯誤訊息
  - **實際結果**: 
  - **狀態**: 

- [ ] **測試項目**: 損壞的 JSON 檔案處理
  - **步驟**: 創建格式錯誤的 JSON 檔案
  - **預期結果**: 應用程式不崩潰，跳過損壞檔案
  - **實際結果**: 
  - **狀態**: 

## 6. 性能測試

### 6.1 資源使用測試
- [ ] **測試項目**: 記憶體使用量
  - **步驟**: 長時間運行應用程式
  - **預期結果**: 記憶體使用量穩定，無記憶體洩漏
  - **實際結果**: 
  - **狀態**: 

### 6.2 啟動時間測試
- [ ] **測試項目**: 應用程式啟動時間
  - **步驟**: 測量從啟動到選單列圖示出現的時間
  - **預期結果**: 啟動時間 < 3 秒
  - **實際結果**: 
  - **狀態**: 

## 7. 測試結果總結

### 7.1 測試統計
- **總測試項目**: 25 項
- **通過項目**: ___ 項
- **失敗項目**: ___ 項
- **跳過項目**: ___ 項

### 7.2 已知問題
- [ ] 問題 1: 
- [ ] 問題 2: 
- [ ] 問題 3: 

### 7.3 建議改進
- [ ] 改進 1: 
- [ ] 改進 2: 
- [ ] 改進 3: 

## 8. 測試簽核

- **測試執行者**: ___________
- **測試日期**: ___________
- **測試版本**: v1.0
- **測試結果**: □ 通過 □ 部分通過 □ 失敗
