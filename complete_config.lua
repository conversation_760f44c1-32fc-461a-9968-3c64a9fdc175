-- Workspace for macOS - Hammerspoon Configuration
-- 視窗佈局管理工具

-- 啟用 AppleScript 支持
hs.allowAppleScript(true)

-- 全域變數
local layoutsPath = os.getenv("HOME") .. "/.hammerspoon/layouts/"

-- 確保 layouts 目錄存在
function ensureLayoutsDirectory()
    local result = os.execute("mkdir -p " .. layoutsPath)
    if result ~= 0 then
        hs.alert.show("無法創建 layouts 目錄")
        return false
    end
    return true
end

-- 儲存當前視窗佈局
function saveWindowLayout(profileName)
    hs.alert.show("開始儲存: " .. profileName)
    
    if not ensureLayoutsDirectory() then
        hs.alert.show("目錄創建失敗")
        return false
    end
    
    local windows = {}
    local allWindows = hs.window.allWindows()
    
    hs.alert.show("找到 " .. #allWindows .. " 個視窗")
    
    -- 檢查是否有可見視窗
    if #allWindows == 0 then
        hs.alert.show("沒有找到可見的視窗")
        return false
    end
    
    for _, win in ipairs(allWindows) do
        if win:isVisible() and win:application() then
            local app = win:application()
            local frame = win:frame()
            
            table.insert(windows, {
                app = app:name(),
                bundleID = app:bundleID() or "",
                title = win:title() or "",
                frame = {
                    x = frame.x,
                    y = frame.y,
                    w = frame.w,
                    h = frame.h
                }
            })
        end
    end
    
    hs.alert.show("準備儲存 " .. #windows .. " 個視窗")
    
    if #windows == 0 then
        hs.alert.show("沒有找到可儲存的視窗")
        return false
    end
    
    local success, json = pcall(hs.json.encode, windows)
    if not success then
        hs.alert.show("JSON 編碼失敗: " .. tostring(json))
        return false
    end
    
    local filePath = layoutsPath .. profileName .. ".json"
    hs.alert.show("寫入檔案: " .. filePath)
    
    local file = io.open(filePath, "w")
    if file then
        file:write(json)
        file:close()
        hs.alert.show("✅ 佈局「" .. profileName .. "」已儲存 (" .. #windows .. " 個視窗)")
        return true
    else
        hs.alert.show("❌ 無法寫入檔案: " .. filePath)
        return false
    end
end

-- 還原視窗佈局
function restoreWindowLayout(profileName)
    local file = io.open(layoutsPath .. profileName .. ".json", "r")
    if not file then
        hs.alert.show("找不到佈局「" .. profileName .. "」")
        return false
    end
    
    local content = file:read("*all")
    file:close()
    
    local success, windows = pcall(hs.json.decode, content)
    if not success or not windows then
        hs.alert.show("佈局檔案格式錯誤")
        return false
    end
    
    local restoredCount = 0
    local totalCount = #windows
    
    -- 還原每個視窗
    for _, windowData in ipairs(windows) do
        local app = hs.application.find(windowData.bundleID)
        if not app then
            -- 嘗試用應用程式名稱查找
            app = hs.application.find(windowData.app)
        end
        
        if app then
            local appWindows = app:allWindows()
            if #appWindows > 0 then
                local targetWindow = appWindows[1]
                -- 如果有多個視窗，嘗試找到標題匹配的
                for _, win in ipairs(appWindows) do
                    if win:title() == windowData.title then
                        targetWindow = win
                        break
                    end
                end
                
                if targetWindow then
                    local frame = hs.geometry.rect(
                        windowData.frame.x,
                        windowData.frame.y,
                        windowData.frame.w,
                        windowData.frame.h
                    )
                    targetWindow:setFrame(frame)
                    restoredCount = restoredCount + 1
                end
            end
        end
    end
    
    hs.alert.show("佈局「" .. profileName .. "」已還原 (" .. restoredCount .. "/" .. totalCount .. " 個視窗)")
    return restoredCount > 0
end

-- 快捷鍵設定
local shortcuts = {
    { key = "1", save = true },
    { key = "2", save = true },
    { key = "3", save = true },
    { key = "4", save = true },
    { key = "5", save = true }
}

-- 註冊快捷鍵
for _, shortcut in ipairs(shortcuts) do
    -- 儲存佈局快捷鍵 (Cmd+Alt+數字)
    hs.hotkey.bind({"cmd", "alt"}, shortcut.key, function()
        saveWindowLayout("Profile" .. shortcut.key)
    end)
    
    -- 還原佈局快捷鍵 (Cmd+Alt+Shift+數字)
    hs.hotkey.bind({"cmd", "alt", "shift"}, shortcut.key, function()
        restoreWindowLayout("Profile" .. shortcut.key)
    end)
end

-- 初始化
ensureLayoutsDirectory()
hs.alert.show("Workspace 配置已載入，AppleScript 已啟用")