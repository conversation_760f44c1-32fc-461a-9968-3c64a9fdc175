-- Workspace for macOS - Hammerspoon Configuration
-- 視窗佈局管理工具

-- 啟用 AppleScript 支持
hs.allowAppleScript(true)

-- 啟用 IPC 支持
hs.ipc.cliInstall()

-- 全域變數
local layoutsPath = os.getenv("HOME") .. "/.hammerspoon/layouts/"

-- 確保 layouts 目錄存在
function ensureLayoutsDirectory()
    -- 先檢查目錄是否已存在
    local checkCmd = "test -d " .. layoutsPath
    local checkResult = os.execute(checkCmd)
    
    print("檢查目錄命令: " .. checkCmd)
    print("檢查結果: " .. tostring(checkResult))
    
    if checkResult == 0 or checkResult == true then
        print("✅ 目錄已存在")
        return true
    end
    
    -- 目錄不存在，嘗試創建
    local createCmd = "mkdir -p " .. layoutsPath
    local createResult = os.execute(createCmd)
    
    print("創建目錄命令: " .. createCmd)
    print("創建結果: " .. tostring(createResult))
    
    -- 在不同的 Lua 版本中，os.execute 的返回值可能不同
    -- 有些返回 true/false，有些返回 0/非0
    if createResult == 0 or createResult == true then
        print("✅ 目錄創建成功")
        return true
    else
        print("❌ 目錄創建失敗: " .. tostring(createResult))
        hs.alert.show("無法創建 layouts 目錄")
        return false
    end
end

-- 儲存當前視窗佈局
function saveWindowLayout(profileName)
    -- 添加詳細的調試信息
    print("=== saveWindowLayout 開始 ===")
    print("Profile 名稱: " .. tostring(profileName))
    
    hs.alert.show("開始儲存: " .. profileName)
    
    if not ensureLayoutsDirectory() then
        print("❌ 目錄創建失敗")
        hs.alert.show("目錄創建失敗")
        return false
    end
    print("✅ 目錄檢查通過")
    
    local windows = {}
    local allWindows = hs.window.allWindows()
    
    print("找到視窗總數: " .. #allWindows)
    hs.alert.show("找到 " .. #allWindows .. " 個視窗")
    
    -- 檢查是否有可見視窗
    if #allWindows == 0 then
        print("❌ 沒有找到任何視窗")
        hs.alert.show("沒有找到可見的視窗")
        return false
    end
    
    local visibleCount = 0
    for _, win in ipairs(allWindows) do
        if win:isVisible() and win:application() then
            visibleCount = visibleCount + 1
            local app = win:application()
            if app then
                local frame = win:frame()
                local appName = app:name() or "Unknown"
                local bundleID = app:bundleID() or ""
                local title = win:title() or ""
                
                print("處理視窗: " .. appName .. " - " .. title)
                
                table.insert(windows, {
                    app = appName,
                    bundleID = bundleID,
                    title = title,
                    frame = {
                        x = frame.x,
                        y = frame.y,
                        w = frame.w,
                        h = frame.h
                    }
                })
            end
        end
    end
    
    print("可見視窗數量: " .. visibleCount)
    print("準備儲存視窗數量: " .. #windows)
    hs.alert.show("準備儲存 " .. #windows .. " 個視窗")
    
    if #windows == 0 then
        print("❌ 沒有找到可儲存的視窗")
        hs.alert.show("沒有找到可儲存的視窗")
        return false
    end
    
    local success, json = pcall(hs.json.encode, windows)
    if not success then
        print("❌ JSON 編碼失敗: " .. tostring(json))
        hs.alert.show("JSON 編碼失敗: " .. tostring(json))
        return false
    end
    print("✅ JSON 編碼成功")
    
    local filePath = layoutsPath .. profileName .. ".json"
    print("檔案路徑: " .. filePath)
    hs.alert.show("寫入檔案: " .. filePath)
    
    local file = io.open(filePath, "w")
    if file then
        file:write(json)
        file:close()
        print("✅ 檔案寫入成功")
        hs.alert.show("✅ 佈局「" .. profileName .. "」已儲存 (" .. #windows .. " 個視窗)")
        print("=== saveWindowLayout 成功結束 ===")
        return true
    else
        print("❌ 無法寫入檔案: " .. filePath)
        hs.alert.show("❌ 無法寫入檔案: " .. filePath)
        print("=== saveWindowLayout 失敗結束 ===")
        return false
    end
end

-- 還原視窗佈局
function restoreWindowLayout(profileName)
    local file = io.open(layoutsPath .. profileName .. ".json", "r")
    if not file then
        hs.alert.show("找不到佈局「" .. profileName .. "」")
        return false
    end
    
    local content = file:read("*all")
    file:close()
    
    local success, windows = pcall(hs.json.decode, content)
    if not success or not windows then
        hs.alert.show("佈局檔案格式錯誤")
        return false
    end
    
    local restoredCount = 0
    local totalCount = #windows
    
    -- 還原每個視窗
    for _, windowData in ipairs(windows) do
        local app = hs.application.find(windowData.bundleID)
        if not app then
            -- 嘗試用應用程式名稱查找
            app = hs.application.find(windowData.app)
        end
        
        if app then
            local appWindows = app:allWindows()
            if #appWindows > 0 then
                local targetWindow = appWindows[1]
                -- 如果有多個視窗，嘗試找到標題匹配的
                for _, win in ipairs(appWindows) do
                    if win:title() == windowData.title then
                        targetWindow = win
                        break
                    end
                end
                
                if targetWindow then
                    local frame = hs.geometry.rect(
                        windowData.frame.x,
                        windowData.frame.y,
                        windowData.frame.w,
                        windowData.frame.h
                    )
                    targetWindow:setFrame(frame)
                    restoredCount = restoredCount + 1
                end
            end
        end
    end
    
    hs.alert.show("佈局「" .. profileName .. "」已還原 (" .. restoredCount .. "/" .. totalCount .. " 個視窗)")
    return restoredCount > 0
end

-- 快捷鍵設定
local shortcuts = {
    { key = "1", save = true },
    { key = "2", save = true },
    { key = "3", save = true },
    { key = "4", save = true },
    { key = "5", save = true }
}

-- 註冊快捷鍵
for _, shortcut in ipairs(shortcuts) do
    -- 儲存佈局快捷鍵 (Cmd+Alt+數字)
    hs.hotkey.bind({"cmd", "alt"}, shortcut.key, function()
        saveWindowLayout("Profile" .. shortcut.key)
    end)
    
    -- 還原佈局快捷鍵 (Cmd+Alt+Shift+數字)
    hs.hotkey.bind({"cmd", "alt", "shift"}, shortcut.key, function()
        restoreWindowLayout("Profile" .. shortcut.key)
    end)
end

-- ========================================
-- 視窗高亮功能
-- ========================================

-- 全域變數用於追蹤高亮效果
local highlightTimer = nil
local highlightCanvas = nil

-- 高亮指定的視窗
function highlightWindow(appName, windowTitle, bundleID)
    print("=== highlightWindow 開始 ===")
    print("應用程式: " .. tostring(appName))
    print("視窗標題: " .. tostring(windowTitle))
    print("Bundle ID: " .. tostring(bundleID))

    -- 清除之前的高亮效果
    clearHighlight()

    -- 尋找目標視窗
    local targetWindow = nil

    -- 首先嘗試用 Bundle ID 查找
    if bundleID and bundleID ~= "" then
        local app = hs.application.find(bundleID)
        if app then
            local windows = app:allWindows()
            for _, win in ipairs(windows) do
                if win:title() == windowTitle or windowTitle == "" then
                    targetWindow = win
                    break
                end
            end
        end
    end

    -- 如果 Bundle ID 查找失敗，嘗試用應用程式名稱查找
    if not targetWindow and appName and appName ~= "" then
        local app = hs.application.find(appName)
        if app then
            local windows = app:allWindows()
            for _, win in ipairs(windows) do
                if win:title() == windowTitle or windowTitle == "" then
                    targetWindow = win
                    break
                end
            end
        end
    end

    if not targetWindow then
        print("❌ 找不到目標視窗")
        hs.alert.show("找不到視窗: " .. appName)
        return false
    end

    print("✅ 找到目標視窗: " .. targetWindow:title())

    -- 將視窗帶到前面
    targetWindow:focus()
    targetWindow:application():activate()

    -- 創建高亮效果
    createHighlightEffect(targetWindow)

    hs.alert.show("已高亮視窗: " .. appName)
    return true
end

-- 創建高亮效果
function createHighlightEffect(window)
    local frame = window:frame()
    local screen = window:screen()

    -- 創建 canvas 用於繪製高亮邊框
    highlightCanvas = hs.canvas.new(screen:frame())
    highlightCanvas:level(hs.canvas.windowLevels.overlay)
    highlightCanvas:alpha(0.8)

    -- 計算邊框位置（稍微放大一點）
    local borderWidth = 8
    local highlightFrame = {
        x = frame.x - borderWidth,
        y = frame.y - borderWidth,
        w = frame.w + borderWidth * 2,
        h = frame.h + borderWidth * 2
    }

    -- 添加外邊框（紅色）
    highlightCanvas:appendElements({
        type = "rectangle",
        frame = highlightFrame,
        strokeColor = { red = 1, green = 0, blue = 0, alpha = 0.8 },
        strokeWidth = borderWidth,
        fillColor = { red = 0, green = 0, blue = 0, alpha = 0 }
    })

    -- 添加內邊框（白色）
    local innerBorderWidth = 3
    local innerFrame = {
        x = frame.x - innerBorderWidth,
        y = frame.y - innerBorderWidth,
        w = frame.w + innerBorderWidth * 2,
        h = frame.h + innerBorderWidth * 2
    }

    highlightCanvas:appendElements({
        type = "rectangle",
        frame = innerFrame,
        strokeColor = { red = 1, green = 1, blue = 1, alpha = 0.9 },
        strokeWidth = innerBorderWidth,
        fillColor = { red = 0, green = 0, blue = 0, alpha = 0 }
    })

    -- 顯示 canvas
    highlightCanvas:show()

    -- 創建脈衝動畫效果
    local pulseCount = 0
    highlightTimer = hs.timer.doEvery(0.5, function()
        pulseCount = pulseCount + 1

        if pulseCount % 2 == 0 then
            highlightCanvas:alpha(0.4)
        else
            highlightCanvas:alpha(0.8)
        end

        -- 3秒後停止高亮
        if pulseCount >= 6 then
            clearHighlight()
        end
    end)
end

-- 清除高亮效果
function clearHighlight()
    if highlightTimer then
        highlightTimer:stop()
        highlightTimer = nil
    end

    if highlightCanvas then
        highlightCanvas:hide()
        highlightCanvas:delete()
        highlightCanvas = nil
    end
end

-- ========================================
-- Space-Specific 視窗管理功能
-- ========================================

-- 獲取特殊應用的額外數據
function getSpecialAppData(app, window)
    local appName = app:name()
    local specialData = {}

    -- Notion 特殊處理
    if appName == "Notion" then
        local success, url = pcall(function()
            local script = [[
                tell application "Notion"
                    return URL of front document
                end tell
            ]]
            return hs.osascript.applescript(script)
        end)
        if success and url then
            specialData.url = url
            specialData.type = "notion"
        end
    end

    -- Chrome/Safari 等瀏覽器處理
    if appName == "Google Chrome" or appName == "Safari" then
        local success, url = pcall(function()
            local script = string.format([[
                tell application "%s"
                    return URL of active tab of front window
                end tell
            ]], appName)
            return hs.osascript.applescript(script)
        end)
        if success and url then
            specialData.url = url
            specialData.type = "browser"
        end
    end

    return next(specialData) and specialData or nil
end

-- 保存當前 Space 的視窗佈局
function saveCurrentSpaceLayout(profileName)
    print("=== saveCurrentSpaceLayout 開始 ===")
    print("Profile 名稱: " .. tostring(profileName))

    hs.alert.show("開始儲存當前 Space: " .. profileName)

    if not ensureLayoutsDirectory() then
        print("❌ 目錄創建失敗")
        hs.alert.show("目錄創建失敗")
        return false
    end

    -- 獲取當前 space 信息
    local currentSpaceID = hs.spaces.focusedSpace()
    local currentScreen = hs.screen.mainScreen()
    local screenUUID = currentScreen:getUUID()

    print("當前 Space ID: " .. tostring(currentSpaceID))
    print("當前螢幕 UUID: " .. tostring(screenUUID))

    -- 獲取所有視窗，然後篩選出屬於當前 space 的視窗
    local allWindows = hs.window.allWindows()
    print("找到視窗總數: " .. #allWindows)

    local windowsData = {}
    local processedCount = 0

    -- 處理每個視窗，檢查是否屬於當前 space
    for _, window in ipairs(allWindows) do
        if window and window:application() then
            local app = window:application()
            local appName = app:name()

            -- 跳過 Hammerspoon 自己的視窗
            if appName ~= "Hammerspoon" then
                -- 檢查視窗是否在當前 space
                local windowSpaces = hs.spaces.windowSpaces(window)
                local isInCurrentSpace = false

                for _, spaceID in ipairs(windowSpaces) do
                    if spaceID == currentSpaceID then
                        isInCurrentSpace = true
                        break
                    end
                end

                if isInCurrentSpace then
                    local frame = window:frame()
                    local isMinimized = window:isMinimized()
                    local windowID = window:id()

                    print("處理視窗: " .. appName .. " - " .. (window:title() or "無標題") .. " (最小化: " .. tostring(isMinimized) .. ")")

                    local windowData = {
                        app = appName,
                        bundleID = app:bundleID() or "",
                        title = window:title() or "",
                        windowID = windowID,
                        frame = {
                            x = frame.x,
                            y = frame.y,
                            w = frame.w,
                            h = frame.h
                        },
                        isMinimized = isMinimized,
                        spaceID = currentSpaceID
                    }

                    -- 獲取特殊應用數據
                    local specialData = getSpecialAppData(app, window)
                    if specialData then
                        windowData.specialData = specialData
                        print("  特殊數據: " .. hs.inspect(specialData))
                    end

                    table.insert(windowsData, windowData)
                    processedCount = processedCount + 1
                end
            end
        end
    end

    -- 記錄當前運行的應用程式
    local runningApps = {}
    local allApps = hs.application.runningApplications()
    for _, app in ipairs(allApps) do
        if not app:isHidden() and app:bundleID() then
            table.insert(runningApps, {
                name = app:name(),
                bundleID = app:bundleID(),
                path = app:path()
            })
        end
    end

    -- 建立完整的 space 佈局數據
    local spaceLayout = {
        version = "2.0",
        type = "space-specific",
        spaceID = currentSpaceID,
        screenUUID = screenUUID,
        timestamp = os.time(),
        windows = windowsData,
        runningApps = runningApps
    }

    print("準備儲存 " .. #windowsData .. " 個視窗和 " .. #runningApps .. " 個應用程式")

    -- 保存到檔案
    local success, json = pcall(hs.json.encode, spaceLayout)
    if not success then
        print("❌ JSON 編碼失敗: " .. tostring(json))
        hs.alert.show("JSON 編碼失敗")
        return false
    end

    local filePath = layoutsPath .. profileName .. "_space.json"
    local file = io.open(filePath, "w")
    if file then
        file:write(json)
        file:close()
        print("✅ Space 佈局已儲存到: " .. filePath)
        hs.alert.show("✅ Space 佈局「" .. profileName .. "」已儲存 (" .. processedCount .. " 個視窗)")
        return true
    else
        print("❌ 無法寫入檔案: " .. filePath)
        hs.alert.show("❌ 無法寫入檔案")
        return false
    end
end

-- 尋找匹配的視窗
function findMatchingWindow(windows, windowData)
    -- 優先級匹配：
    -- 1. 完全匹配 (windowID + title)
    -- 2. 標題匹配
    -- 3. 第一個可用視窗

    for _, window in ipairs(windows) do
        if window:id() == windowData.windowID and
           window:title() == windowData.title then
            return window
        end
    end

    for _, window in ipairs(windows) do
        if window:title() == windowData.title then
            return window
        end
    end

    return windows[1] -- 回退到第一個視窗
end

-- 還原特殊應用數據
function restoreSpecialAppData(app, window, specialData)
    local appName = app:name()

    if specialData.type == "notion" and specialData.url then
        print("還原 Notion URL: " .. specialData.url)
        local script = string.format([[
            tell application "Notion"
                activate
                open location "%s"
            end tell
        ]], specialData.url)

        local success, result = hs.osascript.applescript(script)
        if not success then
            print("Notion URL 還原失敗: " .. tostring(result))
        end
    elseif specialData.type == "browser" and specialData.url then
        print("還原瀏覽器 URL: " .. specialData.url)
        local script = string.format([[
            tell application "%s"
                activate
                open location "%s"
            end tell
        ]], appName, specialData.url)

        local success, result = hs.osascript.applescript(script)
        if not success then
            print("瀏覽器 URL 還原失敗: " .. tostring(result))
        end
    end
end

-- 還原運行的應用程式
function restoreRunningApplications(savedApps)
    print("還原應用程式...")
    local currentApps = hs.application.runningApplications()
    local currentBundleIDs = {}

    -- 建立當前運行應用程式的索引
    for _, app in ipairs(currentApps) do
        if app:bundleID() then
            currentBundleIDs[app:bundleID()] = true
        end
    end

    local launchedCount = 0
    -- 啟動缺失的應用程式
    for _, savedApp in ipairs(savedApps) do
        if not currentBundleIDs[savedApp.bundleID] then
            print("啟動應用程式: " .. savedApp.name)
            local success = hs.application.launchOrFocusByBundleID(savedApp.bundleID)
            if success then
                launchedCount = launchedCount + 1
            else
                print("啟動失敗: " .. savedApp.name)
            end
        end
    end

    print("已啟動 " .. launchedCount .. " 個應用程式")
    return launchedCount
end

-- 還原視窗狀態
function restoreWindowStates(windowsData, targetSpaceID)
    print("還原視窗狀態...")
    local restoredCount = 0

    for _, windowData in ipairs(windowsData) do
        local app = hs.application.find(windowData.bundleID)
        if not app then
            app = hs.application.find(windowData.app)
        end

        if app then
            local windows = app:allWindows()
            if #windows > 0 then
                local targetWindow = findMatchingWindow(windows, windowData)

                if targetWindow then
                    print("還原視窗: " .. windowData.app .. " - " .. windowData.title)

                    -- 移動到正確的 space（如果需要）
                    local currentWindowSpaces = hs.spaces.windowSpaces(targetWindow)
                    if not hs.fnutils.contains(currentWindowSpaces, targetSpaceID) then
                        local success = hs.spaces.moveWindowToSpace(targetWindow, targetSpaceID)
                        if success then
                            print("  已移動到 space " .. targetSpaceID)
                        else
                            print("  移動到 space 失敗")
                        end
                    end

                    -- 還原位置和大小
                    local frame = hs.geometry.rect(
                        windowData.frame.x,
                        windowData.frame.y,
                        windowData.frame.w,
                        windowData.frame.h
                    )
                    targetWindow:setFrame(frame)

                    -- 還原最小化狀態
                    if windowData.isMinimized then
                        targetWindow:minimize()
                        print("  已最小化")
                    else
                        targetWindow:unminimize()
                    end

                    -- 特殊應用處理
                    if windowData.specialData then
                        restoreSpecialAppData(app, targetWindow, windowData.specialData)
                    end

                    restoredCount = restoredCount + 1
                else
                    print("找不到匹配的視窗: " .. windowData.app)
                end
            else
                print("應用程式沒有視窗: " .. windowData.app)
            end
        else
            print("找不到應用程式: " .. windowData.app)
        end
    end

    print("已還原 " .. restoredCount .. " 個視窗")
    return restoredCount
end

-- 還原當前 Space 的視窗佈局
function restoreCurrentSpaceLayout(profileName)
    print("=== restoreCurrentSpaceLayout 開始 ===")
    print("Profile 名稱: " .. tostring(profileName))

    -- 載入保存的佈局數據
    local filePath = layoutsPath .. profileName .. "_space.json"
    local file = io.open(filePath, "r")
    if not file then
        hs.alert.show("找不到 Space 佈局「" .. profileName .. "」")
        print("❌ 找不到檔案: " .. filePath)
        return false
    end

    local content = file:read("*all")
    file:close()

    local success, layoutData = pcall(hs.json.decode, content)
    if not success or not layoutData then
        hs.alert.show("Space 佈局檔案格式錯誤")
        print("❌ JSON 解析失敗")
        return false
    end

    -- 檢查版本和類型
    if layoutData.version ~= "2.0" or layoutData.type ~= "space-specific" then
        hs.alert.show("不相容的佈局檔案版本")
        print("❌ 版本不相容: " .. tostring(layoutData.version))
        return false
    end

    print("載入的佈局數據:")
    print("  Space ID: " .. tostring(layoutData.spaceID))
    print("  螢幕 UUID: " .. tostring(layoutData.screenUUID))
    print("  視窗數量: " .. #layoutData.windows)
    print("  應用程式數量: " .. #layoutData.runningApps)

    -- 確認當前 space
    local currentSpaceID = hs.spaces.focusedSpace()
    local targetSpaceID = layoutData.spaceID

    if currentSpaceID ~= targetSpaceID then
        hs.alert.show("切換到目標 Space...")
        print("切換 Space: " .. currentSpaceID .. " -> " .. targetSpaceID)

        local success = hs.spaces.gotoSpace(targetSpaceID)
        if not success then
            hs.alert.show("無法切換到目標 Space")
            print("❌ Space 切換失敗")
            return false
        end

        -- 等待 space 切換完成
        hs.timer.doAfter(1, function()
            continueRestore(layoutData)
        end)
        return true
    else
        return continueRestore(layoutData)
    end
end

-- 繼續還原過程（在正確的 space 中）
function continueRestore(layoutData)
    print("在正確的 Space 中繼續還原...")

    -- 重新啟動已關閉的應用程式
    local launchedCount = restoreRunningApplications(layoutData.runningApps)

    -- 如果有應用程式需要啟動，等待它們完全載入
    local waitTime = launchedCount > 0 and 3 or 1

    hs.timer.doAfter(waitTime, function()
        -- 還原視窗狀態
        local restoredCount = restoreWindowStates(layoutData.windows, layoutData.spaceID)

        hs.alert.show("✅ Space 佈局「" .. layoutData.profileName or "Unknown" .. "」已還原 (" .. restoredCount .. "/" .. #layoutData.windows .. " 個視窗)")
        print("=== restoreCurrentSpaceLayout 完成 ===")
    end)

    return true
end

-- 新的快捷鍵設定（Space-specific）
local spaceShortcuts = {
    { key = "6", save = true },  -- Cmd+Alt+6 保存當前 space
    { key = "7", save = true },  -- Cmd+Alt+7 保存當前 space
    { key = "8", save = true },  -- Cmd+Alt+8 保存當前 space
    { key = "9", save = true },  -- Cmd+Alt+9 保存當前 space
    { key = "0", save = true }   -- Cmd+Alt+0 保存當前 space
}

-- 註冊 Space-specific 快捷鍵
for _, shortcut in ipairs(spaceShortcuts) do
    -- 儲存當前 Space 佈局快捷鍵 (Cmd+Alt+數字)
    hs.hotkey.bind({"cmd", "alt"}, shortcut.key, function()
        saveCurrentSpaceLayout("SpaceProfile" .. shortcut.key)
    end)

    -- 還原 Space 佈局快捷鍵 (Cmd+Alt+Shift+數字)
    hs.hotkey.bind({"cmd", "alt", "shift"}, shortcut.key, function()
        restoreCurrentSpaceLayout("SpaceProfile" .. shortcut.key)
    end)
end

-- 初始化
ensureLayoutsDirectory()
hs.alert.show("Workspace 配置已載入，AppleScript 已啟用，Space 功能已啟用")