# 實作計劃

- [x] 1. 建立 Space 偵測核心功能

  - 建立 `SpaceDetector` 類別，實作 macOS Spaces 偵測功能
  - 實作 `SpaceInfo` 資料結構來表示 Space 資訊
  - 建立單元測試驗證 Space 偵測功能
  - _需求: 1.1, 4.1, 4.3_

- [x] 2. 擴展 Profile 模型以支援 Space 資訊

  - 在 `Profile` 結構中新增 `spaceID` 屬性
  - 更新 `Profile` 初始化器以支援 Space ID 參數
  - 實作 JSON 編碼/解碼以包含 Space 資訊
  - 建立測試驗證 Profile 模型的 Space 支援
  - _需求: 2.1, 2.2, 6.4_

- [x] 3. 建立 Space-Profile 映射管理器

  - 實作 `SpaceProfileManager` 類別管理 Space 和 Profile 的關聯
  - 建立 Space-Profile 映射的儲存和載入機制
  - 實作設定檔在 Spaces 間移動的功能
  - 建立單元測試驗證映射管理功能
  - _需求: 1.2, 2.4, 4.4, 6.1_

- [x] 4. 更新 ProfileManager 以整合 Space 功能

  - 修改 `ProfileManager.loadProfiles()` 以載入 Space 資訊
  - 更新 `ProfileManager.saveProfile()` 以儲存 Space 關聯
  - 實作設定檔的 Space 感知過濾功能
  - 建立測試驗證 ProfileManager 的 Space 整合
  - _需求: 1.3, 2.1, 6.2_

- [x] 5. 擴展 HammerspoonManager 以支援 Space 感知操作

  - 更新 `saveCurrentLayout()` 以包含當前 Space 資訊
  - 修改 `restoreLayout()` 以驗證 Space 上下文
  - 實作跨 Space 還原的確認機制
  - 建立測試驗證 Hammerspoon 的 Space 整合
  - _需求: 2.1, 3.1, 3.2, 3.4_

- [x] 6. 建立 Space 標籤 UI 組件

  - 實作 `SpaceTabView` 顯示可用的 Spaces
  - 建立 `SpaceTabButton` 組件用於 Space 選擇
  - 實作 Space 切換時的 UI 狀態更新
  - 建立 UI 測試驗證 Space 標籤功能
  - _需求: 4.2, 5.1, 5.3_

- [x] 7. 建立 Space 感知的設定檔列表 UI

  - 實作 `SpaceAwareProfileView` 作為主要 UI 組件
  - 修改設定檔列表以顯示 Space 分組
  - 實作空 Space 的空白狀態顯示
  - 建立 UI 測試驗證設定檔列表功能
  - _需求: 1.1, 1.4, 5.1, 5.4_

- [x] 8. 更新 ProfileEditorView 以支援 Space 資訊

  - 在設定檔編輯器中顯示 Space 資訊
  - 實作設定檔在 Spaces 間移動的 UI
  - 更新設定檔詳細資訊以包含 Space 上下文
  - 建立測試驗證編輯器的 Space 功能
  - _需求: 4.4, 5.2_

- [x] 9. 實作錯誤處理和資料驗證

  - 建立 Space 無法存取時的錯誤處理機制
  - 實作跨 Space 操作的確認對話框
  - 建立資料一致性檢查和修復功能
  - 實作錯誤訊息的本地化顯示
  - _需求: 3.2, 3.4, 4.3, 6.3_

- [x] 10. 實作資料遷移和向後相容性

  - 建立現有設定檔到 Space 系統的自動遷移
  - 實作 Space 映射檔案的建立和初始化
  - 確保舊版本設定檔格式的相容性
  - 建立遷移測試驗證資料完整性
  - _需求: 6.4_

- [x] 11. 整合所有組件並建立主要 UI 流程

  - 將 `SpaceAwareProfileView` 整合到主應用程式
  - 建立完整的 Space 分組工作流程
  - 實作 Space 切換時的狀態同步
  - 建立整合測試驗證完整功能
  - _需求: 1.1, 1.2, 1.3, 5.3_

- [x] 12. 效能最佳化和使用者體驗改善
  - 實作設定檔的懶載入機制
  - 建立 Space 偵測結果的快取
  - 最佳化 UI 響應時間和動畫效果
  - 建立效能測試驗證最佳化效果
  - _需求: 1.3, 5.3_
