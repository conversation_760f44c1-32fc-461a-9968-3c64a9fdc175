# 設計文檔

## 概述

此設計文檔描述了如何在現有的 Workspace 應用程式中實現基於 macOS Spaces 的設定檔分組功能。該功能將允許使用者依照 macOS Spaces 組織視窗佈局設定檔，每個 Space 可以包含多個設定檔，並支援 Space 感知的儲存和還原操作。

基於對現有程式碼的分析，我們將擴展現有的 `Profile` 模型、`ProfileManager` 和相關 UI 組件，以支援 Space 分組功能。

## 架構

### 核心組件架構

```mermaid
graph TB
    A[SpaceProfileManager] --> B[ProfileManager]
    A --> C[SpaceDetector]
    A --> D[HammerspoonManager]
    
    B --> E[Profile Model]
    C --> F[macOS Spaces API]
    D --> G[Hammerspoon Lua Scripts]
    
    H[SpaceAwareProfileView] --> A
    I[SpaceTabView] --> A
    J[ProfileEditorView] --> A
    
    K[Storage Layer] --> L[Space-Profile Mapping]
    K --> M[Profile JSON Files]
```

### 資料流架構

1. **Space 偵測**: `SpaceDetector` 負責偵測當前 Space 和可用 Spaces
2. **設定檔管理**: `SpaceProfileManager` 協調 Space 和 Profile 的關聯
3. **UI 更新**: Space 感知的 UI 組件根據當前 Space 顯示相關設定檔
4. **儲存機制**: 擴展現有的 JSON 儲存格式以包含 Space 資訊

## 組件和介面

### 1. SpaceDetector

負責偵測和管理 macOS Spaces 資訊的核心組件。

```swift
class SpaceDetector: ObservableObject {
    @Published var currentSpaceID: Int?
    @Published var availableSpaces: [SpaceInfo]
    @Published var maxSupportedSpaces: Int = 3
    
    func getCurrentSpace() -> Int?
    func getAvailableSpaces() -> [SpaceInfo]
    func isSpaceAccessible(_ spaceID: Int) -> Bool
}

struct SpaceInfo: Identifiable, Codable {
    let id: Int
    let name: String
    let isActive: Bool
    let displayName: String
}
```

### 2. SpaceProfileManager

管理 Space 和 Profile 關聯的主要管理器。

```swift
class SpaceProfileManager: ObservableObject {
    @Published var spaceProfileMapping: [Int: [Profile]]
    @Published var currentSpace: SpaceInfo?
    
    private let profileManager: ProfileManager
    private let spaceDetector: SpaceDetector
    
    func getProfilesForSpace(_ spaceID: Int) -> [Profile]
    func getProfilesForCurrentSpace() -> [Profile]
    func saveProfileToSpace(_ profile: Profile, spaceID: Int)
    func moveProfileBetweenSpaces(_ profile: Profile, from: Int, to: Int)
    func deleteProfileFromSpace(_ profile: Profile, spaceID: Int)
}
```

### 3. 擴展的 Profile 模型

擴展現有的 `Profile` 結構以支援 Space 資訊。

```swift
extension Profile {
    var spaceID: Int? // 新增 Space 識別碼
    var isSpaceSpecific: Bool // 現有屬性，用於向後相容
    
    init(name: String, windows: [WindowLayout], spaceID: Int?) {
        // 擴展初始化器以支援 Space ID
    }
}
```

### 4. SpaceAwareProfileView

新的主要 UI 組件，支援 Space 分組顯示。

```swift
struct SpaceAwareProfileView: View {
    @StateObject private var spaceProfileManager = SpaceProfileManager.shared
    @State private var selectedSpaceID: Int?
    
    var body: some View {
        VStack {
            SpaceTabView(selectedSpaceID: $selectedSpaceID)
            ProfileListView(spaceID: selectedSpaceID)
        }
    }
}
```

### 5. SpaceTabView

顯示 Space 標籤的 UI 組件。

```swift
struct SpaceTabView: View {
    @Binding var selectedSpaceID: Int?
    @StateObject private var spaceDetector = SpaceDetector.shared
    
    var body: some View {
        HStack {
            ForEach(spaceDetector.availableSpaces) { space in
                SpaceTabButton(space: space, isSelected: selectedSpaceID == space.id)
            }
        }
    }
}
```

## 資料模型

### Space-Profile 映射結構

```json
{
  "spaceProfileMapping": {
    "1": ["profile1", "profile2", "profile3"],
    "2": ["profile4", "profile5", "profile6"],
    "3": ["profile7", "profile8"]
  },
  "defaultSpace": 1,
  "lastUpdated": "2025-01-08T10:30:00Z"
}
```

### 擴展的 Profile JSON 格式

```json
{
  "name": "profile1",
  "spaceID": 1,
  "isSpaceSpecific": true,
  "windows": [...],
  "createdAt": "2025-01-08T10:00:00Z",
  "modifiedAt": "2025-01-08T10:30:00Z"
}
```

### SpaceInfo 資料結構

```json
{
  "id": 1,
  "name": "Space 1",
  "isActive": true,
  "displayName": "工作區 1"
}
```

## 錯誤處理

### Space 偵測錯誤

1. **Space 無法存取**: 當某個 Space 無法存取時，系統將該 Space 標記為不可用，但保留其設定檔資料
2. **Space ID 變更**: 當 macOS 重新分配 Space ID 時，系統將嘗試透過其他識別方式重新映射
3. **預設 Space 回退**: 當無法偵測當前 Space 時，系統將使用預設 Space (ID: 1)

### 設定檔操作錯誤

1. **跨 Space 還原警告**: 當使用者嘗試在不同 Space 還原設定檔時，顯示確認對話框
2. **設定檔遺失**: 當 Space 中的設定檔檔案遺失時，從映射中移除該設定檔
3. **儲存失敗**: 當設定檔儲存失敗時，回滾 Space 映射變更

### 資料一致性

1. **映射檔案損壞**: 當 Space-Profile 映射檔案損壞時，重建映射並將現有設定檔分配到預設 Space
2. **孤立設定檔**: 定期掃描並處理沒有 Space 關聯的設定檔
3. **重複設定檔**: 檢測並處理在多個 Space 中出現的重複設定檔

## 測試策略

### 單元測試

1. **SpaceDetector 測試**
   - 測試 Space 偵測功能
   - 測試 Space 可存取性檢查
   - 測試錯誤處理機制

2. **SpaceProfileManager 測試**
   - 測試 Space-Profile 映射操作
   - 測試設定檔在 Spaces 間的移動
   - 測試資料持久化

3. **Profile 模型測試**
   - 測試擴展的 Profile 初始化
   - 測試 JSON 序列化/反序列化
   - 測試向後相容性

### 整合測試

1. **UI 整合測試**
   - 測試 Space 標籤切換
   - 測試設定檔列表更新
   - 測試跨 Space 操作

2. **Hammerspoon 整合測試**
   - 測試 Space 感知的儲存操作
   - 測試 Space 感知的還原操作
   - 測試錯誤情況處理

### 使用者接受測試

1. **工作流程測試**
   - 測試完整的 Space 分組工作流程
   - 測試多 Space 環境下的使用體驗
   - 測試錯誤恢復情況

2. **效能測試**
   - 測試大量設定檔的載入效能
   - 測試 Space 切換的響應時間
   - 測試記憶體使用情況

## 實作考量

### 向後相容性

1. **現有設定檔遷移**: 自動將現有的非 Space 設定檔分配到預設 Space
2. **檔案格式相容**: 保持現有 JSON 格式的相容性，新增可選的 Space 欄位
3. **UI 漸進升級**: 在現有 UI 基礎上漸進式添加 Space 功能

### 效能最佳化

1. **懶載入**: 只載入當前 Space 的設定檔，減少記憶體使用
2. **快取機制**: 快取 Space 偵測結果，減少系統呼叫
3. **批次操作**: 批次處理設定檔操作，提高效率

### 使用者體驗

1. **視覺指示**: 清楚顯示每個設定檔所屬的 Space
2. **快速切換**: 提供快速的 Space 切換機制
3. **錯誤回饋**: 提供清楚的錯誤訊息和恢復建議

### 安全性

1. **資料驗證**: 驗證 Space ID 和設定檔資料的有效性
2. **權限檢查**: 確保只能存取可用的 Spaces
3. **資料備份**: 在進行重要操作前備份設定檔資料