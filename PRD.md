## 1. 產品概述 (Product Overview)

### 1.1 產品願景 (Vision)
`Workspace for macOS` 是一款 macOS 上的效率工具，旨在為專業人士和高級用戶提供一個直觀、優雅的圖形化介面，用於管理和一鍵切換基於 Hammerspoon 的多個視窗佈局 Profile。它將 Hammerspoon 的強大功能與 macOS 的原生體驗無縫結合，讓用戶可以為不同的工作場景（如編程、設計、通訊）創建和恢復專屬的桌面工作區，從而極大地提升工作效率和專注度。

### 1.2 目標用戶 (Target Audience)
*   **軟體開發者、設計師、內容創作者**：他們需要在多個應用程式和視窗之間頻繁切換，對工作區佈局有著高度個人化的要求。
*   **Hammerspoon 現有用戶**：他們已經了解 Hammerspoon 的強大，但希望有一個更直觀的 GUI 來管理和觸發他們的佈局設定。
*   **效率工具愛好者**：他們追求極致的工作效率，樂於嘗試能優化其 macOS 使用體驗的各種工具。

### 1.3 核心問題與解決方案 (Problem & Solution)
*   **問題**:
    1.  手動排列應用程式視窗耗時且重複。
    2.  Hammerspoon 功能強大，但設定和管理完全基於純文字的 `.lua` 腳本，對非程式設計師用戶不友好，且缺乏直觀的視覺反饋。
    3.  在不同工作任務間切換時，沒有一個快速恢復對應視窗佈局的方案。

*   **解決方案**:
    1.  提供一個常駐在選單列的輕量級入口。
    2.  以圖形化列表展示所有已儲存的視窗佈局 Profile。
    3.  為每個 Profile 提供視覺化的預覽，讓用戶一目了然。
    4.  提供點擊操作，讓用戶無需記憶快捷鍵也能輕鬆切換佈局。
    5.  保留對 Hammerspoon 原生快捷鍵的兼容性，滿足高級用戶的需求。

## 2. 功能需求 (Functional Requirements)

### 2.1 FR-1: 選單列常駐應用 (Menu Bar App)
*   **2.1.1**: 應用程式啟動後，應在 macOS 的選單列（Menu Bar）顯示一個獨特的圖示。
*   **2.1.2**: 點擊該圖示，應彈出一個主下拉式功能表。
*   **2.1.3**: 右鍵點擊圖示，應顯示一個包含「關於」、「檢查更新」、「結束應用」等選項的上下文菜單。
*   **2.1.4**: 應用程式應能設置為開機自動啟動。

### 2.2 FR-2: Profile 列表與觸發 (Profile List & Trigger)
*   **2.2.1**: 主下拉式功能表的核心區域應動態顯示一個 Profile 列表。
*   **2.2.2**: 列表應自動掃描 `~/.hammerspoon/layouts/` 目錄下的所有 `.json` 檔案，並將檔案名（去除 `.json` 後綴）作為 Profile 名稱顯示。
*   **2.2.3**: 每個 Profile 條目應包含：Profile 名稱、一個代表該佈局的微縮預覽圖（參考 FR-3.2）。
*   **2.2.4**: 點擊列表中的任意一個 Profile，應觸發還原該 Profile 的操作（等同於執行對應的 Hammerspoon 還原函式）。
*   **2.2.5**: 列表頂部或底部應有一個醒目的「儲存當前佈局」按鈕，點擊後觸發儲存當前佈局到對應的 Profile（如果當前 Space 已映射）。

### 2.3 FR-3: Profile 編輯與預覽 (Profile Editor & Preview)
*   **2.3.1**: 在主下拉式功能表中，每個 Profile 條目旁邊應有一個「編輯」(例如鉛筆圖示)按鈕，點擊後打開 Profile 編輯視窗。
*   **2.3.2**: **(核心功能)** 編輯視窗應能讀取並解析對應的 `.json` 檔案，並在一個模擬的畫布上**視覺化地渲染**出該 Profile 的佈局。
    *   每個已儲存的應用程式視窗應以一個帶有該 App 圖示和標題的矩形表示。
    *   矩形的位置和大小應按比例反映其儲存的 `frame` 數據。
*   **2.3.3**: 在編輯視窗中，用戶應可以修改 Profile 的名稱（這將重命名對應的 `.json` 檔案）。
*   **2.3.4**: 編輯視窗應提供「刪除 Profile」的按鈕，點擊後刪除對應的 `.json` 檔案。

### 2.4 FR-4: Hammerspoon 整合與快捷鍵說明 (Hammerspoon Integration)
*   **2.4.1**: 應用程式啟動時，應檢查用戶系統中是否已安裝 Hammerspoon。若未安裝，應給予清晰的提示，並引導用戶前往官網下載。
*   **2.4.2**: 應用程式應能讀取 `~/.hammerspoon/init.lua` 檔案（或通過其他方式），以確保其後端邏輯已正確配置。若未配置，可提供一個按鈕讓 App 自動寫入或更新必要的 Hammerspoon 函式。
*   **2.4.3**: 在主下拉式功能表中，應有一個「快捷鍵說明」的選項。
*   **2.4.4**: 點擊「快捷鍵說明」，應彈出一個視窗或視圖，以清晰的列表展示 Hammerspoon 中已設定的用於「儲存」和「還原」各個 Profile 的快捷鍵組合。

### 2.5 FR-5: 使用者自訂設定 (User Settings)
*   **2.5.1**: 在主下拉式功能表中，應有「設定」選項，點擊後打開設定視窗。
*   **2.5.2**: 在設定視窗中，用戶可以自訂**進度提示**的視覺樣式，應包括：
    *   **字體大小**：一個滑桿或輸入框，用於調整 `hs.alert` 的 `textSize`。
    *   **顯示速度**：一個滑桿或下拉式功能表（例如「快」、「中」、「慢」），用於調整進度提示的 `duration` 和 `hs.sleep` 的時長。
*   **2.5.3**: 這些設定的更改應即時寫入一個 App 自身的設定檔，並在觸發 Hammerspoon 操作時作為參數傳遞，或直接修改 `init.lua` 中的 `alertStyle` table。

## 3. 非功能性需求 (Non-Functional Requirements)

*   **NF-1: 性能 (Performance)**: 應用程式應保持輕量，佔用系統資源少。掃描 Profile 和渲染預覽圖的過程不應造成可感知的卡頓。
*   **NF-2: 使用者體驗 (UX)**: 介面設計應簡潔、直觀，符合 macOS 的原生設計語言。所有操作都應有即時的視覺回饋。
*   **NF-3: 相容性 (Compatibility)**: 應明確支援最新版本的 macOS 和 Hammerspoon。對於舊版本，應做優雅降級或給出提示。
*   **NF-4: 穩定性 (Stability)**: 與 Hammerspoon 的通信應建立穩健的錯誤處理機制。App 本身不應因 Hammerspoon 腳本執行失敗而崩潰。

## 4. 技術架構概要 (Technical Architecture Outline)

*   **前端**: 使用 SwiftUI 進行開發，以確保現代化的 UI 和跨 Apple 平台的潛力。
*   **後端/核心邏輯**: 依賴用戶本地安裝的 Hammerspoon 實例。
*   **通信機制**:
    *   **App -> Hammerspoon**: 主要通過 **URL Scheme** (`hammerspoon://runLua?code=...`) 或 **`hs.ipc`** 模組。URL Scheme 更簡單，`hs.ipc` 更強大。推薦初期使用 URL Scheme。
    *   **App 讀取狀態**: 直接讀取 `~/.hammerspoon/layouts/` 目錄下的 `.json` 檔案來獲取 Profile 資訊。
*   **資料儲存**:
    *   **佈局 Profile**: `~/.hammerspoon/layouts/*.json`。
    *   **App 自身設定**: 使用 macOS 的 `UserDefaults` 來儲存字體大小、顯示速度等使用者設定。

## 5. 里程碑 (Milestones) - 可選

*   **M1 (MVP - 最小可行產品)**:
    *   實現選單列常駐和 Profile 列表顯示 (FR-1, FR-2.1, FR-2.2)。
    *   實現點擊列表觸發還原 (FR-2.4)。
    *   實現快捷鍵說明頁面 (FR-4.3, FR-4.4)。
    *   基本的 Hammerspoon 安裝檢查。
*   **M2 (功能增強)**:
    *   實現 Profile 編輯視窗和視覺化預覽 (FR-3)。
    *   實現「儲存當前佈局」按鈕。
*   **M3 (體驗優化)**:
    *   實現使用者自訂設定 (FR-5)。
    *   完善錯誤處理和使用者引導。
    *   增加開機啟動選項。

---