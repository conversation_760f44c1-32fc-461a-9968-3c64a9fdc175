# 視窗預覽系統 - 故障排除指南

## 概述

本指南提供視窗預覽系統常見問題的診斷和解決方案。問題按類別組織，每個問題都包含症狀描述、可能原因、診斷步驟和解決方案。

## 快速診斷檢查清單

在深入具體問題之前，請先執行以下基本檢查：

- [ ] 確認 Profile 包含有效的視窗數據
- [ ] 檢查系統記憶體使用情況
- [ ] 驗證應用程式版本和系統兼容性
- [ ] 查看控制台是否有錯誤訊息
- [ ] 嘗試重新啟動應用程式

## 顯示問題

### 問題 1: 預覽區域顯示空白

**症狀**:
- 預覽區域完全空白，沒有任何視窗顯示
- 模式選擇器正常顯示，但內容區域為空

**可能原因**:
1. Profile 不包含視窗數據
2. 所有視窗的框架數據無效
3. 渲染引擎初始化失敗
4. 視窗數據格式不正確

**診斷步驟**:
```swift
// 1. 檢查視窗數據
print("視窗數量: \(profile.windows.count)")
for window in profile.windows {
    print("視窗: \(window.app) - 框架: \(window.frame)")
}

// 2. 檢查框架有效性
let invalidWindows = profile.windows.filter { window in
    !window.frame.x.isFinite || !window.frame.y.isFinite ||
    !window.frame.w.isFinite || !window.frame.h.isFinite ||
    window.frame.w <= 0 || window.frame.h <= 0
}
print("無效視窗數量: \(invalidWindows.count)")

// 3. 檢查錯誤狀態
if let errorState = previewContainer.errorState {
    print("錯誤狀態: \(errorState)")
}
```

**解決方案**:
1. **無視窗數據**: 確保 Profile 包含至少一個有效視窗
2. **無效框架數據**: 清理或修復視窗框架數據
3. **渲染失敗**: 重新啟動應用程式或切換預覽模式
4. **數據格式錯誤**: 驗證 WindowLayout 結構的完整性

### 問題 2: 視窗重疊嚴重，無法清楚查看

**症狀**:
- 多個視窗完全重疊，無法區分
- 點擊重疊區域無法選擇到想要的視窗
- 重疊指示器不顯示或不準確

**可能原因**:
1. 重疊檢測算法失效
2. 透明度調整不正確
3. Z軸排序問題
4. 縮放比例計算錯誤

**診斷步驟**:
```swift
// 檢查重疊檢測結果
let overlapInfo = OverlapResolver.detectOverlaps(windows: profile.windows)
print("檢測到重疊: \(overlapInfo.hasOverlaps)")
print("重疊視窗對數: \(overlapInfo.overlapPairs.count)")

// 檢查顯示信息
let displayInfo = ScaledPreviewMode.calculateLayout(
    windows: profile.windows,
    canvasSize: canvasSize
)
for info in displayInfo {
    print("視窗: \(info.window.app), 重疊層級: \(info.overlapLevel), 透明度: \(info.opacity)")
}
```

**解決方案**:
1. **切換預覽模式**: 使用網格模式或列表模式避免重疊
2. **啟用重疊指示器**: 在設置中開啟重疊指示器
3. **調整透明度設置**: 增加重疊視窗的透明度差異
4. **使用多選模式**: 按住 Cmd 鍵循環選擇重疊視窗

### 問題 3: 視窗顯示位置不正確

**症狀**:
- 視窗在預覽中的位置與實際位置不符
- 多螢幕配置下視窗位置錯亂
- 縮放後視窗比例失真

**可能原因**:
1. 座標系統轉換錯誤
2. 多螢幕檢測失敗
3. 縮放計算不準確
4. 螢幕邊界檢測錯誤

**診斷步驟**:
```swift
// 檢查螢幕邊界檢測
let screenBounds = ScreenBounds.detectScreenBounds(from: profile.windows)
print("檢測到的螢幕數量: \(screenBounds.count)")
for screen in screenBounds {
    print("螢幕: \(screen.displayName), 邊界: \(screen.bounds)")
}

// 檢查座標轉換
let originalFrame = window.frame
let displayFrame = convertToDisplayFrame(originalFrame, scaleFactor: scaleFactor)
print("原始框架: \(originalFrame)")
print("顯示框架: \(displayFrame)")
```

**解決方案**:
1. **重新檢測螢幕配置**: 使用小地圖模式重新檢測螢幕邊界
2. **手動調整縮放**: 在設置中調整縮放比例
3. **使用絕對座標**: 切換到使用絕對座標的顯示模式
4. **更新螢幕信息**: 重新獲取系統螢幕配置信息

## 性能問題

### 問題 4: 預覽載入緩慢

**症狀**:
- 切換預覽模式時出現長時間載入
- 大量視窗時預覽響應遲緩
- 滾動時出現卡頓

**可能原因**:
1. 視窗數量過多
2. 虛擬化未啟用
3. 快取機制失效
4. 批次更新配置不當

**診斷步驟**:
```swift
// 檢查性能指標
let metrics = PerformanceMonitor.shared.currentMetrics
print("幀率: \(metrics.frameRate) fps")
print("渲染時間: \(metrics.renderingTime * 1000) ms")
print("記憶體使用: \(metrics.memoryUsage / 1024 / 1024) MB")

// 檢查虛擬化狀態
let virtualizationManager = VirtualizationManager.shared
print("虛擬化已啟用: \(virtualizationManager.isEnabled)")
print("可見項目數量: \(virtualizationManager.visibleItemCount)")
print("總項目數量: \(virtualizationManager.totalItemCount)")

// 檢查快取效率
let cacheManager = RenderCacheManager.shared
print("快取命中率: \(cacheManager.hitRate * 100)%")
print("快取大小: \(cacheManager.cacheSize)")
```

**解決方案**:
1. **啟用虛擬化**: 確保大量視窗時自動啟用虛擬化渲染
2. **調整批次大小**: 減少批次更新的大小和頻率
3. **清理快取**: 清除過期的渲染快取
4. **降低視覺效果**: 關閉動畫和透明效果

**性能優化配置**:
```swift
// 針對大量視窗的優化配置
let optimizedConfig = PreviewConfiguration(
    mode: .grid,                    // 使用網格模式
    showLabels: false,              // 關閉標籤顯示
    showOverlapIndicators: false,   // 關閉重疊指示器
    groupByApplication: true,       // 啟用應用程式分組
    maxWindowsPerView: 30,         // 限制同時顯示的視窗數
    enableAnimations: false,        // 關閉動畫
    showWindowDetails: false        // 關閉詳細信息面板
)
```

### 問題 5: 記憶體使用過高

**症狀**:
- 應用程式記憶體使用持續增長
- 系統變得緩慢或不穩定
- 出現記憶體警告

**可能原因**:
1. 記憶體洩漏
2. 快取過度積累
3. 大量視窗數據未釋放
4. 圖像資源未正確管理

**診斷步驟**:
```swift
// 監控記憶體使用
func monitorMemoryUsage() {
    let memoryInfo = mach_task_basic_info()
    var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
    
    let kerr: kern_return_t = withUnsafeMutablePointer(to: &memoryInfo) {
        $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
            task_info(mach_task_self_,
                     task_flavor_t(MACH_TASK_BASIC_INFO),
                     $0,
                     &count)
        }
    }
    
    if kerr == KERN_SUCCESS {
        let memoryUsage = memoryInfo.resident_size
        print("記憶體使用: \(memoryUsage / 1024 / 1024) MB")
    }
}

// 檢查快取大小
let cacheSize = RenderCacheManager.shared.totalCacheSize
print("快取大小: \(cacheSize / 1024 / 1024) MB")
```

**解決方案**:
1. **清理快取**: 定期清理渲染快取和圖像快取
2. **限制視窗數量**: 使用分頁或虛擬化限制同時載入的視窗
3. **優化圖像處理**: 使用適當的圖像壓縮和快取策略
4. **檢查記憶體洩漏**: 使用 Instruments 檢測記憶體洩漏

## 交互問題

### 問題 6: 無法選擇視窗

**症狀**:
- 點擊視窗沒有反應
- 選擇狀態不更新
- 多選模式不工作

**可能原因**:
1. 點擊區域計算錯誤
2. 事件處理被阻塞
3. 選擇狀態管理問題
4. 視窗 ID 不匹配

**診斷步驟**:
```swift
// 檢查點擊區域
func debugTapGesture(at location: CGPoint) {
    print("點擊位置: \(location)")
    
    // 檢查哪些視窗包含此點
    let hitWindows = displayInfo.filter { info in
        info.displayFrame.contains(location)
    }
    
    print("命中的視窗數量: \(hitWindows.count)")
    for window in hitWindows {
        print("視窗: \(window.window.app), 框架: \(window.displayFrame)")
    }
}

// 檢查選擇狀態
print("當前選中視窗: \(selectedWindow?.app ?? "無")")
print("多選視窗數量: \(selectedWindows.count)")
print("多選模式: \(isMultiSelectMode)")
```

**解決方案**:
1. **調整點擊區域**: 增大視窗的可點擊區域
2. **檢查事件處理**: 確保沒有其他手勢識別器干擾
3. **重置選擇狀態**: 清除並重新初始化選擇狀態
4. **驗證視窗 ID**: 確保視窗 ID 的一致性

### 問題 7: 鍵盤導航不工作

**症狀**:
- 方向鍵無法在視窗間導航
- Tab 鍵無法切換焦點
- 鍵盤快捷鍵不響應

**可能原因**:
1. 焦點管理問題
2. 鍵盤事件被攔截
3. 無障礙設置不正確
4. 視圖層次結構問題

**診斷步驟**:
```swift
// 檢查焦點狀態
print("預覽區域有焦點: \(isPreviewFocused)")
print("當前焦點視窗索引: \(focusedWindowIndex)")
print("可導航視窗數量: \(visibleWindows.count)")

// 檢查無障礙設置
print("VoiceOver 已啟用: \(NSWorkspace.shared.isVoiceOverEnabled)")
print("鍵盤導航已啟用: \(NSApplication.shared.isFullKeyboardAccessEnabled)")
```

**解決方案**:
1. **設置正確的焦點**: 確保預覽區域可以獲得鍵盤焦點
2. **實現鍵盤事件處理**: 添加適當的鍵盤事件響應
3. **檢查無障礙設置**: 啟用系統的無障礙功能
4. **調整視圖層次**: 確保鍵盤事件能正確傳遞

## 配置問題

### 問題 8: 設置不保存

**症狀**:
- 預覽模式切換後不記住選擇
- 自定義配置重啟後丟失
- 用戶偏好設置無效

**可能原因**:
1. UserDefaults 寫入失敗
2. 配置序列化問題
3. 權限不足
4. 配置文件損壞

**診斷步驟**:
```swift
// 檢查 UserDefaults
let defaults = UserDefaults.standard
print("保存的預覽模式: \(defaults.string(forKey: "previewMode") ?? "無")")

// 檢查配置序列化
do {
    let configData = try JSONEncoder().encode(currentConfiguration)
    let decodedConfig = try JSONDecoder().decode(PreviewConfiguration.self, from: configData)
    print("配置序列化成功")
} catch {
    print("配置序列化失敗: \(error)")
}

// 檢查文件權限
let configURL = PreviewConfigurationManager.configurationURL
print("配置文件路徑: \(configURL)")
print("文件可寫: \(FileManager.default.isWritableFile(atPath: configURL.path))")
```

**解決方案**:
1. **檢查權限**: 確保應用程式有寫入配置文件的權限
2. **修復序列化**: 檢查配置結構的 Codable 實現
3. **重置配置**: 刪除損壞的配置文件，使用默認設置
4. **使用備用存儲**: 如果 UserDefaults 失敗，使用文件存儲

### 問題 9: 自動優化不工作

**症狀**:
- 大量視窗時不自動切換到網格模式
- 性能優化配置不生效
- 虛擬化不自動啟用

**可能原因**:
1. 自動優化邏輯錯誤
2. 閾值設置不當
3. 配置覆蓋問題
4. 性能檢測失敗

**診斷步驟**:
```swift
// 檢查自動優化邏輯
let windowCount = profile.windows.count
let shouldOptimize = windowCount > 30
print("視窗數量: \(windowCount)")
print("應該優化: \(shouldOptimize)")

// 檢查優化配置
let optimizedConfig = PreviewConfiguration.optimized(for: windowCount)
print("優化後的模式: \(optimizedConfig.mode)")
print("優化後的設置: \(optimizedConfig)")

// 檢查性能指標
let metrics = PerformanceMonitor.shared.currentMetrics
print("當前性能是否最佳: \(metrics.isPerformanceOptimal)")
```

**解決方案**:
1. **調整閾值**: 根據實際性能調整自動優化的閾值
2. **手動觸發優化**: 提供手動觸發優化的選項
3. **檢查配置優先級**: 確保自動優化配置不被用戶設置覆蓋
4. **改進性能檢測**: 使用更準確的性能指標

## 兼容性問題

### 問題 10: 在舊版 macOS 上不工作

**症狀**:
- 應用程式在舊版系統上崩潰
- 某些功能不可用
- 視覺效果異常

**可能原因**:
1. 使用了新版本 API
2. SwiftUI 版本不兼容
3. 系統框架缺失
4. 硬體加速不支持

**診斷步驟**:
```swift
// 檢查系統版本
if #available(macOS 12.0, *) {
    print("支持 macOS 12.0+")
} else {
    print("系統版本過舊")
}

// 檢查可用功能
let hasMetalSupport = MTLCreateSystemDefaultDevice() != nil
print("支持 Metal: \(hasMetalSupport)")

let hasVirtualizationSupport = ProcessInfo.processInfo.operatingSystemVersion.majorVersion >= 11
print("支持虛擬化: \(hasVirtualizationSupport)")
```

**解決方案**:
1. **添加版本檢查**: 使用 `@available` 標記新功能
2. **提供降級方案**: 為舊版本提供簡化的功能
3. **更新最低系統要求**: 明確支持的最低系統版本
4. **使用兼容性 API**: 避免使用過新的 API

## 調試工具

### 性能調試面板

啟用性能調試面板來監控實時性能：

```swift
// 在 LayoutPreviewContainer 中啟用調試面板
@State private var showingPerformanceDebug: Bool = true

// 查看性能指標
struct PerformanceDebugView: View {
    let monitor: PerformanceMonitor
    
    var body: some View {
        VStack(alignment: .leading) {
            Text("性能指標")
                .font(.headline)
            
            HStack {
                Text("幀率:")
                Text("\(monitor.currentMetrics.frameRate, specifier: "%.1f") fps")
                    .foregroundColor(monitor.currentMetrics.frameRate >= 55 ? .green : .red)
            }
            
            HStack {
                Text("渲染時間:")
                Text("\(monitor.currentMetrics.renderingTime * 1000, specifier: "%.1f") ms")
                    .foregroundColor(monitor.currentMetrics.renderingTime <= 0.016 ? .green : .red)
            }
            
            HStack {
                Text("記憶體使用:")
                Text("\(monitor.currentMetrics.memoryUsage / 1024 / 1024) MB")
                    .foregroundColor(monitor.currentMetrics.memoryUsage <= 100_000_000 ? .green : .red)
            }
        }
        .padding()
        .background(Color.secondary.opacity(0.1))
        .cornerRadius(8)
    }
}
```

### 日誌記錄

啟用詳細日誌記錄來追蹤問題：

```swift
// 配置日誌級別
enum LogLevel: String {
    case debug = "DEBUG"
    case info = "INFO"
    case warning = "WARNING"
    case error = "ERROR"
}

class PreviewLogger {
    static let shared = PreviewLogger()
    
    func log(_ message: String, level: LogLevel = .info, file: String = #file, function: String = #function, line: Int = #line) {
        let fileName = (file as NSString).lastPathComponent
        let timestamp = DateFormatter.logFormatter.string(from: Date())
        
        print("[\(timestamp)] [\(level.rawValue)] [\(fileName):\(line)] \(function): \(message)")
    }
}

// 使用示例
PreviewLogger.shared.log("開始渲染預覽", level: .debug)
PreviewLogger.shared.log("檢測到 \(windowCount) 個視窗", level: .info)
PreviewLogger.shared.log("渲染時間超過閾值: \(renderTime)ms", level: .warning)
PreviewLogger.shared.log("渲染失敗: \(error)", level: .error)
```

### 診斷命令

提供診斷命令來快速檢查系統狀態：

```swift
class DiagnosticCommands {
    static func runFullDiagnostic() -> DiagnosticReport {
        var report = DiagnosticReport()
        
        // 系統信息
        report.systemInfo = SystemInfo.current
        
        // 性能指標
        report.performanceMetrics = PerformanceMonitor.shared.currentMetrics
        
        // 配置狀態
        report.configuration = PreviewConfigurationManager.shared.currentConfiguration
        
        // 錯誤日誌
        report.recentErrors = ErrorLogger.shared.getRecentErrors()
        
        return report
    }
    
    static func exportDiagnosticReport() -> URL {
        let report = runFullDiagnostic()
        let reportData = try! JSONEncoder().encode(report)
        
        let documentsPath = FileManager.default.urls(for: .documentsDirectory, in: .userDomainMask)[0]
        let reportURL = documentsPath.appendingPathComponent("preview_diagnostic_\(Date().timeIntervalSince1970).json")
        
        try! reportData.write(to: reportURL)
        return reportURL
    }
}
```

## 聯繫支持

如果以上解決方案都無法解決您的問題，請聯繫技術支持：

### 準備支持請求

在聯繫支持之前，請準備以下信息：

1. **系統信息**:
   - macOS 版本
   - 應用程式版本
   - 硬體配置

2. **問題描述**:
   - 具體症狀
   - 重現步驟
   - 預期行為

3. **診斷數據**:
   - 診斷報告（使用上述診斷命令生成）
   - 相關日誌文件
   - 螢幕截圖或錄影

### 支持渠道

- **GitHub Issues**: 報告 bug 和功能請求
- **用戶論壇**: 社群支持和討論
- **電子郵件**: 私人技術支持

---

*本故障排除指南會持續更新。如果您發現新的問題或解決方案，歡迎貢獻到文檔中。*