# 視窗預覽功能 - 用戶使用指南

## 概述

視窗預覽功能是 Profile 編輯器的核心組件，幫助您清晰地查看和管理視窗佈局。當您的工作空間包含大量視窗時，傳統的預覽方式可能會導致視窗重疊，難以識別。新的預覽系統提供了四種不同的查看模式，讓您能夠以最適合的方式預覽和管理您的視窗佈局。

## 功能特點

- **四種預覽模式**: 縮放、網格、列表、小地圖
- **智能重疊處理**: 自動檢測和避免視窗重疊
- **高性能渲染**: 支持大量視窗的流暢預覽
- **交互式操作**: 點擊選擇、懸停提示、詳細信息面板
- **無障礙支持**: 完整的鍵盤導航和螢幕閱讀器支持

## 快速開始

### 1. 打開 Profile 編輯器

在主界面中選擇要編輯的 Profile，或創建新的 Profile。

### 2. 查看預覽區域

預覽區域位於編輯器的右側，顯示當前 Profile 中所有視窗的佈局。

### 3. 選擇預覽模式

在預覽區域頂部，您會看到四個模式選擇按鈕：

- 🔲 **縮放預覽** - 智能縮放顯示
- ⊞ **網格預覽** - 整齊的網格排列
- ☰ **列表預覽** - 緊湊的列表顯示
- 🗺 **小地圖** - 多螢幕概覽

## 預覽模式詳解

### 縮放預覽模式 🔲

**最適合**: 視窗數量適中（少於30個），需要保持真實佈局關係

**特點**:
- 保持視窗的真實比例和相對位置
- 智能處理重疊視窗，避免完全遮擋
- 重疊視窗會自動調整透明度
- 支持層次化顯示

**使用技巧**:
- 重疊的視窗會以不同透明度顯示，層級越高越不透明
- 點擊重疊區域會循環選擇不同層級的視窗
- 懸停在視窗上會顯示詳細信息工具提示

### 網格預覽模式 ⊞

**最適合**: 大量視窗（30-100個），需要快速瀏覽所有視窗

**特點**:
- 所有視窗以統一大小的卡片形式排列
- 自動計算最佳網格配置
- 支持滾動瀏覽大量視窗
- 顯示應用程式圖標和基本信息

**使用技巧**:
- 使用滾動輪或觸控板滾動瀏覽
- 每個卡片顯示應用程式圖標、名稱和視窗標題
- 可以按住 Cmd 鍵進行多選
- 右鍵點擊卡片可以快速操作

### 列表預覽模式 ☰

**最適合**: 極大量視窗（超過100個），需要快速定位特定視窗

**特點**:
- 最緊湊的顯示方式，信息密度最高
- 支持水平滾動瀏覽
- 可選的應用程式分組功能
- 內建搜索和篩選功能

**使用技巧**:
- 使用搜索框快速定位特定視窗
- 啟用「按應用程式分組」選項來組織視窗
- 支持鍵盤快速導航（方向鍵、Page Up/Down）
- 雙擊列表項目可以快速跳轉到該視窗

### 小地圖模式 🗺

**最適合**: 多螢幕配置，需要整體佈局概覽

**特點**:
- 顯示完整的多螢幕佈局
- 視窗密度熱力圖顯示
- 支持縮放和平移操作
- 清楚標示螢幕邊界

**使用技巧**:
- 使用滾輪縮放查看不同詳細程度
- 拖拽可以平移視圖
- 不同顏色表示視窗密度（紅色=高密度，藍色=低密度）
- 點擊螢幕區域可以快速定位到該區域的視窗

## 交互操作

### 視窗選擇

**單選模式**（默認）:
- 點擊任何視窗進行選擇
- 選中的視窗會高亮顯示
- 自動顯示視窗詳細信息面板

**多選模式**:
- 按住 `Cmd` 鍵點擊多個視窗
- 或按 `空格鍵` 切換到多選模式
- 選中的視窗會以不同顏色標示
- 支持批次操作

### 鍵盤導航

- `方向鍵`: 在視窗間導航
- `空格鍵`: 切換多選模式
- `回車鍵`: 選擇當前焦點視窗
- `Tab`: 在不同區域間切換焦點
- `Esc`: 取消選擇或退出多選模式

### 懸停提示

將滑鼠懸停在任何視窗上，會顯示包含以下信息的工具提示：
- 應用程式名稱和圖標
- 視窗標題
- 視窗位置（X, Y 座標）
- 視窗尺寸（寬度 × 高度）

### 詳細信息面板

選中視窗後，右側會顯示詳細信息面板，包含：
- 完整的視窗信息
- 應用程式詳細資料
- 視窗屬性和狀態
- 快速操作按鈕

## 配置選項

### 預覽設置

在預覽區域的設置菜單中，您可以調整：

**顯示選項**:
- ☑️ 顯示視窗標籤
- ☑️ 顯示重疊指示器
- ☑️ 按應用程式分組
- ☑️ 啟用動畫效果

**性能選項**:
- 每個視圖的最大視窗數（默認：50）
- 啟用虛擬化渲染（大量視窗時自動啟用）
- 渲染品質設置

**無障礙選項**:
- 高對比度模式
- 減少動畫
- 增大點擊區域
- 螢幕閱讀器優化

### 自動優化

系統會根據視窗數量自動調整配置：

- **少於15個視窗**: 使用縮放模式，啟用所有視覺效果
- **15-30個視窗**: 使用縮放模式，啟用重疊指示器
- **30個以上視窗**: 自動切換到網格模式，關閉部分動畫以提升性能

## 性能優化

### 大量視窗處理

當處理大量視窗時，系統會自動：
- 啟用虛擬化渲染，只渲染可見區域
- 使用批次更新減少重繪
- 快取計算結果提升響應速度
- 在必要時降級到簡化顯示模式

### 性能監控

在開發者模式下，您可以查看性能指標：
- 幀率和渲染時間
- 記憶體使用情況
- 快取命中率
- 虛擬化效率

## 故障排除

### 常見問題

**Q: 預覽區域顯示空白**
A: 檢查 Profile 是否包含視窗數據，嘗試重新載入或切換預覽模式。

**Q: 視窗重疊嚴重，無法清楚查看**
A: 切換到網格模式或列表模式，或啟用重疊指示器。

**Q: 預覽載入緩慢**
A: 對於大量視窗，系統需要時間計算佈局。可以嘗試減少每個視圖的最大視窗數。

**Q: 某些視窗無法選擇**
A: 檢查視窗數據是否有效，或嘗試在不同預覽模式下選擇。

### 性能問題

**如果遇到性能問題**:
1. 減少同時顯示的視窗數量
2. 關閉不必要的視覺效果
3. 使用列表模式而非縮放模式
4. 啟用虛擬化渲染

### 無障礙問題

**如果無障礙功能不正常**:
1. 檢查系統無障礙設置
2. 確保 VoiceOver 或其他輔助技術已啟用
3. 嘗試使用鍵盤導航
4. 啟用高對比度模式

## 鍵盤快捷鍵

| 快捷鍵 | 功能 |
|--------|------|
| `1-4` | 切換預覽模式（1=縮放，2=網格，3=列表，4=小地圖）|
| `方向鍵` | 在視窗間導航 |
| `空格鍵` | 切換多選模式 |
| `回車鍵` | 選擇當前焦點視窗 |
| `Esc` | 取消選擇 |
| `Cmd+A` | 全選視窗 |
| `Cmd+D` | 取消全選 |
| `Tab` | 切換焦點區域 |
| `Cmd+F` | 搜索視窗（列表模式） |
| `Cmd+G` | 切換分組顯示 |

## 提示和技巧

### 高效使用技巧

1. **根據視窗數量選擇模式**: 少量視窗用縮放模式，大量視窗用網格或列表模式
2. **利用搜索功能**: 在列表模式下使用搜索快速定位視窗
3. **使用多選操作**: 批次選擇相似視窗進行統一操作
4. **善用懸停提示**: 快速查看視窗信息而無需選擇
5. **自定義配置**: 根據個人偏好調整顯示選項

### 工作流程建議

1. **初次查看**: 使用縮放模式了解整體佈局
2. **詳細檢查**: 切換到網格模式逐一檢查視窗
3. **快速定位**: 使用列表模式搜索特定視窗
4. **多螢幕分析**: 使用小地圖模式分析螢幕分佈

## 更新和反饋

### 功能更新

系統會定期更新，新功能包括：
- 更多預覽模式
- 增強的搜索和篩選
- 更好的性能優化
- 新的交互方式

### 反饋渠道

如果您有任何建議或遇到問題，請通過以下方式反饋：
- 應用程式內的反饋功能
- GitHub Issues
- 用戶社群論壇

---

*本指南會隨著功能更新而持續更新。最後更新時間：2025年1月*