# 視窗預覽系統 - 開發者文檔

## 架構概述

視窗預覽系統採用模組化、高性能的架構設計，支持大規模視窗佈局的實時預覽和交互。系統核心基於 SwiftUI 構建，整合了多種性能優化技術和無障礙功能。

## 核心架構設計

### 1. 分層架構

```
┌─────────────────────────────────────────┐
│           Presentation Layer            │
│  LayoutPreviewContainer, PreviewModes   │
├─────────────────────────────────────────┤
│            Business Layer               │
│   PreviewStateManager, ConfigManager   │
├─────────────────────────────────────────┤
│           Performance Layer             │
│ VirtualizationManager, BatchUpdateMgr  │
├─────────────────────────────────────────┤
│             Data Layer                  │
│   PreviewModels, WindowLayout, Profile │
└─────────────────────────────────────────┘
```

### 2. 組件關係圖

```mermaid
graph TB
    A[LayoutPreviewContainer] --> B[PreviewModeSelector]
    A --> C[ScaledPreviewMode]
    A --> D[GridPreviewMode]
    A --> E[ListPreviewMode]
    A --> F[MiniMapPreviewMode]
    A --> G[WindowDetailPanel]
    
    A --> H[PreviewStateManager]
    A --> I[PreviewConfigurationManager]
    A --> J[VirtualizationManager]
    A --> K[BatchUpdateManager]
    A --> L[RenderCacheManager]
    A --> M[PerformanceMonitor]
    
    H --> N[PreviewModels]
    I --> N
    J --> N
```

## 核心組件詳解

### 1. LayoutPreviewContainer

**職責**: 主容器組件，統一管理所有預覽功能

**關鍵設計決策**:
- 使用 `@StateObject` 管理性能優化組件的生命週期
- 實現雙重初始化器支持新舊集成方式
- 採用錯誤狀態機制處理異常情況

**核心實現**:

```swift
struct LayoutPreviewContainer: View {
    // 外部綁定（用於集成）
    @Binding var selectedWindow: WindowLayout?
    @Binding var selectedWindows: Set<UUID>
    let onSelectionChange: ((WindowLayout?) -> Void)?
    
    // 內部狀態管理
    @StateObject private var previewState = PreviewStateManager()
    @StateObject private var configManager = PreviewConfigurationManager.shared
    
    // 性能優化組件
    @StateObject private var virtualizationManager = VirtualizationManager()
    @StateObject private var batchUpdateManager = BatchUpdateManager()
    @StateObject private var cacheManager = RenderCacheManager()
    @StateObject private var performanceMonitor = PerformanceMonitor()
}
```

**擴展點**:
- 新增預覽模式：在 `virtualizedPreviewContent` 方法中添加新的 case
- 自定義錯誤處理：擴展 `PreviewErrorState` 枚舉
- 性能優化：在 `setupPerformanceOptimizations` 中添加新的優化邏輯

### 2. PreviewStateManager

**職責**: 管理預覽狀態和模式切換

**設計模式**: Observer Pattern + State Machine

```swift
class PreviewStateManager: ObservableObject {
    @Published var currentMode: PreviewMode = .scaled
    @Published var isTransitioning: Bool = false
    @Published var transitionProgress: Double = 0.0
    
    private var stateHistory: [PreviewMode] = []
    private var transitionAnimator: ViewTransitionAnimator?
    
    func switchMode(to newMode: PreviewMode, animated: Bool = true) {
        // 狀態驗證和轉換邏輯
    }
}
```

**狀態轉換規則**:
- 任何模式都可以直接切換到其他模式
- 切換過程中阻止新的切換請求
- 保持切換歷史用於回退功能

### 3. 性能優化架構

#### VirtualizationManager

**核心算法**: 視窗虛擬化渲染

```swift
struct VirtualizationConfig {
    let bufferSize: Int        // 緩衝區大小
    let itemHeight: CGFloat    // 項目高度
    let itemWidth: CGFloat     // 項目寬度
    let enableVirtualization: Bool  // 是否啟用虛擬化
}

class VirtualizationManager: ObservableObject {
    func updateVisibleRange(
        viewportSize: CGSize,
        scrollOffset: CGPoint,
        totalItems: Int,
        mode: PreviewMode
    ) {
        // 計算可見範圍
        let visibleRect = CGRect(
            origin: scrollOffset,
            size: viewportSize
        )
        
        // 根據模式計算項目佈局
        let itemLayout = calculateItemLayout(for: mode)
        
        // 確定可見項目索引範圍
        let visibleRange = calculateVisibleRange(
            visibleRect: visibleRect,
            itemLayout: itemLayout,
            totalItems: totalItems
        )
        
        // 更新虛擬化狀態
        updateVirtualizedItems(range: visibleRange)
    }
}
```

#### BatchUpdateManager

**核心算法**: 批次狀態更新和合併

```swift
enum UpdateType: String, CaseIterable {
    case windowSelection = "windowSelection"
    case windowLayout = "windowLayout"
    case previewMode = "previewMode"
    case configuration = "configuration"
    case visibility = "visibility"
    
    var priority: Int {
        // 優先級定義：0 = 最高，4 = 最低
    }
}

class BatchUpdateManager: ObservableObject {
    private var pendingUpdates: [BatchUpdate] = []
    private let config: BatchConfig
    
    func addUpdate(type: UpdateType, data: Any, completion: (() -> Void)?) {
        // 合併相同類型的更新
        if config.enableCoalescing {
            coalesceUpdate(BatchUpdate(type: type, data: data, completion: completion))
        } else {
            pendingUpdates.append(BatchUpdate(type: type, data: data, completion: completion))
        }
        
        // 檢查是否需要立即處理
        if shouldProcessImmediately(type) {
            processBatch()
        }
    }
}
```

## 預覽模式實現

### 1. 模式接口設計

所有預覽模式都遵循統一的接口：

```swift
protocol PreviewModeProtocol: View {
    var windows: [WindowLayout] { get }
    var canvasSize: CGSize { get }
    var selectedWindow: Binding<WindowLayout?> { get }
    var selectedWindows: Binding<Set<UUID>> { get }
    var configuration: PreviewConfiguration { get }
    
    func handleWindowSelection(_ window: WindowLayout)
    func handleWindowHover(_ window: WindowLayout?, at position: CGPoint)
    func calculateLayout() -> [WindowDisplayInfo]
}
```

### 2. ScaledPreviewMode 實現

**核心算法**: 智能重疊檢測和佈局優化

```swift
struct ScaledPreviewMode: View, PreviewModeProtocol {
    func calculateLayout() -> [WindowDisplayInfo] {
        // 1. 計算縮放比例
        let scaleFactor = calculateOptimalScale()
        
        // 2. 檢測重疊
        let overlapInfo = OverlapResolver.detectOverlaps(windows: windows)
        
        // 3. 調整佈局避免重疊
        let adjustedLayout = OverlapResolver.resolveOverlaps(
            windows: windows,
            overlapInfo: overlapInfo,
            canvasSize: canvasSize,
            scaleFactor: scaleFactor
        )
        
        return adjustedLayout
    }
    
    private func calculateOptimalScale() -> CGFloat {
        // 計算包含所有視窗的最小邊界
        let totalBounds = calculateTotalBounds(windows: windows)
        
        // 計算適合畫布的縮放比例
        let scaleX = canvasSize.width / totalBounds.width
        let scaleY = canvasSize.height / totalBounds.height
        
        return min(scaleX, scaleY, 1.0) // 不放大，只縮小
    }
}
```

### 3. GridPreviewMode 實現

**核心算法**: 自適應網格佈局

```swift
struct GridPreviewMode: View, PreviewModeProtocol {
    func calculateGridLayout() -> GridConfiguration {
        let itemSize = CGSize(width: 160, height: 120)
        let spacing: CGFloat = 8
        let padding: CGFloat = 16
        
        let availableWidth = canvasSize.width - padding * 2
        let columnsPerRow = max(1, Int(availableWidth / (itemSize.width + spacing)))
        
        let totalRows = (windows.count + columnsPerRow - 1) / columnsPerRow
        let totalHeight = CGFloat(totalRows) * (itemSize.height + spacing) + padding * 2
        
        return GridConfiguration(
            columns: columnsPerRow,
            rows: totalRows,
            itemSize: itemSize,
            spacing: spacing,
            totalHeight: totalHeight
        )
    }
}
```

## 數據模型設計

### 1. 核心數據結構

```swift
// 預覽模式枚舉
enum PreviewMode: String, CaseIterable, Identifiable, Codable {
    case scaled, grid, list, miniMap
    
    // 提供豐富的元數據
    var displayName: String { /* 實現 */ }
    var icon: String { /* SF Symbols 圖標 */ }
    var description: String { /* 詳細描述 */ }
}

// 預覽配置
struct PreviewConfiguration: Codable, Equatable {
    var mode: PreviewMode
    var showLabels: Bool
    var showOverlapIndicators: Bool
    var groupByApplication: Bool
    var maxWindowsPerView: Int
    var enableAnimations: Bool
    var showWindowDetails: Bool
    
    // 智能配置生成
    static func optimized(for windowCount: Int) -> PreviewConfiguration
}

// 視窗顯示信息
struct WindowDisplayInfo: Identifiable, Equatable {
    let window: WindowLayout
    let displayFrame: CGRect
    let isOverlapping: Bool
    let overlapLevel: Int
    let isVisible: Bool
    let opacity: Double
    let zIndex: Int
    
    // 實用計算方法
    func isVisible(in bounds: CGRect) -> Bool
    func overlapArea(with other: WindowDisplayInfo) -> CGFloat
}
```

### 2. 數據流設計

```
User Input → BatchUpdateManager → StateManager → View Update
     ↓              ↓                   ↓            ↓
Configuration → CacheManager → VirtualizationManager → Render
```

## 性能優化策略

### 1. 渲染優化

**虛擬化渲染**:
- 只渲染可見區域的視窗
- 使用 `LazyVGrid` 和 `LazyVStack` 進行延遲載入
- 實現自定義的 `VirtualizedScrollView`

**快取策略**:
- 快取計算結果（佈局、重疊檢測）
- 快取渲染內容（視窗縮略圖）
- 使用 LRU 算法管理快取

**批次更新**:
- 合併相同類型的狀態更新
- 按優先級處理更新
- 使用防抖動技術減少不必要的重繪

### 2. 記憶體優化

```swift
class MemoryOptimizer {
    // 監控記憶體使用
    func monitorMemoryUsage() -> MemoryMetrics
    
    // 清理不必要的資源
    func cleanupResources()
    
    // 調整快取大小
    func adjustCacheSize(based metrics: MemoryMetrics)
}
```

### 3. 性能監控

```swift
struct PerformanceMetrics {
    let frameRate: Double
    let renderingTime: TimeInterval
    let memoryUsage: UInt64
    let cacheHitRate: Double
    let virtualizationEfficiency: Double
    
    var isPerformanceOptimal: Bool {
        return frameRate >= 55.0 && 
               renderingTime <= 0.016 && 
               memoryUsage <= 100_000_000
    }
}
```

## 錯誤處理和恢復

### 1. 錯誤分類

```swift
enum PreviewError: Error, LocalizedError {
    case dataValidationFailed(String)
    case renderingTimeout
    case memoryLimitExceeded
    case invalidConfiguration(String)
    
    var errorDescription: String? {
        switch self {
        case .dataValidationFailed(let details):
            return "數據驗證失敗: \(details)"
        case .renderingTimeout:
            return "渲染超時，請嘗試減少視窗數量"
        case .memoryLimitExceeded:
            return "記憶體使用超限，已切換到簡化模式"
        case .invalidConfiguration(let details):
            return "配置無效: \(details)"
        }
    }
}
```

### 2. 恢復策略

```swift
class ErrorRecoveryManager {
    func handleError(_ error: PreviewError) -> RecoveryAction {
        switch error {
        case .renderingTimeout:
            return .switchToSimplifiedMode
        case .memoryLimitExceeded:
            return .enableVirtualization
        case .dataValidationFailed:
            return .sanitizeData
        case .invalidConfiguration:
            return .resetToDefault
        }
    }
}
```

## 測試策略

### 1. 單元測試

```swift
class PreviewModeTests: XCTestCase {
    func testScaledModeLayout() {
        // 測試縮放模式的佈局計算
        let windows = createTestWindows(count: 10)
        let mode = ScaledPreviewMode(windows: windows, canvasSize: CGSize(width: 800, height: 600))
        let layout = mode.calculateLayout()
        
        XCTAssertEqual(layout.count, windows.count)
        XCTAssertTrue(layout.allSatisfy { $0.displayFrame.width > 0 && $0.displayFrame.height > 0 })
    }
    
    func testOverlapDetection() {
        // 測試重疊檢測算法
        let overlappingWindows = createOverlappingWindows()
        let overlapInfo = OverlapResolver.detectOverlaps(windows: overlappingWindows)
        
        XCTAssertTrue(overlapInfo.hasOverlaps)
        XCTAssertGreaterThan(overlapInfo.overlapCount, 0)
    }
}
```

### 2. 性能測試

```swift
class PerformanceTests: XCTestCase {
    func testLargeDatasetRendering() {
        measure {
            let windows = createTestWindows(count: 1000)
            let container = LayoutPreviewContainer(profile: Profile(windows: windows))
            // 測量渲染時間
        }
    }
    
    func testMemoryUsage() {
        let initialMemory = getMemoryUsage()
        
        // 創建大量視窗
        let windows = createTestWindows(count: 500)
        let container = LayoutPreviewContainer(profile: Profile(windows: windows))
        
        let finalMemory = getMemoryUsage()
        let memoryIncrease = finalMemory - initialMemory
        
        XCTAssertLessThan(memoryIncrease, 50_000_000) // 50MB 限制
    }
}
```

### 3. UI 測試

```swift
class UITests: XCTestCase {
    func testModeSwitch() {
        let app = XCUIApplication()
        app.launch()
        
        // 測試模式切換
        app.buttons["網格預覽"].tap()
        XCTAssertTrue(app.otherElements["GridPreviewMode"].exists)
        
        app.buttons["列表預覽"].tap()
        XCTAssertTrue(app.otherElements["ListPreviewMode"].exists)
    }
    
    func testWindowSelection() {
        let app = XCUIApplication()
        app.launch()
        
        // 測試視窗選擇
        let firstWindow = app.otherElements["WindowItem"].firstMatch
        firstWindow.tap()
        
        XCTAssertTrue(firstWindow.isSelected)
        XCTAssertTrue(app.otherElements["WindowDetailPanel"].exists)
    }
}
```

## 擴展指南

### 1. 添加新的預覽模式

**步驟**:
1. 在 `PreviewMode` 枚舉中添加新案例
2. 創建對應的視圖組件，實現 `PreviewModeProtocol`
3. 在 `LayoutPreviewContainer` 中添加切換邏輯
4. 更新配置管理器
5. 添加相應的測試

**示例**:
```swift
// 1. 添加枚舉案例
enum PreviewMode {
    case scaled, grid, list, miniMap, timeline // 新增 timeline
}

// 2. 創建視圖組件
struct TimelinePreviewMode: View, PreviewModeProtocol {
    // 實現時間軸預覽邏輯
}

// 3. 在容器中添加切換邏輯
switch previewState.currentMode {
case .timeline:
    TimelinePreviewMode(/* 參數 */)
}
```

### 2. 自定義性能優化

**創建自定義優化器**:
```swift
class CustomPerformanceOptimizer: PerformanceOptimizer {
    override func optimizeForLargeDataset(_ windows: [WindowLayout]) -> OptimizationResult {
        // 自定義優化邏輯
        return super.optimizeForLargeDataset(windows)
    }
}

// 註冊自定義優化器
PerformanceOptimizerRegistry.register(CustomPerformanceOptimizer.self, for: .largeDataset)
```

### 3. 擴展配置選項

```swift
extension PreviewConfiguration {
    var customRenderingOptions: CustomRenderingOptions {
        get { /* 從 UserDefaults 讀取 */ }
        set { /* 保存到 UserDefaults */ }
    }
}

struct CustomRenderingOptions: Codable {
    var enableShadows: Bool = true
    var cornerRadius: CGFloat = 8.0
    var animationDuration: TimeInterval = 0.3
}
```

## 部署和維護

### 1. 版本兼容性

**向後兼容策略**:
- 保持舊的初始化器
- 使用 `@available` 標記新功能
- 提供遷移指南

```swift
@available(macOS 12.0, *)
extension LayoutPreviewContainer {
    // 新功能只在支持的系統版本上可用
}
```

### 2. 性能監控

**生產環境監控**:
```swift
class ProductionPerformanceMonitor {
    func reportMetrics() {
        let metrics = PerformanceMonitor.shared.currentMetrics
        
        // 發送到分析服務
        AnalyticsService.shared.track("preview_performance", parameters: [
            "frame_rate": metrics.frameRate,
            "memory_usage": metrics.memoryUsage,
            "window_count": metrics.windowCount
        ])
    }
}
```

### 3. 錯誤報告

```swift
class ErrorReporter {
    func reportError(_ error: PreviewError, context: ErrorContext) {
        let report = ErrorReport(
            error: error,
            context: context,
            systemInfo: SystemInfo.current,
            performanceMetrics: PerformanceMonitor.shared.currentMetrics
        )
        
        CrashReporter.shared.report(report)
    }
}
```

## 最佳實踐

### 1. 代碼組織

- 使用 MVVM 架構分離關注點
- 採用協議導向編程提高可測試性
- 使用依賴注入管理組件關係

### 2. 性能優化

- 優先使用 SwiftUI 的內建優化（LazyVStack, LazyVGrid）
- 避免在 body 中進行複雜計算
- 使用 `@State` 和 `@StateObject` 正確管理狀態

### 3. 用戶體驗

- 提供載入狀態指示
- 實現優雅的錯誤處理
- 支持鍵盤導航和無障礙功能

### 4. 維護性

- 編寫全面的文檔
- 保持高測試覆蓋率
- 使用語義化版本控制

---

*本文檔會隨著系統演進持續更新。如有疑問或建議，請聯繫開發團隊。*